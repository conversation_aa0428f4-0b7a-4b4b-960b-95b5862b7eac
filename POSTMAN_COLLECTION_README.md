# EllumAI Backend API Postman Collection

This comprehensive Postman collection provides complete API documentation and testing capabilities for the EllumAI backend microservices architecture.

## 📁 Collection Contents

### 🔐 Authentication Service
- **User Management**: Registration, login, OAuth, password management
- **Organization Management**: Create, update, manage organizations and members
- **Roles & Permissions**: Role-based access control and permission management
- **User Profile**: Profile management and user settings

### 💬 Chat Service
- **Chat Conversations**: Start, continue, and manage AI chat sessions
- **File Management**: Upload and manage files for chat context
- **Knowledge Base**: Add business information for personalized responses

### 🚀 FastBot Service
- **Advanced Chat**: Enhanced AI chat with web search and file analysis
- **File Operations**: Upload, manage, and analyze documents
- **Web Scraping**: Extract content from URLs for AI processing
- **Social Media Content**: Content repurposing for different platforms

### 📱 Socials Service
- **Account Management**: Connect and manage social media accounts
- **Instagram**: Post creation, comments, analytics, and engagement
- **Facebook**: Page management, posting, comment handling
- **Twitter**: Tweet creation, analytics, and engagement tracking
- **LinkedIn**: Professional content posting and management
- **🔄 Unified Comments**: Platform-agnostic comment management with normalized data

### ⚙️ Settings & Payment Service
- **General Settings**: User preferences and profile settings
- **Notification Settings**: Email, push, and SMS notification preferences
- **Privacy Settings**: Data sharing and privacy controls
- **Payment & Billing**: Subscription management and billing information

### 🧠 Deep Ellum Service
- **Custom Agents**: Create and manage specialized AI agents
- **Agent Conversations**: Chat with custom agents for specific use cases
- **Agent Templates**: Pre-built agent templates for common scenarios

## 🚀 Quick Start

### 1. Import the Collection
1. Open Postman
2. Click "Import" button
3. Select `ellumAI_backend_postman_collection.json`
4. The collection will be imported with all endpoints and examples

### 2. Set Up Environment
Choose one of the provided environments:

**Development Environment:**
- Import `ellumAI_development_environment.json`
- Uses localhost with default ports
- Includes test data for quick testing

**Production Environment:**
- Import `ellumAI_production_environment.json`
- Uses production URLs
- Requires real credentials

### 3. Authentication Flow
1. **Register a new user** or **Login** using the Authentication Service
2. The collection automatically extracts and stores the `accessToken`
3. All subsequent requests use this token automatically
4. The `organizationId` is also auto-extracted for multi-tenant requests

## 🔧 Configuration

### Environment Variables
The collection uses these key variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `baseUrl` | Base URL for all services | `http://localhost` |
| `authServicePort` | Authentication service port | `7777` |
| `chatServicePort` | Chat service port | `8000` |
| `fastbotServicePort` | FastBot service port | `8001` |
| `socialsServicePort` | Socials service port | `8006` |
| `settingsServicePort` | Settings service port | `8005` |
| `deepEllumServicePort` | Deep Ellum service port | `8007` |
| `accessToken` | JWT access token | Auto-extracted |
| `organizationId` | Organization ID | Auto-extracted |

### Auto-Scripts
The collection includes pre-request and test scripts that:
- Automatically add organization headers
- Extract and store tokens from login responses
- Set up authentication for all requests

## 📋 Usage Examples

### Basic Authentication Flow
```
1. POST /api/v1/auth/register - Register new user
2. POST /api/v1/auth/login - Login and get token
3. Use any authenticated endpoint
```

### Social Media Workflow
```
1. POST /api/v1/socials/instagram/initialise - Connect Instagram
2. POST /api/v1/socials/instagram/create - Create post
3. GET /api/v1/socials/comments/unified/{id} - Get unified comments
```

### AI Chat Workflow
```
1. POST /api/v1/chat/start-chat - Start conversation
2. POST /api/v1/chat/continue-chat - Continue conversation
3. GET /api/v1/chat/chats - Get chat history
```

## 🔄 Unified Comments System

The collection includes comprehensive examples of the new unified comments system:

### Key Features
- **Platform Agnostic**: Single API for Instagram and Facebook comments
- **Normalized Data**: Consistent structure across all platforms
- **Rich Metadata**: Includes reactions, media, attachments, and replies
- **Filtering**: Filter by platform, pagination, and include/exclude replies

### Example Endpoints
```
GET /api/v1/socials/comments/unified/{id}
GET /api/v1/socials/comments/unified/{id}/replies/{commentId}
GET /api/v1/socials/comments/platforms/summary/{id}
```

### Response Format
The unified format includes:
- Author information (username, name, profile picture)
- Content and timestamps
- Platform identification
- Engagement metrics (likes, replies)
- Media attachments (Instagram) and attachments (Facebook)
- Reactions and detailed interaction data

## 🧪 Testing

### Response Examples
Each endpoint includes:
- **Success responses** with realistic sample data
- **Error responses** for common failure scenarios
- **Pagination examples** where applicable
- **Platform-specific vs unified** response comparisons

### Test Data
The development environment includes test IDs for:
- Scheduled content
- Comments and replies
- Chat threads
- Files and agents
- Organizations and users

## 🔒 Security

### Authentication
- Bearer token authentication for all protected endpoints
- Automatic token refresh handling
- Organization-based multi-tenancy

### Headers
Required headers are automatically added:
- `Authorization: Bearer {{accessToken}}`
- `organization-id: {{organizationId}}`
- `Content-Type: application/json`

## 📊 Error Handling

The collection includes examples of common error responses:

| Status Code | Description | Example |
|-------------|-------------|---------|
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Missing or invalid token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource doesn't exist |
| 422 | Validation Error | Invalid input parameters |
| 500 | Server Error | Internal server error |

## 🔄 Updates and Maintenance

### Version Information
- **Collection Version**: 1.0.0
- **API Version**: v1
- **Last Updated**: January 2025

### Changelog
- Initial release with all microservices
- Unified comments system integration
- Comprehensive authentication flow
- Multi-environment support

## 🆘 Support

### Common Issues
1. **Token Expiration**: Use the refresh token endpoint
2. **CORS Errors**: Ensure proper environment configuration
3. **Missing Organization**: Check organization-id header
4. **Rate Limiting**: Implement appropriate delays between requests

### Documentation
- Each endpoint includes detailed descriptions
- Parameter explanations and validation rules
- Usage examples and common patterns
- Response schema documentation

## 🚀 Advanced Features

### Batch Operations
Some endpoints support batch operations for efficiency:
- Bulk comment retrieval
- Multiple file uploads
- Batch social media posting

### Webhooks
The collection includes webhook endpoints for:
- Social media platform events
- Payment status changes
- User activity notifications

### Analytics
Comprehensive analytics endpoints for:
- Social media performance
- User engagement metrics
- Content performance tracking
- Platform comparison data

---

**Ready to explore the EllumAI API?** Import the collection and start testing! 🚀
