import ast
import json
import os
import tempfile
from typing import Dict, List, Optional, Set

# import google.generativeai as genai
from google import genai
from google.genai.types import Tool, GenerateContentConfig, GoogleSearch, FileData
from pydantic import BaseModel


from app.core.config import settings
from app.utils.logger import get_logger
from fastapi import HTTPException
from vertexai.language_models import TextEmbeddingInput, TextEmbeddingModel

# List of MIME types supported by Gemini
# Based on documentation and testing
SUPPORTED_MIME_TYPES: Set[str] = {
    # Images
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/heic",
    "image/heif",

    # Audio
    "audio/mpeg",
    "audio/mp3",
    "audio/mp4",
    "audio/wav",
    "audio/wave",
    "audio/x-wav",
    "audio/webm",
    "audio/ogg",

    # Video
    "video/mp4",
    "video/mpeg",
    "video/webm",
    "video/quicktime",

    # Documents
    "application/pdf",
    "text/plain",
    "text/markdown",
    "text/html",
    "text/csv",

    # Archives (for code)
    "application/zip",
    "application/x-zip-compressed",
    "application/gzip",
    "application/x-gzip",

    # Default type (for testing)
    "application/octet-stream",
}

def is_mime_type_supported(mime_type: str) -> bool:
    """
    Check if a MIME type is supported by Gemini.

    Parameters:
      - mime_type: The MIME type to check

    Returns:
      - True if the MIME type is supported, False otherwise
    """
    return mime_type in SUPPORTED_MIME_TYPES

logger = get_logger(__name__)

# Configure the Gemini client using the API key from the environment.
# genai.configure(api_key=settings.GEMINI_API_KEY)
client = genai.Client(api_key=settings.GEMINI_API_KEY)

async def upload_file_to_gemini(file_content: bytes, file_name: str, mime_type: str = None) -> Optional[FileData]:
    """
    Upload a file to Gemini using the Files API.

    Parameters:
      - file_content: The binary content of the file
      - file_name: The name of the file
      - mime_type: The MIME type of the file (optional)

    Returns:
      - FileData object if successful, None otherwise

    Raises:
      - HTTPException if the MIME type is not supported
    """
    # If mime_type is not provided, try to guess it from the file name
    if not mime_type:
        import mimetypes
        mime_type = mimetypes.guess_type(file_name)[0] or "application/octet-stream"

    # Check if the MIME type is supported
    if not is_mime_type_supported(mime_type):
        error_msg = f"MIME type '{mime_type}' is not supported by Gemini. Please upload a file with a supported MIME type."
        logger.error(error_msg)
        raise HTTPException(status_code=400, detail=error_msg)

    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file_name)[1]) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Upload the file to Gemini
            # logger.info(f"Uploading file {file_name} to Gemini with MIME type {mime_type}")

            # According to the documentation, we should use the file parameter with a path
            uploaded_file = client.files.upload(file=temp_file_path)

            # logger.info(f"File uploaded successfully to Gemini with ID: {uploaded_file.name}")
            return uploaded_file
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error uploading file to Gemini: {e}")
        return None


def generate_reply(prompt, model="gemini-1.5-flash", web_search=False, files=None):
    """
    Generate content using Gemini.

    Parameters:
      - prompt: a string prompt that includes the conversation context.
      - model: model name (default: "gemini-1.5-flash")
      - web_search: whether to use web search (default: False)
      - files: list of FileData objects to include in the prompt (default: None)

    Returns:
      - The generated text as a string.
    """
    try:
        contents = []

        # Add text prompt
        if isinstance(prompt, str):
            contents.append({"text": prompt})
        elif isinstance(prompt, list):
            contents = prompt

        # Add files if provided
        if files and isinstance(files, list):
            for file in files:
                if file:
                    contents.append(file)

        if not web_search:
            response = client.models.generate_content(
                model=model,
                contents=contents
            )
            return response.text.strip()
        else:
            google_search_tool = Tool(
                google_search = GoogleSearch()
            )

            response = client.models.generate_content(
                model=model,
                contents=contents,
                config=GenerateContentConfig(
                    tools=[google_search_tool],
                    response_modalities=["TEXT"],
                )
            )

            for each in response.candidates[0].content.parts:
                return each.text.strip

    except Exception as e:
        logger.error("Error generating reply with Gemini: %s", e)
        return "I'm sorry, I'm having trouble generating a response right now."


async def get_title_and_summary(chunk: str, url: str) -> Dict[str, str]:
    """
    Asynchronously extract title and summary from a text chunk using Google's Gemini model.
    """
    system_prompt = (
        "You are an AI that extracts titles and summaries from documentation chunks. "
        "Return a JSON object with 'title' and 'summary' keys. "
        "For the title: If this is the start of a document, extract its title. "
        "If it's a middle chunk, derive a descriptive title. "
        "For the summary: Create a concise summary of the main points in this chunk. "
        "Keep both concise but informative."
    )

    try:
        response = client.models.generate_content(
            model='gemini-2.0-flash',
            contents=
            [
                {"text": system_prompt},
                {"text": f"URL: {url}\n\nContent:\n{chunk[:1000]}..."},
            ]
        )

        # Extract and parse response text
        content = response.text.strip()
        return (
            json.loads(content)
            if content.startswith("{")
            else {"title": "N/A", "summary": content}
        )

    except Exception as e:
        logger.error("Error getting title and summary with Gemini: %s", e)
        return {
            "title": "Error processing title",
            "summary": "Error processing summary",
        }


def generate_title(prompt, model="gemini-1.5-flash"):
    """
    Generate a thread title using Gemini.

    Parameters:
      - prompt: A string prompt providing context for title generation.
      - model: The model name to use (default: "gemini-1.5-flash").

    Returns:
      - The generated title as a string.
    """
    try:
        response = client.models.generate_content(model=model, contents=prompt)
        return response.text.strip()
    except Exception as e:
        logger.error("Error generating title with Gemini: %s", e)
        return "Untitled Thread"


def generate_summary(text: str, model: str = "gemini-1.5-flash") -> str:
    """
    Generate a summary of the provided text using the Gemini model.

    Parameters:
      - text: The text to summarize.
      - model: The Gemini model to use (default: "gemini-1.5-flash").

    Returns:
      - The generated summary as a string.
    """
    try:
        response = client.models.generate_content(model=model, contents=text)
        return response.text.strip()
    except Exception as e:
        logger.error("Error generating summary with Gemini: %s", e)
        return "Summary not available."


# Set model name and dimensionality
MODEL_NAME = "multilingual-e5"  # "text-embedding-005"
DIMENSIONALITY = 1536  # Set dimensionality to 1536


async def get_embedding(text: str) -> list[float]:
    """Asynchronously get an embedding vector from Google's embedding model with 1536 dimensions."""
    try:
        result = client.models.embed_content(
            model="text-embedding-004",
            contents=text
        )
        # logger.info(result.embeddings)
        # Extract the embedding values from the first ContentEmbedding object
        if result.embeddings and hasattr(result.embeddings[0], 'values'):
            return result.embeddings[0].values
        else:
            return [0.0] * DIMENSIONALITY  # Fallback in case no embeddings are returned
    except Exception as e:
        logger.error("Error getting embedding with Gemini: %s", e)
        return [0.0] * DIMENSIONALITY  # Return a zero vector on error


async def get_embedding_multilingual_e5(text: str) -> list[float]:
    """Asynchronously get an embedding vector from the Multilingual E5 model with 1536 dimensions."""
    try:
        # Initialize the Multilingual E5 model
        model = TextEmbeddingModel.from_pretrained(MODEL_NAME)

        # Create the input for embedding
        input_text = [TextEmbeddingInput(text, task="TEXT_EMBEDDING")]

        # Get embeddings with the specified dimensionality
        embeddings = model.get_embeddings(
            input_text, output_dimensionality=DIMENSIONALITY
        )

        # Extract the embedding values from the response
        return embeddings[
            0
        ].values  # Assuming the response returns embeddings as a list of embeddings
    except Exception as e:
        logger.error("Error getting embedding with Multilingual E5: %s", e)
        return [0.0] * DIMENSIONALITY  # Return a zero vector on error


def find_similar_competitors(
    business_description: str, competitor_names: list[str], model: str = "gemini-1.5-flash",

) -> list:
    """
    Generate a list of competitor website links for a business given its description
    and a list of known competitor names using the Gemini model.

    The response must be strictly a list. For example:
    ['https://apple.com','https://ethereum.org','https://ripple.com']
    """
    # Validate inputs.
    if not business_description or not isinstance(competitor_names, list):
        raise ValueError(
            "Invalid input: business_description must be a string and competitor_names must be a list."
        )

    # Construct the prompt to instruct Gemini.
    system_prompt = (
        "Please generate a list response (strictly list) listing competitor website links for "
        f"a business that fits the following description: {business_description}. "
        f"The known competitors are {', '.join(competitor_names)}. "
        "No additional text or explanations should be included. "
        "For example: ['https://apple.com','https://ethereum.org','https://ripple.com']"
    )

    try:

        # Generate the output using the Gemini model.
        response = client.models.generate_content(model=model, contents=system_prompt)
        output = response.text.strip()
        # logger.info("Gemini model response: %s", output)

        # Parse the output to a Python list.
        competitor_list = ast.literal_eval(output)

        # Ensure the output is a list.
        if isinstance(competitor_list, list):
            return competitor_list
        else:
            raise ValueError("Response is not a valid list")

    except ValueError as ve:
        logger.error("ValueError in find_similar_competitors: %s", ve)
        raise HTTPException(
            status_code=400, detail="Invalid response format from the model."
        )
    except SyntaxError as se:
        logger.error("SyntaxError in find_similar_competitors: %s", se)
        raise HTTPException(
            status_code=400, detail="Error parsing the model's response."
        )
    except HTTPException as he:
        logger.error("HTTPException in find_similar_competitors: %s", he)
        raise he
    except Exception as e:
        logger.error("Unexpected error in find_similar_competitors: %s", e)
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while finding similar competitors.",
        )

def analyse_prompt_image_generation(prompt: str, model: str = "gemini-1.5-flash") -> str:
    """
    Analyze a user prompt to determine if it requests image generation.

    Parameters:
      - prompt: The user's message to analyze.
      - model: The Gemini model to use (default: "gemini-1.5-flash").

    Returns:
      - The analysis result as a string containing 'yes $ellum' or 'no $ellum'.
    """
    try:
        # Enhanced prompt for better image detection
        analysis_prompt = f"""You are an AI assistant that determines if a user message is requesting image generation or visual content creation.

Analyze this user message: "{prompt}"

Look for requests to:
- Generate, create, make, draw, or produce images
- Create visual content like pictures, photos, illustrations, artwork
- Draw, sketch, paint, or design something visual
- Make logos, diagrams, charts, or visual representations
- Create any visual media

Examples of image requests:
- "Generate an image of a dog"
- "Create a picture of a sunset"
- "Draw me a logo"
- "Make an illustration of..."
- "Show me what X looks like"
- "Can you create a visual of..."

Examples of NON-image requests:
- "Tell me about dogs"
- "Explain how to draw"
- "What is the best camera?"
- "Describe a sunset"

If this is clearly requesting image generation or visual content creation, respond with exactly: "yes $ellum"
If this is NOT requesting image generation, respond with exactly: "no $ellum"

Response:"""

        response = client.models.generate_content(
            model=model,
            contents=analysis_prompt
        )

        result = response.text.strip()
        # logger.info(f"Image analysis for '{prompt[:50]}...': {result}")
        return result

    except Exception as e:
        logger.error("Error analysing prompt with Gemini: %s", e)
        return "no $ellum"  # Default to no image generation on error


class CompetitorSocialHandles(BaseModel):
    facebook: List[str]
    twitter: List[str]
    instagram: List[str]

def get_competitor_social_handles(competitor_name:str, model_id: str = "gemini-2.0-flash") -> CompetitorSocialHandles:
    """
    Uses the Gemini API with Google Search to find and return a competitor's social handles.
    """
    model_id = "gemini-2.0-flash"

    prompt = f"""
    Using Google Search, find the verified social media handles for the competitor named "{competitor_name}".
    Specifically, provide the Facebook, Twitter, and Instagram handles.
    Format your response in JSON using the following schema:

    {{
        "facebook": list[str],
        "twitter": list[str],
        "instagram": list[str]
    }}

    Only return the JSON without any additional commentary.
    """

    response = client.models.generate_content(
        model=model_id,
        contents=prompt,
        config={
            'response_mime_type': 'application/json',
            'response_schema': CompetitorSocialHandles,
        },
    )
    return response.parsed