import json
import os
from typing import Dict, List
from app.utils.logger import get_logger
from openai import AsyncOpenAI
from app.utils.s3_utils import upload_generated_image_to_s3
from dotenv import load_dotenv
import tracemalloc
tracemalloc.start()

load_dotenv()

logger = get_logger(__name__)

client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))


async def get_title_and_summary(chunk: str, url: str) -> Dict[str, str]:
    """
    Asynchronously extract title and summary from a text chunk using OpenAI.
    Uses a system prompt to instruct the LLM.
    """
    system_prompt = (
        "You are an AI that extracts titles and summaries from documentation chunks. "
        "Return a JSON object with 'title' and 'summary' keys. "
        "For the title: If this seems like the start of a document, extract its title. "
        "If it's a middle chunk, derive a descriptive title. "
        "For the summary: Create a concise summary of the main points in this chunk. "
        "Keep both concise but informative."
    )

    try:
        response = await client.chat.completions.create(
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": f"URL: {url}\n\nContent:\n{chunk[:1000]}...",
                },
            ],
        )
        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error("Error getting title and summary: %s", e)
        return {
            "title": "Error processing title",
            "summary": "Error processing summary",
        }


async def get_embedding(text: str) -> List[float]:
    """Asynchronously get an embedding vector from OpenAI."""
    try:
        response = await client.embeddings.create(
            model="text-embedding-3-small", input=text
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error("Error getting embedding: %s", e)
        return [0.0] * 1536  # Adjust VECTOR_DIM if necessary


async def generate_image(prompt: str, organization_id: str = "default"):
    """
    Generates an image using the OpenAI DALL-E API and uploads it to S3.

    Args:
        prompt (str): The prompt for image generation
        organization_id (str): Organization ID for S3 folder structure

    Returns:
        str: The S3 URL of the uploaded image, or None if failed
    """
    try:
        # Generate the image using OpenAI DALL-E
        # logger.info(f"Generating image with prompt: {prompt[:100]}...")

        # Properly await the OpenAI API call
        images_response = await client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            size="1024x1024",
            quality="standard",
            n=1
        )

        # logger.info(f"OpenAI API response received. Data length: {len(images_response.data) if images_response.data else 0}")

        # Extract the URL from the first image in the data list
        if images_response.data and len(images_response.data) > 0:
            openai_image_url = images_response.data[0].url
            # logger.info(f"Image generated successfully from OpenAI: {openai_image_url}")

            # Upload the image to S3
            try:
                # logger.info(f"Starting S3 upload for organization: {organization_id}")
                # logger.info(f"OpenAI image URL length: {len(openai_image_url)}")

                s3_url = await upload_generated_image_to_s3(
                    openai_image_url,
                    organization_id,
                    custom_filename=f"dalle_image_{hash(prompt)}"
                )
                # logger.info(f"Image uploaded to S3 successfully: {s3_url}")
                return s3_url
            except Exception as s3_error:
                logger.error(f"S3 upload failed: {str(s3_error)}")
                logger.error(f"S3 error type: {type(s3_error).__name__}")
                import traceback
                logger.error(f"S3 upload traceback: {traceback.format_exc()}")
                # logger.info("Falling back to OpenAI URL due to S3 upload failure")
                # Fallback to OpenAI URL if S3 upload fails
                return openai_image_url
        else:
            logger.error("No image data received from OpenAI API response")
            raise ValueError("OpenAI API returned empty response - no image data received")

    except Exception as e:
        logger.error(f"Error generating image: {str(e)}")
        # Re-raise with more specific error information
        if "insufficient_quota" in str(e).lower():
            raise ValueError("OpenAI API quota exceeded - please check your billing and usage limits")
        elif "invalid_request_error" in str(e).lower():
            raise ValueError(f"Invalid request to OpenAI API: {str(e)}")
        elif "rate_limit" in str(e).lower():
            raise ValueError("OpenAI API rate limit exceeded - please try again later")
        elif "authentication" in str(e).lower():
            raise ValueError("OpenAI API authentication failed - please check your API key")
        else:
            raise ValueError(f"OpenAI image generation failed: {str(e)}")

   