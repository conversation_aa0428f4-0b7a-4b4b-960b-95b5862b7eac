"""
Multi-Database Connection Manager for Agentic Database Queries
Manages connections to all microservice databases for admin queries.
"""

import asyncio
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from app.core.config import settings
from app.utils.logger import get_logger
import httpx

logger = get_logger(__name__)


class MultiDatabaseManager:
    """Manages connections to all microservice databases."""
    
    def __init__(self):
        self.engines = {}
        self.session_makers = {}
        self._initialize_engines()
    
    def _initialize_engines(self):
        """Initialize database engines for all microservices."""
        
        # FastBot Database (current service)
        fastbot_url = (
            settings.PROD_DATABASE_URL
            if settings.ENV == "PRODUCTION"
            else settings.LOCAL_DATABASE_URL
        )
        
        # Database URLs for other microservices
        # These should be added to config or fetched from environment
        database_configs = {
            'fastbot': fastbot_url,
            'socials': self._get_socials_db_url(),
            'authentication': self._get_auth_db_url(),
            'settings_payment': self._get_settings_payment_db_url()
        }
        
        for service_name, db_url in database_configs.items():
            if db_url:
                try:
                    engine = create_async_engine(
                        db_url,
                        future=True,
                        echo=False,
                        pool_pre_ping=True,
                        pool_recycle=1800,
                        pool_timeout=30,
                        pool_size=5,
                        max_overflow=10,
                    )
                    self.engines[service_name] = engine
                    
                    session_maker = sessionmaker(
                        bind=engine,
                        class_=AsyncSession,
                        expire_on_commit=False,
                    )
                    self.session_makers[service_name] = session_maker
                    
                    # logger.info(f"Initialized database connection for {service_name}")
                except Exception as e:
                    logger.error(f"Failed to initialize {service_name} database: {e}")
    
    def _get_socials_db_url(self) -> Optional[str]:
        """Get socials service database URL."""
        # This should be configured in environment variables
        # For now, using the same database with different schema or service discovery
        return getattr(settings, 'SOCIALS_DATABASE_URL', None)
    
    def _get_auth_db_url(self) -> Optional[str]:
        """Get authentication service database URL."""
        return getattr(settings, 'AUTH_DATABASE_URL', None)

    def _get_settings_payment_db_url(self) -> Optional[str]:
        """Get settings and payment service database URL."""
        return getattr(settings, 'SETTINGS_PAYMENT_DATABASE_URL', None)
    
    async def get_session(self, service_name: str) -> AsyncSession:
        """Get database session for a specific service."""
        if service_name not in self.session_makers:
            raise ValueError(f"No database connection for service: {service_name}")
        
        session_maker = self.session_makers[service_name]
        return session_maker()
    
    async def execute_query(self, service_name: str, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute a query on a specific service database."""
        try:
            async with await self.get_session(service_name) as session:
                result = await session.execute(text(query), params or {})
                rows = result.fetchall()
                
                # Convert rows to dictionaries
                columns = result.keys()
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Error executing query on {service_name}: {e}")
            raise
    
    async def get_socials_metrics(self, organization_id: str) -> Dict[str, Any]:
        """Get social media metrics for an organization."""
        try:
            # Facebook metrics
            facebook_query = """
                SELECT platform, username, login_status, 
                       COUNT(posts.id) as total_posts
                FROM social_media_accounts sma
                LEFT JOIN posts ON sma.id = posts.social_media_account_id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'facebook'
                GROUP BY sma.platform, sma.username, sma.login_status
            """
            
            # Instagram metrics
            instagram_query = """
                SELECT platform, username, login_status,
                       COUNT(posts.id) as total_posts
                FROM social_media_accounts sma
                LEFT JOIN posts ON sma.id = posts.social_media_account_id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'instagram'
                GROUP BY sma.platform, sma.username, sma.login_status
            """
            
            # Twitter metrics
            twitter_query = """
                SELECT platform, username, login_status,
                       COUNT(posts.id) as total_posts
                FROM social_media_accounts sma
                LEFT JOIN posts ON sma.id = posts.social_media_account_id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'twitter'
                GROUP BY sma.platform, sma.username, sma.login_status
            """
            
            params = {"org_id": organization_id}
            
            facebook_data = await self.execute_query('socials', facebook_query, params)
            instagram_data = await self.execute_query('socials', instagram_query, params)
            twitter_data = await self.execute_query('socials', twitter_query, params)
            
            return {
                'facebook': facebook_data,
                'instagram': instagram_data,
                'twitter': twitter_data
            }
            
        except Exception as e:
            logger.error(f"Error getting social metrics: {e}")
            return {}
    
    async def get_user_analytics(self, organization_id: str) -> Dict[str, Any]:
        """Get user analytics for an organization."""
        try:
            query = """
                SELECT COUNT(*) as total_users,
                       COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
                       COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_users
                FROM users u
                JOIN user_organisation_roles uor ON u.id = uor.user_id
                JOIN organisations o ON uor.organisation_id = o.id
                WHERE o.id = :org_id
            """
            
            params = {"org_id": organization_id}
            result = await self.execute_query('authentication', query, params)
            
            return result[0] if result else {}
            
        except Exception as e:
            logger.error(f"Error getting user analytics: {e}")
            return {}
    
    async def get_chat_analytics(self, organization_id: str) -> Dict[str, Any]:
        """Get chat analytics for an organization."""
        try:
            query = """
                SELECT COUNT(DISTINCT ct.id) as total_threads,
                       COUNT(cm.id) as total_messages,
                       AVG(
                           CASE WHEN ct.updated_at > ct.created_at 
                           THEN EXTRACT(EPOCH FROM (ct.updated_at - ct.created_at))/60 
                           ELSE 0 END
                       ) as avg_session_duration_minutes
                FROM chat_threads ct
                LEFT JOIN chat_messages cm ON ct.id = cm.thread_id
                WHERE ct.organization_id = :org_id
                AND ct.created_at >= NOW() - INTERVAL '30 days'
            """
            
            params = {"org_id": organization_id}
            result = await self.execute_query('fastbot', query, params)
            
            return result[0] if result else {}
            
        except Exception as e:
            logger.error(f"Error getting chat analytics: {e}")
            return {}
    
    async def close_all_connections(self):
        """Close all database connections."""
        for service_name, engine in self.engines.items():
            try:
                await engine.dispose()
                # logger.info(f"Closed database connection for {service_name}")
            except Exception as e:
                logger.error(f"Error closing {service_name} database: {e}")


# Global instance
multi_db_manager = MultiDatabaseManager()


async def get_multi_db_manager() -> MultiDatabaseManager:
    """Dependency to get the multi-database manager."""
    return multi_db_manager
