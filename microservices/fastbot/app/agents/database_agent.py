"""
LangGraph Database Agent for Cross-Service Analytics
Main agent that orchestrates database queries across all microservices:
- socials: Social media accounts and posts
- authentication: Users, organizations, roles
- fastbot: Chat threads, messages, files
- settings_payment: Application settings and payment/billing data
"""

import asyncio
from typing import Dict, Any, List, Optional
from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.tools import BaseTool
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel
from app.database.multi_db_manager import MultiDatabaseManager, get_multi_db_manager
from app.utils.logger import get_logger
from app.gen_models.openai_model import client as openai_client
import json
import os

# Import all database tools
from app.agents.tools.socials_tools import (
    FacebookMetricsTool, InstagramMetricsTool, TwitterMetricsTool, SocialOverviewTool
)
from app.agents.tools.auth_tools import (
    OrganizationInfoTool, UserMetricsTool, PermissionsOverviewTool, UserProfilesTool
)
from app.agents.tools.fastbot_tools import (
    ChatMetricsTool, FileUploadMetricsTool, KnowledgeBaseMetricsTool, UserEngagementTool
)
from app.agents.tools.settings_tools import (
    ApplicationSettingsTool, NotificationSettingsTool, DataStorageSettingsTool,
    UsageMetricsTool, SessionActivityTool
)
from app.agents.tools.payment_tools import (
    SubscriptionStatusTool, PaymentHistoryTool, BillingAnalyticsTool,
    InvoiceStatusTool, SubscriptionUsageTool
)

logger = get_logger(__name__)


class AgentState(BaseModel):
    """State for the database agent."""
    messages: List[BaseMessage]
    organization_id: str
    user_role: str
    user_id: str
    admin_context: str
    next_action: Optional[str] = None


class DatabaseAgent:
    """LangGraph agent for cross-database analytics queries."""
    
    def __init__(self, db_manager: MultiDatabaseManager):
        self.db_manager = db_manager
        self.tools = self._initialize_tools()
        self.graph = self._build_graph()
    
    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize all database query tools."""
        tools = []
        
        # Social media tools
        tools.extend([
            FacebookMetricsTool(self.db_manager),
            InstagramMetricsTool(self.db_manager),
            TwitterMetricsTool(self.db_manager),
            SocialOverviewTool(self.db_manager)
        ])
        
        # Authentication and user tools
        tools.extend([
            OrganizationInfoTool(self.db_manager),
            UserMetricsTool(self.db_manager),
            PermissionsOverviewTool(self.db_manager),
            UserProfilesTool(self.db_manager)
        ])
        
        # FastBot analytics tools
        tools.extend([
            ChatMetricsTool(self.db_manager),
            FileUploadMetricsTool(self.db_manager),
            KnowledgeBaseMetricsTool(self.db_manager),
            UserEngagementTool(self.db_manager)
        ])
        
        # Settings and configuration tools
        tools.extend([
            ApplicationSettingsTool(self.db_manager),
            NotificationSettingsTool(self.db_manager),
            DataStorageSettingsTool(self.db_manager),
            UsageMetricsTool(self.db_manager),
            SessionActivityTool(self.db_manager)
        ])
        
        # Payment and subscription tools
        tools.extend([
            SubscriptionStatusTool(self.db_manager),
            PaymentHistoryTool(self.db_manager),
            BillingAnalyticsTool(self.db_manager),
            InvoiceStatusTool(self.db_manager),
            SubscriptionUsageTool(self.db_manager)
        ])
        
        # logger.info(f"Initialized {len(tools)} database query tools")
        return tools
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""
        
        # Create the state graph
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("tools", ToolNode(self.tools))
        
        # Set entry point
        workflow.set_entry_point("agent")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "continue": "tools",
                "end": END,
            }
        )
        
        # Add edge from tools back to agent
        workflow.add_edge("tools", "agent")
        
        return workflow.compile()
    
    async def _agent_node(self, state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
        """Main agent reasoning node."""
        try:
            # Get the last message
            last_message = state.messages[-1] if state.messages else None
            
            if not last_message:
                return {
                    "messages": [AIMessage(content="I need a question to help you with analytics.")],
                    "next_action": "end"
                }
            
            # Create system prompt with admin context
            system_prompt = f"""
You are an advanced analytics AI assistant with access to comprehensive database tools across all microservices.

{state.admin_context}

Available Tools:
- Social Media Analytics: Facebook, Instagram, Twitter metrics and performance data
- User & Organization Analytics: User metrics, roles, permissions, and organization information
- Chat & AI Analytics: Conversation metrics, file uploads, knowledge base usage
- Settings & Payment Analytics: Application settings, notifications, storage, billing, subscription status, payment history, and usage limits

Guidelines:
1. Always provide specific, actionable insights
2. Use multiple tools when needed to give comprehensive answers
3. Include relevant metrics, trends, and recommendations
4. Be concise but thorough in your analysis
5. Highlight any issues or opportunities you discover
6. Format responses clearly with headers and bullet points

Current Context:
- Organization ID: {state.organization_id}
- User Role: {state.user_role}
- User ID: {state.user_id}

User Question: {last_message.content}
"""
            
            # Prepare messages for OpenAI
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Add conversation history
            for msg in state.messages[-5:]:  # Last 5 messages for context
                if isinstance(msg, HumanMessage):
                    messages.append({"role": "user", "content": msg.content})
                elif isinstance(msg, AIMessage):
                    messages.append({"role": "assistant", "content": msg.content})
            
            # Get response from OpenAI with tool calling
            response = await openai_client.chat.completions.create(
                model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
                messages=messages,
                tools=[{
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.args_schema.schema() if hasattr(tool.args_schema, 'schema') else {}
                    }
                } for tool in self.tools],
                tool_choice="auto",
                temperature=0.1
            )
            
            response_message = response.choices[0].message
            
            # Check if tools should be called
            if response_message.tool_calls:
                # Return tool calls for execution
                return {
                    "messages": [AIMessage(
                        content=response_message.content or "",
                        tool_calls=[{
                            "id": tc.id,
                            "function": {
                                "name": tc.function.name,
                                "arguments": tc.function.arguments
                            },
                            "type": tc.type
                        } for tc in response_message.tool_calls]
                    )],
                    "next_action": "continue"
                }
            else:
                # Return final response
                return {
                    "messages": [AIMessage(content=response_message.content)],
                    "next_action": "end"
                }
                
        except Exception as e:
            logger.error(f"Error in agent node: {e}")
            return {
                "messages": [AIMessage(content=f"I encountered an error while processing your request: {str(e)}")],
                "next_action": "end"
            }
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine if the agent should continue or end."""
        return state.next_action if state.next_action else "end"
    
    async def query(
        self,
        question: str,
        organization_id: str,
        user_role: str,
        user_id: str,
        admin_context: str
    ) -> str:
        """
        Process a database analytics query.
        
        Args:
            question: The user's question
            organization_id: Organization ID for scoping queries
            user_role: User's role (admin/owner)
            user_id: User ID
            admin_context: Context about admin privileges
        
        Returns:
            str: The agent's response with analytics
        """
        try:
            # Create initial state
            initial_state = AgentState(
                messages=[HumanMessage(content=question)],
                organization_id=organization_id,
                user_role=user_role,
                user_id=user_id,
                admin_context=admin_context
            )
            
            # Run the graph
            result = await self.graph.ainvoke(initial_state)
            
            # Extract the final response
            final_messages = result.get("messages", [])
            if final_messages:
                last_message = final_messages[-1]
                if isinstance(last_message, AIMessage):
                    return last_message.content
            
            return "I wasn't able to generate a response. Please try rephrasing your question."
            
        except Exception as e:
            logger.error(f"Error in database agent query: {e}")
            return f"I encountered an error while processing your analytics request: {str(e)}"


# Global agent instance
_database_agent: Optional[DatabaseAgent] = None


async def get_database_agent() -> DatabaseAgent:
    """Get or create the global database agent instance."""
    global _database_agent
    
    if _database_agent is None:
        db_manager = await get_multi_db_manager()
        _database_agent = DatabaseAgent(db_manager)
        # logger.info("Database agent initialized")
    
    return _database_agent


async def process_admin_query(
    question: str,
    organization_id: str,
    user_role: str,
    user_id: str,
    admin_context: str
) -> str:
    """
    Process an admin analytics query using the database agent.
    
    Args:
        question: The admin's question
        organization_id: Organization ID
        user_role: User's role (admin/owner)
        user_id: User ID
        admin_context: Admin context information
    
    Returns:
        str: Analytics response
    """
    try:
        agent = await get_database_agent()
        response = await agent.query(
            question=question,
            organization_id=organization_id,
            user_role=user_role,
            user_id=user_id,
            admin_context=admin_context
        )
        
        # logger.info(f"Processed admin query for organization {organization_id}")
        return response
        
    except Exception as e:
        logger.error(f"Error processing admin query: {e}")
        return f"I encountered an error while processing your analytics request. Please try again or contact support if the issue persists."
