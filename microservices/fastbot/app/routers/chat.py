import asyncio
import json
import mimetypes
import uuid
from datetime import datetime, timedelta
from io import BytesIO
from typing import Annotated, Dict, List, Optional, Any

from fastapi.responses import StreamingResponse
import redis.asyncio as redis
from app.core.config import settings
from app.database.database import get_db_session
from app.gen_models.gemini_model import (
    analyse_prompt_image_generation,
    generate_title,
    upload_file_to_gemini,
    client as gemini_client
)
from app.models.models import Chat<PERSON>essage, ChatThread, GeminiFile
from app.models.schema import (
    ChatRequest,
    ChatResponse,
    DeleteThreadResponse,
    FileUploadResponse,
    MessageResponse,
    ThreadHistoryResponse,
    TrialChatRequest,
    TrialChatResponse,
    UpdateThreadTitleRequest,
    UpdateThreadTitleResponse
)
from pydantic import BaseModel
from app.utils.dependency import check_permissions, get_current_user
from app.utils.external_calls import (
    get_organisation_info_cached,
    get_user_info_cached,
    verify_organization,
)
from app.utils.admin_middleware import check_agentic_permissions, get_admin_context, verify_admin_access
from app.agents.database_agent import process_admin_query
from app.utils.logger import get_logger
from app.utils.qdrant_utils import get_knowledge_from_kb
# Removed complex RAG imports - using simple RAG system
from app.utils.success_response import fail_response, success_response
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.gen_models.openai_model import generate_image, client as openai_client
import tracemalloc
tracemalloc.start()

MODEL_PROVIDER, REDIS_URL = (settings.MODEL_PROVIDER, settings.REDIS_URL)

router = APIRouter()
logger = get_logger(__name__)

# Initialize Redis client with error handling
try:
    # logger.info(f"Initializing Redis client with URL: {REDIS_URL}")
    redis_client = redis.from_url(REDIS_URL, encoding="utf8", decode_responses=True)
    # logger.info("Redis client initialized successfully")
except Exception as e:
    logger.error(f"Error initializing Redis client: {str(e)}")
    # Create a dummy Redis client that won't crash the app
    class DummyRedisClient:
        async def get(self, key):
            logger.warning(f"Dummy Redis GET operation for key: {key}")
            return None

        async def set(self, key, value, ex=None):
            logger.warning(f"Dummy Redis SET operation for key: {key}, value: {value}, expiry: {ex}")
            return True

        async def delete(self, key):
            logger.warning(f"Dummy Redis DELETE operation for key: {key}")
            return True

    redis_client = DummyRedisClient()
    # logger.info("Using dummy Redis client as fallback")

STREAM_RESPONSE = True

if MODEL_PROVIDER.lower() == "openai":
    from app.gen_models.openai_model import generate_reply as generate_reply_openai
elif MODEL_PROVIDER.lower() == "gemini":
    from app.gen_models.gemini_model import generate_reply as generate_reply_gemini
elif MODEL_PROVIDER.lower() == "deepseek":
    from app.gen_models.deepseek_model import generate_reply as generate_reply_deepseek
else:
    raise ValueError("Invalid MODEL_PROVIDER specified in the .env file.")


async def analyze_admin_query(message: str) -> Dict[str, Any]:
    """
    Use LLM to intelligently detect and categorize admin analytics queries.
    Returns detailed analysis including whether it's an analytics query and what type.
    """
    try:
        analysis_prompt = f"""
You are an AI assistant that analyzes user messages to determine if they're requesting business analytics or database queries.

Analyze this user message and provide a JSON response with the following structure:
{{
    "is_analytics_query": true/false,
    "confidence": 0.0-1.0,
    "query_type": "social_media|user_analytics|financial|system_usage|general_analytics|not_analytics",
    "specific_request": "brief description of what they're asking for",
    "suggested_databases": ["socials", "authentication", "fastbot", "settings_payment"]
}}

Categories:
- social_media: Facebook, Instagram, Twitter metrics, posts, engagement
- user_analytics: User counts, roles, activity, organization data
- financial: Revenue, billing, payments, subscriptions, invoices
- system_usage: Storage, sessions, app usage, performance
- general_analytics: Mixed or broad analytics requests
- not_analytics: Regular chat, questions not requiring database queries

User message: "{message}"

Examples:
"How many users do we have?" -> {{"is_analytics_query": true, "confidence": 0.95, "query_type": "user_analytics", "specific_request": "total user count", "suggested_databases": ["authentication"]}}

"Show me Facebook metrics" -> {{"is_analytics_query": true, "confidence": 0.98, "query_type": "social_media", "specific_request": "Facebook performance data", "suggested_databases": ["socials"]}}

"Hello, how are you?" -> {{"is_analytics_query": false, "confidence": 0.99, "query_type": "not_analytics", "specific_request": "greeting", "suggested_databases": []}}

"What's our revenue this month?" -> {{"is_analytics_query": true, "confidence": 0.96, "query_type": "financial", "specific_request": "monthly revenue data", "suggested_databases": ["settings_payment"]}}

Respond with ONLY the JSON object:"""

        # Use the OpenAI client for analysis
        import os
        response = await openai_client.chat.completions.create(
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
            messages=[{"role": "user", "content": analysis_prompt}],
            temperature=0.1,
            max_tokens=200
        )

        result_text = response.choices[0].message.content.strip()
        # logger.info(f"LLM analytics analysis for '{message[:50]}...': {result_text}")

        # Parse JSON response
        import json
        try:
            analysis = json.loads(result_text)
            return analysis
        except json.JSONDecodeError:
            logger.error(f"Failed to parse LLM response as JSON: {result_text}")
            # Return default analysis
            return {
                "is_analytics_query": False,
                "confidence": 0.5,
                "query_type": "not_analytics",
                "specific_request": "unknown",
                "suggested_databases": []
            }

    except Exception as e:
        logger.error(f"Error in LLM analytics analysis: {e}")
        # Fallback to keyword-based detection
        analytics_keywords = [
            "metrics", "analytics", "performance", "statistics", "stats", "data",
            "facebook", "instagram", "twitter", "social media", "posts", "engagement",
            "users", "subscribers", "followers", "likes", "comments", "shares",
            "revenue", "billing", "payments", "subscription", "invoices",
            "storage", "usage", "activity", "sessions", "logins",
            "how many", "how much", "total", "average", "count", "sum",
            "dashboard", "report", "overview", "summary", "trends"
        ]

        message_lower = message.lower()
        is_analytics = any(keyword in message_lower for keyword in analytics_keywords)

        return {
            "is_analytics_query": is_analytics,
            "confidence": 0.7 if is_analytics else 0.3,
            "query_type": "general_analytics" if is_analytics else "not_analytics",
            "specific_request": message[:100],
            "suggested_databases": ["socials", "authentication", "fastbot", "settings_payment"] if is_analytics else []
        }


async def is_admin_analytics_query(message: str) -> bool:
    """
    Simple wrapper for backward compatibility.
    """
    analysis = await analyze_admin_query(message)
    return analysis.get("is_analytics_query", False)


async def extract_image_prompt(user_message: str, max_length: int = 3500) -> str:
    """
    Extract a clean, concise prompt for image generation from the user's message.
    Removes any system context and keeps only the essential image description.

    Args:
        user_message (str): The user's original message
        max_length (int): Maximum length for the image prompt (DALL-E limit is 4000)

    Returns:
        str: Clean image prompt suitable for DALL-E
    """
    try:
        # Use OpenAI to extract and clean the image prompt
        extraction_prompt = f"""
Extract a clean, concise image generation prompt from this user message.
Remove any conversational elements and focus only on the visual description.
Keep it under {max_length} characters and make it suitable for DALL-E image generation.

User message: "{user_message}"

Return only the cleaned image prompt, nothing else:"""

        response = await openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": extraction_prompt}],
            temperature=0.1,
            max_tokens=200
        )

        cleaned_prompt = response.choices[0].message.content.strip()

        # Ensure it's within the length limit
        if len(cleaned_prompt) > max_length:
            cleaned_prompt = cleaned_prompt[:max_length].rsplit(' ', 1)[0]  # Cut at word boundary

        # logger.info(f"Extracted image prompt: '{cleaned_prompt}' (length: {len(cleaned_prompt)})")
        return cleaned_prompt

    except Exception as e:
        logger.error(f"Error extracting image prompt: {e}")
        # Fallback: use the original message but truncate it
        fallback_prompt = user_message[:max_length] if len(user_message) > max_length else user_message
        # logger.info(f"Using fallback image prompt: '{fallback_prompt}' (length: {len(fallback_prompt)})")
        return fallback_prompt


async def trim_chat_history(history: List[Dict[str, any]], max_messages: int = 10, max_total_length: int = 2000) -> List[Dict[str, any]]:
    """
    Trim chat history to keep only recent messages and stay within length limits.

    Args:
        history (List[Dict]): Chat history
        max_messages (int): Maximum number of messages to keep
        max_total_length (int): Maximum total character length

    Returns:
        List[Dict]: Trimmed history
    """
    if not history:
        return []

    # Keep system message if it exists
    system_messages = [msg for msg in history if msg.get("role") == "system"]
    other_messages = [msg for msg in history if msg.get("role") != "system"]

    # Take the most recent messages
    recent_messages = other_messages[-max_messages:] if len(other_messages) > max_messages else other_messages

    # Calculate total length and trim if necessary
    total_length = sum(len(msg.get("content", msg.get("message", ""))) for msg in recent_messages)

    if total_length > max_total_length:
        # Remove older messages until we're under the limit
        while recent_messages and total_length > max_total_length:
            removed_msg = recent_messages.pop(0)
            total_length -= len(removed_msg.get("content", removed_msg.get("message", "")))

    # Combine system messages with trimmed history
    trimmed_history = system_messages + recent_messages

    # logger.info(f"Trimmed history from {len(history)} to {len(trimmed_history)} messages, total length: {total_length}")
    return trimmed_history


async def vectorize_chat_summary(history: List[Dict[str, any]]) -> str:
    """
    Create a vectorized summary of chat history for better context management.

    Args:
        history (List[Dict]): Chat history

    Returns:
        str: Summarized context
    """
    if not history or len(history) <= 3:
        return ""

    try:
        # Extract non-system messages for summarization
        chat_messages = [msg for msg in history if msg.get("role") != "system"]

        if len(chat_messages) <= 2:
            return ""

        # Create a conversation summary
        conversation_text = ""
        for msg in chat_messages[-10:]:  # Last 10 messages
            role = msg.get("role", "unknown")
            content = msg.get("content", msg.get("message", ""))
            conversation_text += f"{role}: {content}\n"

        # Use OpenAI to create a concise summary
        summary_prompt = f"""
Summarize this conversation in 2-3 sentences, focusing on the main topics and context:

{conversation_text}

Summary:"""

        response = await openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": summary_prompt}],
            temperature=0.1,
            max_tokens=150
        )

        summary = response.choices[0].message.content.strip()
        # logger.info(f"Generated chat summary: {summary}")
        return summary

    except Exception as e:
        logger.error(f"Error creating chat summary: {e}")
        return ""


# REMOVED: filter_relevant_knowledge function
# This function was filtering out relevant documents with restrictive score thresholds
# Now using direct retrieval without filtering for better document access


async def handle_admin_query(
    message: str,
    organization_id: str,
    token: dict,
    db: AsyncSession
) -> Optional[str]:
    """
    Handle admin analytics queries using the database agent.
    Returns the agent response if it's an admin query, None otherwise.
    """
    try:
        # Analyze the query with detailed LLM analysis
        analysis = await analyze_admin_query(message)

        # Check if this is an analytics query with sufficient confidence
        if not analysis.get("is_analytics_query", False) or analysis.get("confidence", 0) < 0.7:
            # logger.info(f"Query not identified as analytics: confidence={analysis.get('confidence', 0)}, type={analysis.get('query_type', 'unknown')}")
            return None

        # Check if user has admin permissions
        has_admin_access = await check_agentic_permissions(organization_id, token)
        user_id = token["decoded"].get("user_id")
        has_admin_access = await check_permissions(user_id, organization_id, "can delete knowledgebase")
        if not has_admin_access:
            logger.warning(f"User attempted analytics query without admin access: {message[:50]}...")
            return None

        # Get admin verification info
        admin_info = await verify_admin_access(organization_id, token)

        # Enhanced admin context with query analysis
        base_context = get_admin_context(admin_info)
        enhanced_context = f"""{base_context}

Query Analysis:
- Query Type: {analysis.get('query_type', 'general_analytics')}
- Specific Request: {analysis.get('specific_request', 'analytics data')}
- Suggested Databases: {', '.join(analysis.get('suggested_databases', []))}
- Confidence: {analysis.get('confidence', 0):.2f}

Focus your response on the specific type of analytics requested. Use the suggested databases to provide targeted insights.
"""

        # Process the query with the database agent
        response = await process_admin_query(
            question=message,
            organization_id=organization_id,
            user_role=admin_info["user_role"],
            user_id=admin_info["user_id"],
            admin_context=enhanced_context
        )

        # logger.info(f"Processed {analysis.get('query_type', 'general')} analytics query for user {admin_info['user_id']} with confidence {analysis.get('confidence', 0):.2f}")
        return response

    except Exception as e:
        logger.error(f"Error handling admin query: {e}")
        return None


async def create_new_thread(
    db: AsyncSession,
    title: str,
    organization_id: str,
    user_id: str,
    initial_message: str,
) -> str:
    try:
        thread = ChatThread(
            title=title,
            organization_id=organization_id,
            user_id=user_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        db.add(thread)
        await db.commit()
        await db.refresh(thread)
        await store_chat_message(db, thread.id, "user", initial_message)
        # logger.info("Created new chat thread %s for organization %s.", thread.id, organization_id)
        return thread.id
    except Exception as e:
        logger.error("Error generating thread title: %s", str(e))
        raise


async def store_chat_message(db: AsyncSession, thread_id: str, role: str, message: str, file_ids: List[str] = None):
    """
    Store chat message in database without cache invalidation.
    Removed Redis caching to prevent stale data issues.
    """
    try:
        # Create a ChatMessage with the current timestamp
        # Note: We're using a naive datetime here because the database expects it
        # The timezone handling is done at the database level
        msg = ChatMessage(
            thread_id=thread_id,
            role=role,
            message=message,
            file_ids=",".join(file_ids) if file_ids else None,
            timestamp=datetime.now(),
        )
        db.add(msg)
        await db.commit()
        # logger.info("Stored message for thread %s.", thread_id)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error("Error storing chat messages: %s\nTraceback: %s", str(e), error_traceback)
        raise


async def get_chat_history(db: AsyncSession, thread_id: str) -> List[Dict[str, any]]:
    """
    Get chat history from database without caching.
    Removed Redis caching to prevent stale data issues.
    """
    try:
        # logger.info("Retrieving chat history for thread %s from database.", thread_id)

        result = await db.execute(
            select(ChatMessage)
            .where(ChatMessage.thread_id == thread_id)
            .order_by(ChatMessage.timestamp)
        )
        messages = result.scalars().all()
        MAX_MESSAGES = 50
        trimmed = messages[-MAX_MESSAGES:] if len(messages) > MAX_MESSAGES else messages

        history = []
        for msg in trimmed:
            message_data = {"role": msg.role, "message": msg.message}
            if msg.file_ids:
                message_data["file_ids"] = msg.file_ids.split(",")
            history.append(message_data)

        # logger.info(f"Retrieved {len(history)} messages for thread {thread_id}")
        return history
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error("Error retrieving chat history: %s\nTraceback: %s", str(e), error_traceback)
        # Return an empty history instead of raising an exception
        return []


async def generate_chat_reply(history, user_message, file_ids=None):
    """
    Unified function to generate a reply based on conversation history and the latest user message.
    For OpenAI, we expect history to be a list of messages.
    For Gemini, we convert the history to a prompt string.

    Parameters:
      - history: The conversation history
      - user_message: The latest user message
      - file_ids: Optional list of file IDs to include in the prompt
    """
    try:
        if MODEL_PROVIDER.lower() == "openai":
            messages = []
            for entry in history:
                content = entry.get("content", entry.get("message", ""))
                messages.append({"role": entry["role"], "content": content})
            messages.append({"role": "user", "content": user_message})
            return await generate_reply_openai(messages)

        elif MODEL_PROVIDER.lower() == "gemini":
            # Get file objects if file_ids are provided
            files = []
            if file_ids:
                # Query the database for file information
                for file_id in file_ids:
                    try:
                        # Get the file from Gemini
                        file = gemini_client.files.get(name=file_id)
                        if file:
                            files.append(file)
                            # logger.info(f"Retrieved file from Gemini: {file_id}")
                    except Exception as e:
                        logger.error(f"Error retrieving file from Gemini: {e}")

            # Build the prompt
            prompt_parts = []

            # Add system and history messages
            prompt_text = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                prompt_text += f"{entry['role'].capitalize()}: {content}\n"

            # Add the latest user message
            prompt_text += f"User: {user_message}\nAssistant:"
            prompt_parts.append({"text": prompt_text})

            # If streaming, we need to handle files differently
            if STREAM_RESPONSE:
                # Note: In this branch, thread_id and thread_title are provided in the endpoint.
                return stream_gemini_reply(prompt_text, None, None, None, files=files)
            else:
                # Use the generate_reply_gemini function with files
                from app.gen_models.gemini_model import generate_reply as generate_reply_gemini
                return generate_reply_gemini(prompt_parts, files=files)

        elif MODEL_PROVIDER.lower() == "deepseek":
            messages = []
            for entry in history:
                content = entry.get("content", entry.get("message", ""))
                messages.append({"role": entry["role"], "content": content})
            messages.append({"role": "user", "content": user_message})
            return await asyncio.to_thread(generate_reply_deepseek, messages)

        else:
            raise ValueError(f"Unsupported MODEL_PROVIDER: {MODEL_PROVIDER}")
    except Exception as e:
        logger.error("Error generating a reply to chat: %s", str(e))
        raise HTTPException(status_code=500, detail="Error generating a reply to chat")


async def stream_gemini_reply(
    prompt: str, thread_id: str, thread_title: str, db: AsyncSession, web_search=False, files=None, is_trial=False, organization_id: str = "default"
):
    """
    Streams the Gemini reply text chunk-by-chunk.
    Sends metadata first (thread_id, thread_title), then streams chunks.
    After the stream is done, the full reply is stored in the database.

    The function retrieves chat history internally to provide proper context to the AI
    without exposing it in the response metadata.

    If image=True and not is_trial, the prompt is first analyzed for image suitability.
    If deemed suitable, an image is generated and streamed as a single chunk.

    Parameters:
      - prompt: The prompt text (will be enhanced with chat history)
      - thread_id: The thread ID
      - thread_title: The thread title
      - db: The database session
      - web_search: Whether to use web search
      - files: Optional list of file objects to include in the prompt
      - is_trial: Whether this is a trial chat (disables image generation)
      - organization_id: Organization ID for S3 folder structure
    """
    full_reply = ""


    # If the image flag is set and this is not a trial chat, analyze the prompt and attempt image generation.
    generate_image_flag = False
    if not is_trial:  # Disable image generation for trial chats
        try:
            # Extract the user message for analysis instead of the full prompt
            user_message_for_analysis = ""
            if "User:" in prompt:
                # Extract the last user message from the prompt
                user_parts = prompt.split("User:")
                if len(user_parts) > 1:
                    last_user_part = user_parts[-1].split("Assistant:")[0].strip()
                    user_message_for_analysis = last_user_part

            if not user_message_for_analysis:
                # Fallback: use a simple extraction from the prompt
                # Look for the actual user message in the prompt
                lines = prompt.split('\n')
                for line in lines:
                    if line.strip().startswith('User:'):
                        user_message_for_analysis = line.replace('User:', '').strip()
                        break

            # If we still don't have a user message, try keyword-based detection as fallback
            if not user_message_for_analysis:
                logger.warning("Could not extract user message for image analysis, using keyword fallback")
                # Use the original prompt for keyword detection
                image_keywords = [
                    "generate image", "create image", "make image", "draw", "picture",
                    "photo", "illustration", "artwork", "visual", "logo", "diagram",
                    "generate a", "create a", "make a", "show me", "draw me"
                ]
                prompt_lower = prompt.lower()
                generate_image_flag = any(keyword in prompt_lower for keyword in image_keywords)
                if generate_image_flag:
                    # logger.info("Image generation detected via keyword fallback")
            else:
                # logger.info(f"Analyzing user message for image generation: '{user_message_for_analysis[:100]}...'")
                gpt_response = analyse_prompt_image_generation(user_message_for_analysis)

                # Check for positive response
                if "yes $ellum" in gpt_response.lower():
                    generate_image_flag = True
                    # logger.info(f"Image generation APPROVED by Gemini: {gpt_response}")
                else:
                    # logger.info(f"Image generation REJECTED by Gemini: {gpt_response}")
                    # Additional keyword-based fallback if Gemini says no but keywords suggest yes
                    image_keywords = [
                        "generate image", "create image", "make image", "draw", "picture",
                        "photo", "illustration", "artwork", "visual", "logo"
                    ]
                    user_lower = user_message_for_analysis.lower()
                    keyword_detected = any(keyword in user_lower for keyword in image_keywords)
                    if keyword_detected:
                        # logger.info("Overriding Gemini decision with keyword detection")
                        generate_image_flag = True

        except Exception as e:
            logger.error(f"Failed to analyze prompt for image generation using Gemini: {str(e)}")
            # Fallback to keyword detection on error
            image_keywords = [
                "generate image", "create image", "make image", "draw", "picture",
                "photo", "illustration", "artwork", "visual", "logo"
            ]
            prompt_lower = prompt.lower()
            generate_image_flag = any(keyword in prompt_lower for keyword in image_keywords)
            if generate_image_flag:
                # logger.info("Image generation detected via keyword fallback after Gemini error")
    else:
        # logger.info("Image generation disabled for trial chat")

    # First, yield metadata as an SSE event (without chat history)
    # Chat history is used internally for AI context but not returned to client
    metadata = {
        "thread_id": thread_id,
        "title": thread_title,
        "type": "text" if not generate_image_flag else "image"
    }
    yield f"data: {json.dumps({'metadata': metadata})}\n\n"

    # Check if the prompt already contains conversation history
    # If it does (from continue chat), use it as-is. If not (from new chat), enhance it.
    enhanced_prompt = prompt

    if "Conversation so far:" in prompt or "Previous conversation:" in prompt:
        # Prompt already contains history from continue chat - use as-is
        # logger.info(f"Using pre-built prompt with history: {len(prompt)} characters")
        enhanced_prompt = prompt
    elif thread_id and db:
        # This is likely a new chat or the prompt doesn't have history - enhance it
        try:
            history = await get_chat_history(db, thread_id)
            if history and len(history) > 1:  # Only enhance if there's actual conversation history
                # logger.info(f"Retrieved {len(history)} messages for AI context enhancement")

                # Build enhanced prompt with chat history context
                conversation_context = "Previous conversation:\n"
                for msg in history[-10:]:  # Use last 10 messages for context
                    role = msg.get("role", "unknown")
                    message = msg.get("message", "")
                    conversation_context += f"{role.capitalize()}: {message}\n"

                # Extract the current user message from the original prompt
                current_message = prompt.split("User:")[-1].split("Assistant:")[0].strip() if "User:" in prompt else prompt

                # Build enhanced prompt with conversation context
                enhanced_prompt = f"{conversation_context}\nUser: {current_message}\nAssistant:"

                # logger.info(f"Enhanced prompt with chat history: {len(enhanced_prompt)} characters")
            else:
                # logger.info("No significant chat history found - using original prompt")
        except Exception as e:
            logger.error(f"Error retrieving chat history for AI context: {e}")
            # Continue with original prompt if history retrieval fails

    # Generate an image if the analysis indicates so
    if generate_image_flag:
        # logger.info("Image generation requested - starting process")
        try:
            # Extract clean image prompt from the original user message
            # We need to extract the user message from the prompt
            user_message = ""
            if "User:" in prompt:
                # Extract the last user message from the prompt
                user_parts = prompt.split("User:")
                if len(user_parts) > 1:
                    last_user_part = user_parts[-1].split("Assistant:")[0].strip()
                    user_message = last_user_part

            if not user_message:
                # Fallback: use the entire prompt but this shouldn't happen
                user_message = prompt

            # Extract a clean, concise image prompt
            clean_image_prompt = await extract_image_prompt(user_message)

            # logger.info(f"Original prompt length: {len(prompt)}, Clean image prompt length: {len(clean_image_prompt)}")
            # logger.info(f"Calling generate_image with organization_id: {organization_id}")

            # Use the clean prompt for image generation
            bot_response = await generate_image(clean_image_prompt, organization_id)

            # If bot_response is still a coroutine, await it again
            if asyncio.iscoroutine(bot_response):
                bot_response = await bot_response

            # Check if bot_response has a 'data' attribute (e.g., from a library like aiohttp)
            if hasattr(bot_response, "data"):
                bot_response = bot_response.data

            # Validate the response format
            if not isinstance(bot_response, (str, bytes)):
                logger.error(f"Unexpected response format from generate_image: {type(bot_response)}")
                raise ValueError(f"Unexpected response format from generate_image: expected str or bytes, got {type(bot_response)}")

            if bot_response:
                # logger.info("Image generated and uploaded successfully")
                full_reply = bot_response
                # Yield the image response as a single chunk (e.g., image URL or base64 data)
                yield f"data: {json.dumps({'image': full_reply})}\n\n"
                # Store the image response and indicate completion, then exit.
                await store_chat_message(db, thread_id, "assistant", full_reply)
                yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                return
            else:
                logger.warning("Image generation returned empty response")
                raise ValueError("Image generation returned empty response")

        except ValueError as ve:
            # Handle specific ValueError exceptions with user-friendly messages
            logger.error(f"Image generation failed with ValueError: {str(ve)}")

            # Provide specific error messages based on the error type
            if "quota exceeded" in str(ve).lower():
                error_message = "I'm unable to generate images right now due to API quota limits. Please try again later or contact support."
            elif "authentication failed" in str(ve).lower():
                error_message = "I'm experiencing authentication issues with the image generation service. Please contact support."
            elif "rate limit" in str(ve).lower():
                error_message = "I'm currently rate-limited by the image generation service. Please try again in a few minutes."
            elif "s3" in str(ve).lower():
                error_message = "I generated the image but had trouble saving it. Please try again or contact support if the issue persists."
            elif "openai api returned empty response" in str(ve).lower():
                error_message = "The image generation service didn't return any data. This might be a temporary issue - please try again."
            else:
                error_message = f"I encountered an issue while generating your image: {str(ve)}"

            # logger.info("Falling back to text response due to image generation failure")
            # Reset the generate_image_flag to continue with text generation
            generate_image_flag = False
            # Yield an informative message about the image generation failure
            yield f"data: {json.dumps({'chunk': error_message + '\\n\\nLet me provide a text response instead:\\n\\n'})}\n\n"
            full_reply += error_message + "\n\nLet me provide a text response instead:\n\n"

        except Exception as e:
            logger.error(f"Unexpected error during image generation: {str(e)}")
            # Handle unexpected errors with a generic message
            error_message = "I'm experiencing technical difficulties with image generation. Let me provide a text response instead."
            # logger.info("Falling back to text response due to unexpected error")

            # Reset the generate_image_flag to continue with text generation
            generate_image_flag = False

            # Yield an informative message about the image generation failure
            yield f"data: {json.dumps({'chunk': error_message + '\\n\\nLet me provide a text response instead:\\n\\n'})}\n\n"
            full_reply += error_message + "\n\nLet me provide a text response instead:\n\n"

    # If web_search is requested, use the Gemini generation with the Google search tool.
    if web_search:
        # Use the generate_content method with the google search tool
        from google import genai
        from google.genai.types import Tool, GenerateContentConfig, GoogleSearch

        client = genai.Client(api_key=settings.GEMINI_API_KEY)
        model_id = "gemini-2.0-flash"
        google_search_tool = Tool(google_search=GoogleSearch())

        # Prepare contents with enhanced prompt and files
        contents = []
        if isinstance(enhanced_prompt, str):
            contents.append({"text": enhanced_prompt})
        elif isinstance(enhanced_prompt, list):
            contents = enhanced_prompt

        # Add files if provided
        if files and isinstance(files, list):
            for file in files:
                if file:
                    contents.append(file)

        response = client.models.generate_content(
            model=model_id,
            contents=contents,
            config=GenerateContentConfig(
                tools=[google_search_tool],
                response_modalities=["TEXT"],
            )
        )

        # Iterate over the returned content parts and yield them as SSE chunks.
        for part in response.candidates[0].content.parts:
            text = part.text
            full_reply += text
            yield f"data: {json.dumps({'chunk': text})}\n\n"

    else:
        # Use the chat stream generator as before
        def get_stream_generator():
            from google import genai
            client = genai.Client(api_key=settings.GEMINI_API_KEY)

            # Create a chat with the model
            chat = client.chats.create(model="gemini-2.0-flash")

            # Prepare the message content
            message_content = []

            # Add enhanced text prompt with chat history context
            if isinstance(enhanced_prompt, str):
                message_content.append({"text": enhanced_prompt})
            elif isinstance(enhanced_prompt, list):
                message_content = enhanced_prompt

            # Add files if provided
            if files and isinstance(files, list):
                for file in files:
                    if file:
                        # Log the file object to understand its structure
                        # logger.info(f"File object type: {type(file)}")
                        # logger.info(f"File object has uri: {hasattr(file, 'uri')}")
                        # logger.info(f"File object has mime_type: {hasattr(file, 'mime_type')}")

                        # Add the file object directly - this is the correct format for Gemini chat API
                        try:
                            message_content.append(file)
                            # logger.info(f"Added file object directly: {file.name if hasattr(file, 'name') else 'unknown'}")
                        except Exception as e:
                            logger.error(f"Error adding file: {e}")
                            # Skip this file if it can't be added
                            continue

            # Send the message with content
            # The API expects individual parts, not a list
            # logger.info(f"Sending message_content: {message_content}")
            # logger.info(f"Message content type: {type(message_content)}")
            # logger.info(f"Message content length: {len(message_content) if isinstance(message_content, list) else 'N/A'}")

            # For chat API, we need to pass the content as a list to send_message_stream
            # The chat API expects the content to be structured properly
            if isinstance(message_content, list):
                # If we have a list, we need to extract the text and combine with files
                text_parts = []
                file_parts = []

                for item in message_content:
                    if isinstance(item, dict) and 'text' in item:
                        text_parts.append(item['text'])
                    elif hasattr(item, 'uri'):  # This is a file object
                        file_parts.append(item)
                    elif isinstance(item, str):
                        text_parts.append(item)
                    else:
                        file_parts.append(item)

                # Combine text parts into a single string
                combined_text = ' '.join(text_parts) if text_parts else ""

                # Create the content list for the chat API
                content_parts = []
                if combined_text:
                    content_parts.append(combined_text)
                content_parts.extend(file_parts)

                # Send the message with the properly structured content
                return chat.send_message_stream(content_parts)
            else:
                # If it's not a list, pass it directly
                return chat.send_message_stream(message_content)

        # Get the blocking generator in a separate thread
        try:
            gen_obj = await asyncio.to_thread(get_stream_generator)

            # Helper to get the next chunk asynchronously
            async def async_next():
                try:
                    return await asyncio.to_thread(lambda: next(gen_obj, None))
                except Exception as e:
                    logger.error(f"Error getting next chunk: {str(e)}")
                    return None

            # Stream the response chunks as SSE events with buffering to prevent fragmentation
            buffer = ""
            buffer_size = 0
            min_chunk_size = 10  # Minimum characters before sending a chunk
            max_buffer_size = 100  # Maximum buffer size before forcing send

            while True:
                try:
                    chunk = await async_next()
                    if chunk is None:
                        # Send any remaining buffer content
                        if buffer.strip():
                            yield f"data: {json.dumps({'chunk': buffer})}\n\n"
                        break

                    text = chunk.text
                    full_reply += text
                    buffer += text
                    buffer_size += len(text)

                    # Send buffer if it's large enough or if we hit max buffer size
                    if (buffer_size >= min_chunk_size and
                        (text.endswith((' ', '.', ',', '!', '?', '\n')) or buffer_size >= max_buffer_size)):
                        yield f"data: {json.dumps({'chunk': buffer})}\n\n"
                        buffer = ""
                        buffer_size = 0

                except Exception as e:
                    logger.error(f"Error processing chunk: {str(e)}")
                    # Send any remaining buffer content before error
                    if buffer.strip():
                        yield f"data: {json.dumps({'chunk': buffer})}\n\n"
                    # Yield an error message and break the loop
                    yield f"data: {json.dumps({'error': f'Error processing response: {str(e)}'})}\n\n"
                    break
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error in stream generation: {str(e)}\nTraceback: {error_traceback}")
            # Yield an error message
            yield f"data: {json.dumps({'error': f'Error generating response: {str(e)}'})}\n\n"

    # After streaming, store the full reply in the DB
    await store_chat_message(db, thread_id, "assistant", full_reply)
    # Indicate completion
    yield f"data: {json.dumps({'status': 'completed'})}\n\n"



@router.post("/new", response_model=ChatResponse)
async def new_chat(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    chat_request: ChatRequest = ...,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Start a new chat thread.
    When using Gemini the assistant’s reply is streamed back along with metadata.
    """
    try:
        user_id = token["decoded"].get("user_id")
        organisation_info = await get_organisation_info_cached(organization_id)
        user_info = await get_user_info_cached(organization_id, user_id)
        # logger.info(f"User {user_id} is starting a new chat")

        # Check if this is an admin analytics query
        admin_response = await handle_admin_query(chat_request.message, organization_id, token, db)
        if admin_response:
            # Create a new thread and store the initial user message.
            thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
            thread_title = generate_title(thread_title_prompt)
            thread_id = await create_new_thread(db, thread_title, organization_id, user_id, chat_request.message)

            # Store the admin analytics response
            await store_chat_message(db, thread_id, "assistant", admin_response)
            history = await get_chat_history(db, thread_id)

            # logger.info(f"Processed admin analytics query in new chat thread {thread_id}")
            return success_response(
                200,
                "admin analytics chat started",
                ChatResponse(
                    thread_id=thread_id, title=thread_title, history=history, reply=admin_response
                ),
            )

        # Create a new thread and store the initial user message.
        thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
        thread_title = generate_title(thread_title_prompt)
        thread_id = await create_new_thread(db, thread_title, organization_id, user_id, chat_request.message)
        history = await get_chat_history(db, thread_id)

        # Check if this is likely an image generation request to limit knowledge retrieval
        is_image_request = any(keyword in chat_request.message.lower() for keyword in [
            "generate image", "create image", "draw", "picture", "photo", "illustration", "artwork"
        ])

        if is_image_request:
            # For image requests, use minimal context to avoid prompt length issues
            # logger.info("Image request detected - using minimal context")
            system_context = (
                f"You are an AI assistant. Here are the organisation details: {organisation_info} "
                f"and user details: {user_info}. You can generate images when requested."
            )
        else:
            # SIMPLE RAG RETRIEVAL - RELIABLE AND ACCURATE
            try:
                from app.utils.simple_rag import get_context_simple_rag

                # logger.info(f"Using simple RAG for org {organization_id}")
                # logger.info(f"Chat query: '{chat_request.message}'")

                # Get context using simple RAG system
                knowledge_context = await get_context_simple_rag(
                    organization_id,
                    chat_request.message,
                    top_k=10
                )

                # logger.info(f"Knowledge context retrieved: {len(knowledge_context)} characters")

                if knowledge_context:
                    system_context = (
                        f"Relevant knowledge from the knowledge base:\n{knowledge_context}\n\n"
                        f"Organization: {organisation_info}\nUser: {user_info}\n"
                        "Provide accurate responses based on the relevant knowledge above. "
                        "Use the knowledge to give comprehensive, helpful answers."
                    )
                    # logger.info(f"Added knowledge context: {len(knowledge_context)} characters")
                else:
                    # logger.info("No relevant knowledge found in database")
                    system_context = (
                        f"You are an AI assistant for {organisation_info}. "
                        f"User details: {user_info}. "
                        "Provide helpful responses based on your general knowledge."
                    )

            except Exception as error:
                logger.error(f"Knowledge retrieval failed: {error}")
                # Continue without knowledge base - don't fail the chat
                system_context = (
                    f"You are an AI assistant for {organisation_info}. "
                    f"User details: {user_info}. "
                    "Provide helpful responses based on your general knowledge."
                )

        history.insert(0, {"role": "system", "content": system_context})

        web_search = True if chat_request.web else False

        # Get file objects if file_ids are provided
        file_ids = chat_request.file_ids

        # Store the user message with file IDs
        await store_chat_message(db, thread_id, "user", chat_request.message, file_ids)

        if MODEL_PROVIDER.lower() == "gemini" and STREAM_RESPONSE:
            # Use already optimized history (no need to trim again)
            # Build the Gemini prompt from optimized history
            prompt = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                # NO TRUNCATION - preserve complete context including full documents
                prompt += f"{entry['role'].capitalize()}: {content}\n"
            prompt += f"User: {chat_request.message}\nAssistant:"

            # Log prompt length for monitoring
            # logger.info(f"Built prompt for new chat: {len(prompt)} characters")
            # Get file objects if file_ids are provided
            files = []
            if file_ids:
                # Query the database for file information
                for file_id in file_ids:
                    try:
                        # Get the file from Gemini
                        file = gemini_client.files.get(name=file_id)
                        if file:
                            files.append(file)
                            # logger.info(f"Retrieved file from Gemini for chat: {file_id}")
                    except Exception as e:
                        logger.error(f"Error retrieving file from Gemini for chat: {e}")

            # Pass thread_id, thread_title, into the stream generator.
            stream = stream_gemini_reply(prompt, thread_id, thread_title, db, web_search, files=files, organization_id=organization_id)
            return StreamingResponse(stream, media_type="text/event-stream")
        else:
            reply = await generate_chat_reply(history, chat_request.message, file_ids)
            await store_chat_message(db, thread_id, "assistant", reply)
            history = await get_chat_history(db, thread_id)
            # logger.info(f"Started new chat thread {thread_id} for organization {organization_id}.")
            return success_response(
                200,
                "chat started",
                ChatResponse(
                    thread_id=thread_id, title=thread_title, history=history, reply=reply
                ),
            )
    except HTTPException as e:
        logger.error("HTTPException: %s", e)
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("Error starting a new chat: %s", str(e))
        return fail_response(500, "Error starting a new chat")


@router.post("/continue", response_model=ChatResponse)
async def continue_chat(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    chat_request: ChatRequest = ...,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Continue an existing chat thread.
    When using Gemini the reply is streamed back along with metadata.
    """
    try:
        user_id = token["decoded"].get("user_id")
        organisation_info = await get_organisation_info_cached(organization_id)
        user_info = await get_user_info_cached(organization_id, user_id)
        # logger.info(f"User {user_id} is continuing chat in thread {chat_request.thread_id}")

        if not chat_request.thread_id:
            raise HTTPException(status_code=400, detail="thread_id is required to continue chat")

        # Check if this is an admin analytics query
        admin_response = await handle_admin_query(chat_request.message, organization_id, token, db)
        if admin_response:
            # Store the new user message with file IDs
            await store_chat_message(db, chat_request.thread_id, "user", chat_request.message, chat_request.file_ids)

            # Store the admin analytics response
            await store_chat_message(db, chat_request.thread_id, "assistant", admin_response)
            history = await get_chat_history(db, chat_request.thread_id)

            # logger.info(f"Processed admin analytics query in chat thread {chat_request.thread_id}")
            return success_response(
                200,
                "admin analytics chat continued",
                ChatResponse(
                    thread_id=chat_request.thread_id, history=history, reply=admin_response
                ),
            )

        # Get file objects if file_ids are provided
        file_ids = chat_request.file_ids

        # Store the new user message with file IDs
        await store_chat_message(db, chat_request.thread_id, "user", chat_request.message, file_ids)
        history = await get_chat_history(db, chat_request.thread_id)

        # Check if this is likely an image generation request to limit knowledge retrieval
        is_image_request = any(keyword in chat_request.message.lower() for keyword in [
            "generate image", "create image", "draw", "picture", "photo", "illustration", "artwork"
        ])

        # SMART TRIMMING - preserve recent chat history but vectorize old conversations
        # This maintains conversation memory while preserving knowledge base context
        history = await trim_chat_history(history, max_messages=8, max_total_length=4000)

        # Create vectorized summary of older conversation if history is long
        chat_summary = ""
        if len(history) > 8:  # If we have many messages, create a summary
            chat_summary = await vectorize_chat_summary(history[:-4])  # Summarize all but last 4 messages
            # Keep only recent messages plus summary
            recent_history = history[-4:]  # Last 4 messages
            if chat_summary:
                summary_msg = {"role": "system", "content": f"Previous conversation summary: {chat_summary}"}
                history = [summary_msg] + recent_history
                # logger.info(f"Created chat summary: {len(chat_summary)} chars, keeping {len(recent_history)} recent messages")

        if is_image_request:
            # For image requests, use minimal context to avoid prompt length issues
            # logger.info("Image request detected - using minimal context")
            system_context = (
                f"You are an AI assistant. Here are the organisation details: {organisation_info} "
                f"and user details: {user_info}. You can generate images when requested."
            )
        else:
            # SIMPLE RAG RETRIEVAL - RELIABLE AND ACCURATE
            try:
                from app.utils.simple_rag import get_context_simple_rag

                # logger.info(f"Using simple RAG for org {organization_id}")

                # Get context using simple RAG system
                knowledge_context = await get_context_simple_rag(
                    organization_id,
                    chat_request.message,
                    top_k=10
                )

                if knowledge_context:
                    system_context = (
                        f"Relevant knowledge from the knowledge base:\n{knowledge_context}\n\n"
                        f"Organization: {organisation_info}\nUser: {user_info}\n"
                        "Continue the conversation using relevant knowledge when applicable. "
                        "Provide accurate responses based on the knowledge above."
                    )
                    # logger.info(f"Added knowledge context: {len(knowledge_context)} characters")
                else:
                    # logger.info("No relevant knowledge found in database")
                    system_context = (
                        f"You are an AI assistant for {organisation_info}. "
                        f"User details: {user_info}. "
                        "Continue the conversation naturally based on the chat history."
                    )

            except Exception as error:
                logger.error(f"Knowledge retrieval failed: {error}")
                # Continue without knowledge base - don't fail the chat
                system_context = (
                    f"You are an AI assistant for {organisation_info}. "
                    f"User details: {user_info}. "
                    "Continue the conversation naturally based on the chat history."
                )

        history.insert(0, {"role": "system", "content": system_context})

        web_search = True if chat_request.web else False

        if MODEL_PROVIDER.lower() == "gemini" and STREAM_RESPONSE:
            # History is already optimized and vectorized above
            # Build the Gemini prompt from optimized history
            prompt = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                # NO TRUNCATION - preserve complete context including full documents
                prompt += f"{entry['role'].capitalize()}: {content}\n"
            prompt += f"User: {chat_request.message}\nAssistant:"

            # Log prompt length for monitoring
            # logger.info(f"Built prompt for continue chat: {len(prompt)} characters")
            # Get file objects if file_ids are provided
            files = []
            if file_ids:
                # Query the database for file information
                for file_id in file_ids:
                    try:
                        # Get the file from Gemini
                        file = gemini_client.files.get(name=file_id)
                        if file:
                            files.append(file)
                            # logger.info(f"Retrieved file from Gemini for chat: {file_id}")
                    except Exception as e:
                        logger.error(f"Error retrieving file from Gemini for chat: {e}")

            # In this example, thread_title is not updated on continue so we pass None.
            stream = stream_gemini_reply(prompt, chat_request.thread_id, None, db, web_search, files=files, organization_id=organization_id)
            return StreamingResponse(stream, media_type="text/event-stream")
        else:
            reply = await generate_chat_reply(history, chat_request.message, file_ids)
            await store_chat_message(db, chat_request.thread_id, "assistant", reply)
            history = await get_chat_history(db, chat_request.thread_id)
            # logger.info(f"Continued chat thread {chat_request.thread_id} for organization {organization_id}.")
            return success_response(
                200,
                "chat continued",
                ChatResponse(
                    thread_id=chat_request.thread_id, history=history, reply=reply
                ),
            )
    except HTTPException as e:
        logger.error("An exception occurred: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        return fail_response(500, "An unexpected error occurred")


@router.get("/threads/{thread_id}", response_model=ThreadHistoryResponse)
async def get_thread_message(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    thread_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """Retrieve the chat history in a particular thread"""
    try:
        # logger.info(f"retrieving the messages in chat thread {thread_id}")
        user_id = token["decoded"].get("user_id")
        thread_stmt = select(ChatThread).where(
            ChatThread.id == thread_id,
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(thread_stmt)
        thread_data = result.scalars().first()

        # logger.info("chat thread retrieved")
        if not thread_data:
            raise HTTPException(status_code=404, detail="Requested thread not found")

        # logger.info(
            f"Retrieving the related chat messages for the thread id : {thread_id}"
        )

        msg_stmt = (
            select(ChatMessage)
            .where(ChatMessage.thread_id == thread_id)
            .order_by(ChatMessage.timestamp)
        )
        msg_result = await db.execute(msg_stmt)
        messages = msg_result.scalars().all()
        # logger.info(messages)
        message_data = []
        for msg in messages:
            response = MessageResponse(
                role=msg.role,
                message=msg.message,
                timestamp=msg.timestamp,
            )
            if msg.file_ids:
                response.file_ids = msg.file_ids.split(",")
            message_data.append(response)

        response = ThreadHistoryResponse(
            thread_id=thread_data.id,
            title=thread_data.title,
            created_at=thread_data.created_at,
            updated_at=thread_data.updated_at,
            history=message_data,
        )

        # logger.info(f"chat thread: {thread_id} messages retrived successfully")
        return success_response(200, "Message thread retrieved", response)
    except HTTPException as e:
        logger.error("An exception occurred: %s".str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        return fail_response(500, "An unexpected error occurred")


@router.get("/threads", response_model=List[ThreadHistoryResponse])
async def get_all_threads(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    db: AsyncSession = Depends(get_db_session),
):
    """
    Retrieve all chat threads and their chat history for the given organization and user.
    """
    try:
        user_id = token["decoded"].get("user_id")
        # Query threads for the given organization and user.
        threads_stmt = select(ChatThread).where(
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(threads_stmt)
        threads = result.scalars().all()

        if not threads:
            raise HTTPException(
                status_code=404,
                detail="No threads found for the provided organization and user.",
            )

        thread_data = [
            {"id": thread.id, "title": thread.title, "created_at": thread.created_at}
            for thread in threads
        ]

        return success_response(200, "chat history retrieved", thread_data)
    except HTTPException as e:
        logger.error("An exception occurred: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        return fail_response(500, "An unexpected error occurred")


@router.delete("/threads/{thread_id}", response_model=DeleteThreadResponse)
async def delete_chat_thread(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[dict, Depends(get_current_user)],
    thread_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Delete a chat thread and all its associated messages for the given organization and user.
    Only the user who owns the thread can delete it.
    """
    try:
        user_id = token["decoded"].get("user_id")
        # logger.info(f"User {user_id} is attempting to delete chat thread {thread_id}")

        # Verify the thread exists and belongs to the user and organization
        thread_stmt = select(ChatThread).where(
            ChatThread.id == thread_id,
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(thread_stmt)
        thread = result.scalars().first()

        if not thread:
            logger.error(f"Thread {thread_id} not found for user {user_id} in organization {organization_id}")
            return fail_response(
                404,
                "Chat thread not found or you don't have permission to delete it"
            )

        # Delete all chat messages associated with this thread
        messages_stmt = select(ChatMessage).where(ChatMessage.thread_id == thread_id)
        messages_result = await db.execute(messages_stmt)
        messages = messages_result.scalars().all()

        for message in messages:
            await db.delete(message)

        # Delete the thread itself
        await db.delete(thread)
        await db.commit()

        # logger.info(f"Successfully deleted chat thread {thread_id} and {len(messages)} messages")
        return success_response(
            200,
            "Chat thread deleted successfully",
            {"thread_id": thread_id, "messages_deleted": len(messages)}
        )

    except HTTPException as e:
        logger.error(f"HTTPException occurred while deleting thread {thread_id}: {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred while deleting thread {thread_id}: {str(e)}")
        return fail_response(500, "An unexpected error occurred while deleting the chat thread")


@router.put("/threads/{thread_id}", response_model=UpdateThreadTitleResponse)
async def update_thread_title(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[dict, Depends(get_current_user)],
    thread_id: str,
    request: UpdateThreadTitleRequest,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Update the title of a chat thread for the given organization and user.
    Only the user who owns the thread can update its title.
    """
    try:
        user_id = token["decoded"].get("user_id")
        # logger.info(f"User {user_id} is updating thread {thread_id} title to '{request.new_title}'")

        # Verify the thread exists and belongs to the user and organization
        thread_stmt = select(ChatThread).where(
            ChatThread.id == thread_id,
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(thread_stmt)
        thread = result.scalars().first()

        if not thread:
            logger.error(f"Thread {thread_id} not found for user {user_id} in organization {organization_id}")
            return fail_response(
                404,
                "Chat thread not found or you don't have permission to update it"
            )

        # Store the old title for response
        old_title = thread.title

        # Update the title and updated_at timestamp
        thread.title = request.new_title
        thread.updated_at = datetime.now()
        await db.commit()

        # logger.info(f"Successfully updated thread {thread_id} title from '{old_title}' to '{request.new_title}'")
        return success_response(
            200,
            "Thread title updated successfully",
            {
                "thread": {
                    "thread_id": thread.id,
                    "old_title": old_title,
                    "new_title": request.new_title,
                    "updated_at": thread.updated_at.isoformat()
                }
            }
        )

    except HTTPException as e:
        logger.error(f"HTTPException occurred while updating thread {thread_id}: {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred while updating thread {thread_id}: {str(e)}")
        return fail_response(500, "An unexpected error occurred while updating the chat thread")


async def create_trial_thread(
    db: AsyncSession,
    title: str,
    session_id: str,
    initial_message: str,
    file_ids: List[str] = None,
) -> str:
    """Create a new chat thread for trial users without authentication"""
    try:
        # For trial users, we use a temporary organization_id and user_id based on session
        thread = ChatThread(
            title=title,
            organization_id=f"trial_{session_id}",
            user_id=f"trial_{session_id}",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        db.add(thread)
        await db.commit()
        await db.refresh(thread)
        await store_chat_message(db, thread.id, "user", initial_message, file_ids)
        # logger.info("Created new trial chat thread %s for session %s.", thread.id, session_id)
        return thread.id
    except Exception as e:
        logger.error("Error creating trial thread: %s", str(e))
        raise


async def store_trial_personalization(session_id: str, personalization_data: dict) -> bool:
    """Store personalization data for a trial session"""
    try:
        cache_key = f"trial_personalization:{session_id}"
        # logger.info(f"Storing personalization data for session {session_id}")

        try:
            await redis_client.set(cache_key, json.dumps(personalization_data), ex=86400)  # Expire after 24 hours
            return True
        except Exception as redis_error:
            logger.error(f"Redis error when storing personalization: {str(redis_error)}")
            return False
    except Exception as e:
        logger.error("Error storing trial personalization: %s", str(e))
        return False


async def get_trial_personalization(session_id: str) -> dict:
    """Get personalization data for a trial session"""
    try:
        cache_key = f"trial_personalization:{session_id}"
        # logger.info(f"Getting personalization data for session {session_id}")

        try:
            data = await redis_client.get(cache_key)
            if data:
                return json.loads(data)
        except Exception as redis_error:
            logger.error(f"Redis error when getting personalization: {str(redis_error)}")

        return {}
    except Exception as e:
        logger.error("Error getting trial personalization: %s", str(e))
        return {}


def is_unlimited_session(session_id: str) -> bool:
    """Check if a session ID is unlimited (bypasses trial limits)"""
    return session_id.startswith("unlimited_")

async def get_trials_remaining(session_id: str) -> int:
    """Get the number of trial messages remaining for a session"""
    try:
        # Check for special unlimited session ID
        if is_unlimited_session(session_id):
            # logger.info(f"Unlimited session detected: {session_id}")
            return 999999  # Return a very high number to indicate unlimited

        cache_key = f"trial_count:{session_id}"
        # logger.info(f"Checking Redis for trial count with key: {cache_key}")

        try:
            count = await redis_client.get(cache_key)
            # logger.info(f"Redis returned count: {count}")
        except Exception as redis_error:
            logger.error(f"Redis error when getting count: {str(redis_error)}")
            # If Redis fails, default to allowing the trial
            return 4

        if count is None:
            # First time user, set the trial count to 4
            # logger.info(f"First time user, setting trial count to 4 for session {session_id}")
            try:
                await redis_client.set(cache_key, "4", ex=86400)  # Expire after 24 hours
            except Exception as redis_set_error:
                logger.error(f"Redis error when setting count: {str(redis_set_error)}")
                # Continue even if Redis set fails
            return 4

        return int(count)
    except Exception as e:
        logger.error("Error getting trial count: %s", str(e))
        # Default to 4 if there's an error - allow the trial rather than block it
        return 4


async def decrement_trials_remaining(session_id: str) -> int:
    """Decrement the number of trial messages remaining and return the new count"""
    try:
        # Check for special unlimited session ID - don't decrement
        if is_unlimited_session(session_id):
            # logger.info(f"Unlimited session detected, not decrementing: {session_id}")
            return 999999  # Return a very high number to indicate unlimited

        cache_key = f"trial_count:{session_id}"
        # logger.info(f"Decrementing trial count for session {session_id}")

        try:
            count = await redis_client.get(cache_key)
            # logger.info(f"Current count before decrement: {count}")
        except Exception as redis_error:
            logger.error(f"Redis error when getting count for decrement: {str(redis_error)}")
            # If Redis fails, assume 3 remaining (first message used)
            return 3

        if count is None:
            # Should not happen, but handle it gracefully
            # logger.info(f"No count found for decrement, setting to 3 for session {session_id}")
            try:
                await redis_client.set(cache_key, "3", ex=86400)  # Expire after 24 hours
            except Exception as redis_set_error:
                logger.error(f"Redis error when setting count after decrement: {str(redis_set_error)}")
            return 3

        new_count = max(0, int(count) - 1)
        # logger.info(f"New count after decrement: {new_count}")

        try:
            await redis_client.set(cache_key, str(new_count), ex=86400)  # Refresh expiry
        except Exception as redis_set_error:
            logger.error(f"Redis error when updating count after decrement: {str(redis_set_error)}")
            # Continue even if Redis set fails

        return new_count
    except Exception as e:
        logger.error("Error decrementing trial count: %s", str(e))
        # Return 3 instead of 0 to allow the trial to continue
        return 3


@router.post("/trial", response_model=TrialChatResponse)
async def trial_chat(
    chat_request: TrialChatRequest,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Start or continue a trial chat without authentication.
    Users get 4 messages before being prompted to register.

    Optional Personalization Fields:
    - industry_or_niche: The user's industry or niche (e.g., "Tech", "Fashion", "Health")
    - company_brand_name: The name of the user's company or brand
    - company_brand_description: A brief description or mission of the company/brand

    These fields help the AI provide more personalized and relevant responses.

    Frontend Implementation Guide:
    1. For a new user, call this endpoint without a session_id
    2. Store the returned session_id in localStorage/sessionStorage
    3. For subsequent requests, include the stored session_id
    4. Track trials_remaining to show appropriate UI prompts
    5. When the user registers, call /transfer-trial-chats with the session_id
    6. Optionally include personalization fields to improve response quality
    """
    try:
        # Use the provided session_id or generate a new one
        session_id = chat_request.session_id or str(uuid.uuid4())

        # Store personalization data if provided (for new sessions or updates)
        personalization_data = {}
        if chat_request.industry_or_niche:
            personalization_data['industry_or_niche'] = chat_request.industry_or_niche
        if chat_request.company_brand_name:
            personalization_data['company_brand_name'] = chat_request.company_brand_name
        if chat_request.company_brand_description:
            personalization_data['company_brand_description'] = chat_request.company_brand_description

        if personalization_data:
            await store_trial_personalization(session_id, personalization_data)

        # Check if the user has trials remaining
        try:
            trials_remaining = await get_trials_remaining(session_id)
            # logger.info(f"Trials remaining for session {session_id}: {trials_remaining}")

            if trials_remaining <= 0:
                return fail_response(
                    403,
                    "You've used all your trial messages. Please register and log in to continue chatting.",
                    {"trials_remaining": 0, "session_id": session_id}
                )

            # Decrement the trial count
            new_trials_remaining = await decrement_trials_remaining(session_id)
            # logger.info(f"New trials remaining after decrement: {new_trials_remaining}")
        except Exception as e:
            logger.error(f"Error handling trial count: {str(e)}")
            # If there's an error, allow the trial to continue with a default count
            new_trials_remaining = 3  # Assume this is the first message

        # Get file objects if file_ids are provided
        file_ids = chat_request.file_ids

        # If this is a new chat (no thread_id), create a new thread
        if not chat_request.session_id:
            # Generate a title for the thread
            thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
            thread_title = generate_title(thread_title_prompt)
            thread_id = await create_trial_thread(db, thread_title, session_id, chat_request.message, file_ids)
            history = await get_chat_history(db, thread_id)
        else:
            # Continue an existing chat
            # Get the thread_id from the session_id
            thread_stmt = select(ChatThread).where(
                ChatThread.user_id == f"trial_{session_id}"
            ).order_by(ChatThread.created_at.desc())
            result = await db.execute(thread_stmt)
            thread = result.scalars().first()

            if not thread:
                # If no thread is found, create a new one
                thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
                thread_title = generate_title(thread_title_prompt)
                thread_id = await create_trial_thread(db, thread_title, session_id, chat_request.message, file_ids)
            else:
                thread_id = thread.id
                thread_title = thread.title
                # Store the new user message with file IDs
                await store_chat_message(db, thread_id, "user", chat_request.message, file_ids)

            history = await get_chat_history(db, thread_id)

        # For trial users, build personalized context using both current request and stored data
        system_context_parts = ["You are a helpful AI assistant. This is a trial conversation."]

        # Get stored personalization data for this session
        stored_personalization = await get_trial_personalization(session_id)

        # Merge current request data with stored data (current request takes precedence)
        effective_personalization = {**stored_personalization}
        if chat_request.industry_or_niche:
            effective_personalization['industry_or_niche'] = chat_request.industry_or_niche
        if chat_request.company_brand_name:
            effective_personalization['company_brand_name'] = chat_request.company_brand_name
        if chat_request.company_brand_description:
            effective_personalization['company_brand_description'] = chat_request.company_brand_description

        # Add personalization context if available
        if effective_personalization.get('industry_or_niche'):
            system_context_parts.append(f"The user works in the {effective_personalization['industry_or_niche']} industry/niche.")

        if effective_personalization.get('company_brand_name'):
            system_context_parts.append(f"The user represents {effective_personalization['company_brand_name']}.")

        if effective_personalization.get('company_brand_description'):
            system_context_parts.append(f"About the company/brand: {effective_personalization['company_brand_description']}")

        # Add trial information
        system_context_parts.append(f"The user has {new_trials_remaining} trial messages remaining.")
        system_context_parts.append("You can suggest registering for a full account to access additional features.")

        # Add personalization instructions
        if any(effective_personalization.values()):
            system_context_parts.append("Please tailor your responses to be relevant to their industry and company context.")

        system_context = " ".join(system_context_parts)
        history.insert(0, {"role": "system", "content": system_context})

        web_search = True if chat_request.web else False

        if MODEL_PROVIDER.lower() == "gemini" and STREAM_RESPONSE:
            # Build the Gemini prompt from history
            prompt = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                prompt += f"{entry['role'].capitalize()}: {content}\n"
            prompt += f"User: {chat_request.message}\nAssistant:"

            # First, yield the session_id as a separate event
            async def session_id_stream():
                try:
                    # Yield session_id first
                    yield f"data: {json.dumps({'session_id': session_id, 'trials_remaining': new_trials_remaining})}\n\n"

                    # Get file objects if file_ids are provided
                    files = []
                    if file_ids:
                        # Query the database for file information
                        for file_id in file_ids:
                            try:
                                # Get the file from Gemini
                                file = gemini_client.files.get(name=file_id)
                                if file:
                                    files.append(file)
                                    # logger.info(f"Retrieved file from Gemini for chat: {file_id}")
                            except Exception as e:
                                logger.error(f"Error retrieving file from Gemini for chat: {e}")
                                # Continue without this file

                    # Then yield the regular stream
                    try:
                        async for chunk in stream_gemini_reply(prompt, thread_id, thread_title, db, web_search, files=files, is_trial=True, organization_id=f"trial_{session_id}"):
                            yield chunk
                    except Exception as e:
                        import traceback
                        error_traceback = traceback.format_exc()
                        logger.error(f"Error in stream_gemini_reply: {str(e)}\nTraceback: {error_traceback}")
                        yield f"data: {json.dumps({'error': f'Error generating response: {str(e)}'})}\n\n"
                        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                except Exception as e:
                    import traceback
                    error_traceback = traceback.format_exc()
                    logger.error(f"Error in session_id_stream: {str(e)}\nTraceback: {error_traceback}")
                    yield f"data: {json.dumps({'error': f'Error processing request: {str(e)}'})}\n\n"
                    yield f"data: {json.dumps({'status': 'completed'})}\n\n"

            return StreamingResponse(session_id_stream(), media_type="text/event-stream")
        else:
            reply = await generate_chat_reply(history, chat_request.message, file_ids)
            await store_chat_message(db, thread_id, "assistant", reply)
            history = await get_chat_history(db, thread_id)

            # logger.info(f"Trial chat message processed for session {session_id}, thread {thread_id}.")
            return success_response(
                200,
                "trial chat message processed",
                TrialChatResponse(
                    thread_id=thread_id,
                    title=thread_title,
                    history=history,
                    reply=reply,
                    trials_remaining=new_trials_remaining,
                    session_id=session_id  # Always return the session_id to the frontend
                ),
            )
    except HTTPException as e:
        logger.error("HTTPException in trial chat: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error("Error in trial chat: %s\nTraceback: %s", str(e), error_traceback)
        return fail_response(500, f"Error processing trial chat message: {str(e)}")


class TransferTrialRequest(BaseModel):
    session_id: str

@router.post("/transfer-trial-chats")
async def transfer_trial_chats(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    transfer_request: TransferTrialRequest = None,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Transfer trial chat threads to a registered user account.
    This endpoint should be called after a user registers and logs in.

    The frontend should store the session_id from trial chat responses
    and pass it to this endpoint after user registration and login.
    """
    try:
        if not transfer_request or not transfer_request.session_id:
            return fail_response(400, "session_id is required")

        session_id = transfer_request.session_id

        user_id = token["decoded"].get("user_id")
        # logger.info(f"Transferring trial chats for session {session_id} to user {user_id}")

        # Find all threads associated with the trial session
        trial_user_id = f"trial_{session_id}"
        trial_org_id = f"trial_{session_id}"

        # Query for all trial threads
        thread_stmt = select(ChatThread).where(
            ChatThread.user_id == trial_user_id,
            ChatThread.organization_id == trial_org_id
        )
        result = await db.execute(thread_stmt)
        trial_threads = result.scalars().all()

        if not trial_threads:
            return success_response(200, "No trial chats found to transfer", {"transferred": 0})

        # Update each thread to associate with the registered user
        transferred_count = 0
        for thread in trial_threads:
            thread.user_id = user_id
            thread.organization_id = organization_id
            thread.updated_at = datetime.now()
            transferred_count += 1

        await db.commit()

        # Clear the trial count and personalization data from Redis
        try:
            trial_count_key = f"trial_count:{session_id}"
            personalization_key = f"trial_personalization:{session_id}"
            # logger.info(f"Clearing trial data from Redis for session {session_id}")
            await redis_client.delete(trial_count_key)
            await redis_client.delete(personalization_key)
            # logger.info(f"Successfully cleared trial data from Redis for session {session_id}")
        except Exception as redis_error:
            logger.error(f"Error clearing trial data from Redis: {str(redis_error)}")
            # Continue even if Redis delete fails

        # No need to delete the threads or messages since they've been transferred
        # But we should clean up any other temporary data associated with the trial session
        # For now, there's no additional data to clean up, but this is where we would do it
        # if we added any in the future (e.g., temporary user profiles, etc.)
        # logger.info(f"No additional cleanup needed for session {session_id}")

        # logger.info(f"Successfully transferred {transferred_count} trial chats to user {user_id}")
        return success_response(
            200,
            f"Successfully transferred {transferred_count} trial chats",
            {"transferred": transferred_count}
        )
    except HTTPException as e:
        logger.error("HTTPException in transfer trial chats: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("Error transferring trial chats: %s", str(e))
        return fail_response(500, "Error transferring trial chats")


@router.post("/upload-file", response_model=FileUploadResponse)
async def upload_file_for_chat(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db_session),
):
    """
    Upload a file to be used in chat.
    The file is uploaded to Gemini and the file ID is returned.
    """
    try:
        user_id = token["decoded"].get("user_id")
        # logger.info(f"User {user_id} is uploading a file for chat: {file.filename}")

        # Check if file is empty
        if not file.filename:
            logger.error("Empty filename provided")
            return fail_response(400, "Empty filename provided")

        # Validate file size before reading (10MB limit for Gemini)
        # FastAPI handles this automatically via the 413 error, but we'll add an explicit check
        MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes

        try:
            # Get file size if available
            if hasattr(file, "size") and file.size > MAX_FILE_SIZE:
                logger.error(f"File too large: {file.size} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Read the file content
            file_content = await file.read()

            # Check size after reading if size wasn't available before
            if len(file_content) > MAX_FILE_SIZE:
                logger.error(f"File too large: {len(file_content)} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Check if file is empty
            if len(file_content) == 0:
                logger.error("Empty file uploaded")
                return fail_response(400, "Empty file uploaded")
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            return fail_response(500, f"Error reading file: {str(e)}")

        # Get the file type
        file_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

        # Special handling for markdown files
        if file.filename.endswith('.md'):
            file_type = "text/markdown"

        # Upload the file to Gemini with MIME type validation
        try:
            uploaded_file = await upload_file_to_gemini(file_content, file.filename, file_type)

            if not uploaded_file:
                logger.error("Failed to upload file to Gemini - null response")
                raise HTTPException(status_code=500, detail="Failed to upload file to Gemini")
        except HTTPException as e:
            # Re-raise HTTP exceptions with the same status code and detail
            logger.error(f"HTTP error during file upload to Gemini: {e.status_code} - {e.detail}")
            raise e
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error uploading file to Gemini: {error_msg}")

            # Check for specific error messages that might indicate a 413 error
            if "413" in error_msg or "too large" in error_msg.lower() or "entity too large" in error_msg.lower():
                return fail_response(413, "File too large for processing")

            # Check for network-related errors
            if "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                return fail_response(503, "Service temporarily unavailable. Please try again later.")

            # Generic error
            return fail_response(500, f"Error uploading file to Gemini: {error_msg}")

        try:
            # Calculate expiry date (48 hours from now)
            expiry_date = datetime.now() + timedelta(hours=48)

            # Store file information in the database
            gemini_file = GeminiFile(
                id=uploaded_file.name,
                filename=file.filename,
                filetype=file_type,
                user_id=user_id,
                organisation_id=organization_id,
                upload_date=datetime.now(),
                expiry_date=expiry_date
            )

            db.add(gemini_file)
            await db.commit()

            # logger.info(f"File uploaded to Gemini with ID: {uploaded_file.name}")

            return success_response(
                200,
                "File uploaded successfully",
                FileUploadResponse(
                    file_id=uploaded_file.name,
                    file_name=file.filename
                )
            )
        except Exception as db_error:
            logger.error(f"Database error after successful file upload: {str(db_error)}")
            # Even though DB operation failed, the file was uploaded to Gemini
            # We should return a partial success to the client with the file ID
            return success_response(
                207, # Partial success
                "File uploaded but metadata storage failed. Some features may be limited.",
                FileUploadResponse(
                    file_id=uploaded_file.name,
                    file_name=file.filename
                )
            )
    except HTTPException as e:
        logger.error(f"HTTPException in upload_file_for_chat: {e.status_code} - {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Unexpected error uploading file for chat: {str(e)}\nTraceback: {error_traceback}")
        return fail_response(500, f"Error uploading file: {str(e)}")


@router.post("/trial-upload-file", response_model=FileUploadResponse)
async def upload_file_for_trial_chat(
    file: UploadFile = File(...),
    session_id: str = None,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Upload a file to be used in trial chat without authentication.
    The file is uploaded to Gemini and the file ID is returned.
    """
    try:
        # Validate session ID
        if not session_id:
            session_id = str(uuid.uuid4())
            # logger.info(f"Generated new session ID for trial file upload: {session_id}")
        else:
            # logger.info(f"Using provided session ID for trial file upload: {session_id}")

        # Check if file is empty
        if not file.filename:
            logger.error("Empty filename provided for trial upload")
            return fail_response(400, "Empty filename provided")

        # Check if the user has trials remaining
        try:
            trials_remaining = await get_trials_remaining(session_id)
            # logger.info(f"Trials remaining for session {session_id}: {trials_remaining}")

            if trials_remaining <= 0:
                return fail_response(
                    403,
                    "You've used all your trial messages. Please register and log in to continue chatting.",
                    {"trials_remaining": 0, "session_id": session_id}
                )
        except Exception as e:
            logger.error(f"Error checking trial count: {e}")
            # If there's an error, allow the trial to continue with a default count
            trials_remaining = 5  # Default value

        # Validate file size before reading (10MB limit for Gemini)
        MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes

        try:
            # Get file size if available
            if hasattr(file, "size") and file.size > MAX_FILE_SIZE:
                logger.error(f"Trial upload - File too large: {file.size} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Read the file content
            file_content = await file.read()

            # Check size after reading if size wasn't available before
            if len(file_content) > MAX_FILE_SIZE:
                logger.error(f"Trial upload - File too large: {len(file_content)} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Check if file is empty
            if len(file_content) == 0:
                logger.error("Empty file uploaded for trial")
                return fail_response(400, "Empty file uploaded")
        except Exception as e:
            logger.error(f"Error reading file for trial upload: {str(e)}")
            return fail_response(500, f"Error reading file: {str(e)}")

        # Get the file type
        file_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

        # Special handling for markdown files
        if file.filename.endswith('.md'):
            file_type = "text/markdown"

        # Upload the file to Gemini with MIME type validation
        try:
            uploaded_file = await upload_file_to_gemini(file_content, file.filename, file_type)

            if not uploaded_file:
                logger.error("Failed to upload trial file to Gemini - null response")
                raise HTTPException(status_code=500, detail="Failed to upload file to Gemini")
        except HTTPException as e:
            # Re-raise HTTP exceptions with the same status code and detail
            logger.error(f"HTTP error during trial file upload to Gemini: {e.status_code} - {e.detail}")
            raise e
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error uploading trial file to Gemini: {error_msg}")

            # Check for specific error messages that might indicate a 413 error
            if "413" in error_msg or "too large" in error_msg.lower() or "entity too large" in error_msg.lower():
                return fail_response(413, "File too large for processing")

            # Check for network-related errors
            if "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                return fail_response(503, "Service temporarily unavailable. Please try again later.")

            # Generic error
            return fail_response(500, f"Error uploading file to Gemini: {error_msg}")

        try:
            # Calculate expiry date (48 hours from now)
            expiry_date = datetime.now() + timedelta(hours=48)

            # Store file information in the database
            gemini_file = GeminiFile(
                id=uploaded_file.name,
                filename=file.filename,
                filetype=file_type,
                user_id=f"trial_{session_id}",
                organisation_id=f"trial_{session_id}",
                upload_date=datetime.now(),
                expiry_date=expiry_date
            )

            db.add(gemini_file)
            await db.commit()

            # logger.info(f"File uploaded to Gemini with ID: {uploaded_file.name} for trial session {session_id}")

            return success_response(
                200,
                "File uploaded successfully",
                {
                    "file_id": uploaded_file.name,
                    "file_name": file.filename,
                    "session_id": session_id,
                    "trials_remaining": trials_remaining
                }
            )
        except Exception as db_error:
            logger.error(f"Database error after successful trial file upload: {str(db_error)}")
            # Even though DB operation failed, the file was uploaded to Gemini
            # We should return a partial success to the client with the file ID
            return success_response(
                207, # Partial success
                "File uploaded but metadata storage failed. Some features may be limited.",
                {
                    "file_id": uploaded_file.name,
                    "file_name": file.filename,
                    "session_id": session_id,
                    "trials_remaining": trials_remaining
                }
            )
    except HTTPException as e:
        logger.error(f"HTTPException in upload_file_for_trial_chat: {e.status_code} - {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Unexpected error uploading file for trial chat: {str(e)}\nTraceback: {error_traceback}")
        return fail_response(500, f"Error uploading file: {str(e)}")


@router.get("/file")
async def get_file_info(
    file_id: str,
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
):
    """
    Get information about a file.
    """
    try:
        user_id = token["decoded"].get("user_id")
        # logger.info(f"User {user_id} is getting file info for file: {file_id}")

        # Query the database for file information
        file_stmt = select(GeminiFile).where(
            GeminiFile.id == file_id,
            GeminiFile.organisation_id == organization_id
        )
        result = await db.execute(file_stmt)
        file_data = result.scalars().first()

        if not file_data:
            # Check if it's a trial file
            trial_file_stmt = select(GeminiFile).where(
                GeminiFile.id == file_id,
                GeminiFile.user_id.like(f"trial_%")
            )
            result = await db.execute(trial_file_stmt)
            file_data = result.scalars().first()

            if not file_data:
                raise HTTPException(status_code=404, detail="File not found")

        # Get the file from Gemini
        try:
            gemini_file = gemini_client.files.get(name=file_id)
            if not gemini_file:
                raise HTTPException(status_code=404, detail="File not found in Gemini")

            # logger.info(f"Retrieved file from Gemini: {file_id}")

            # Return file information
            return success_response(
                200,
                "File information retrieved",
                {
                    "file_id": file_data.id,
                    "file_name": file_data.filename,
                    "file_type": file_data.filetype,
                    "upload_date": file_data.upload_date,
                    "expiry_date": file_data.expiry_date
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving file from Gemini: {e}")
            raise HTTPException(status_code=500, detail=f"Error retrieving file from Gemini: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException in get_file_info: {e}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        return fail_response(500, f"Error getting file info: {str(e)}")


@router.get("/trial-file")
async def get_trial_file_info(
    file_id: str,
    session_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Get information about a trial file without authentication.
    """
    try:
        # logger.info(f"Getting trial file info for file: {file_id} with session ID: {session_id}")

        # Query the database for file information
        file_stmt = select(GeminiFile).where(
            GeminiFile.id == file_id,
            GeminiFile.user_id == f"trial_{session_id}"
        )
        result = await db.execute(file_stmt)
        file_data = result.scalars().first()

        if not file_data:
            raise HTTPException(status_code=404, detail="File not found")

        # Get the file from Gemini
        try:
            gemini_file = gemini_client.files.get(name=file_id)
            if not gemini_file:
                raise HTTPException(status_code=404, detail="File not found in Gemini")

            # logger.info(f"Retrieved trial file from Gemini: {file_id}")

            # Return file information
            return success_response(
                200,
                "File information retrieved",
                {
                    "file_id": file_data.id,
                    "file_name": file_data.filename,
                    "file_type": file_data.filetype,
                    "upload_date": file_data.upload_date,
                    "expiry_date": file_data.expiry_date,
                    "session_id": session_id
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving trial file from Gemini: {e}")
            raise HTTPException(status_code=500, detail=f"Error retrieving file from Gemini: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException in get_trial_file_info: {e}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Error getting trial file info: {e}")
        return fail_response(500, f"Error getting file info: {str(e)}")
