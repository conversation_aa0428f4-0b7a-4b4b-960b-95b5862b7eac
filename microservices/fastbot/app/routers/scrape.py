import asyncio
from datetime import datetime
import uuid
import aiohttp

import requests

from app.gen_models.gemini_model import get_embedding
from app.utils.logger import get_logger
from app.utils.qdrant_utils import add_embedding_to_qdrant
from app.utils.text_processing import chunk_text
from crawl4ai import (AsyncWebCrawler, BrowserConfig, CacheMode,
                      CrawlerRunConfig)
from app.database.database import SessionLocal
from app.models.models import UploadedFile
from fastapi import APIRouter

from app.gen_models.gemini_model import generate_summary

router = APIRouter()
logger = get_logger(__name__)


async def process_scraped_content(organization_id: str, url: str, content: str):
    chunks = chunk_text(content)
    for chunk in chunks:
        try:
            embedding = await get_embedding(chunk)
        except Exception as e:
            logger.error("Embedding error for %s: %s", url, e)
            continue
        doc_id = str(uuid.uuid4())
        metadata = {"url": url, "text_snippet": chunk[:200]}
        try:
            add_embedding_to_qdrant(organization_id, doc_id, embedding, metadata)
        except Exception as e:
            logger.error("Upsert error for %s: %s", url, e)

async def scrape_urls_task(token, organization_id: str, urls: list, max_concurrent: int = 5):
    browser_config = BrowserConfig(
        headless=True,
        verbose=False,
        extra_args=["--disable-gpu", "--disable-dev-shm-usage", "--no-sandbox"],
    )
    crawl_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
    crawler = AsyncWebCrawler(config=browser_config)
    await crawler.start()

    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_single_url(url: str):
        async with semaphore:
            try:
                result = await crawler.arun(url=url, config=crawl_config, session_id="session_scrape")
            except Exception as e:
                logger.error("Error crawling %s: %s", url, e)
                return

            if result.success:
                content = (
                    getattr(result, "extracted_content", None)
                    or getattr(result, "cleaned_html", None)
                    or getattr(result, "fit_html", None)
                    or getattr(result, "markdown", None)
                    or getattr(result, "html", None)
                )
                if not content:
                    logger.error("No content found for %s. Available attributes: %s", url, dir(result))
                    return

                # logger.info("Crawled: %s", url)
                await process_scraped_content(organization_id, url, content)
            else:
                logger.error("Failed to crawl %s: %s", url, result.error_message)

    async def process_url_for_db(url: str, session: aiohttp.ClientSession):
        try:
            async with session.get(url) as response:
                if response.status == 200:
                    text = await response.text()
                    text_prompt = f"Summarize this in one sentence. {text[:2000]}..."
                    summary = await asyncio.to_thread(generate_summary, text_prompt)

                    content_bytes = await response.read()
                    filesize = len(content_bytes)

                    new_file = UploadedFile(
                        filename=url.split("/")[-1],
                        filesize=filesize,
                        filetype=response.headers.get("Content-Type", "link"),
                        user_id=token["decoded"]["user_id"],
                        username=token["decoded"]['user_details'].get('first_name') + ' ' +
                                 token["decoded"]['user_details'].get('last_name'),
                        organisation_id=organization_id,
                        summary=summary,
                        upload_date=datetime.now().isoformat() + "Z",
                        timestamp=int(datetime.now().timestamp())
                    )

                    def db_add_and_commit():
                        db = SessionLocal()
                        try:
                            db.add(new_file)
                            db.commit()
                        except Exception as e:
                            logger.error("Error storing file metadata for %s: %s", url, e)
                            db.rollback()
                        finally:
                            db.close()

                    await asyncio.to_thread(db_add_and_commit)
                else:
                    logger.error("Failed to fetch %s, status: %s", url, response.status)
        except Exception as e:
            logger.error("Exception fetching %s: %s", url, e)

    async with aiohttp.ClientSession() as http_session:
        db_tasks = [process_url_for_db(url, http_session) for url in urls if url]
        await asyncio.gather(*db_tasks)

    crawl_tasks = [process_single_url(url) for url in urls if url]
    await asyncio.gather(*crawl_tasks)

    await crawler.close()
