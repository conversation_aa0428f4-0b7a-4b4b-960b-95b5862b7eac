from typing import Annotated

from app.core.config import settings
from app.utils.external_calls import fetch_user_permissions
from app.utils.logger import get_logger
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt

logger = get_logger(__name__)


AUTH_SERVICE_URL = settings.AUTH_SERVICE_URL
ALGORITHM = settings.ALGORITHM
SECRET = settings.SECRET_KEY

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{AUTH_SERVICE_URL}/login")


def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET, algorithms=[ALGORITHM])
        # # logger.info(f"payload: {payload}")
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
    except JW<PERSON>rror:
        raise credentials_exception
    return {"decoded": payload, "raw_token": token}


async def check_permissions(user_id, organisation_id, required_permission):
    try:
        # fetch the user permissions
        # logger.info('fetching user permissions')
        user_permissions_dict = await fetch_user_permissions(user_id, organisation_id)
        user_permissions = user_permissions_dict.get("permissions", [])
        user_role = user_permissions_dict.get("role", "")

        if (user_role != "admin" and user_role != "owner") and required_permission not in user_permissions:
            logger.error('user does not have the required permissions')
            raise HTTPException(
                status_code=403,
                detail=f"User does not have permission to access this endpoint: {required_permission}",
            )
        return user_role
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching user permissions: {e}")
        raise HTTPException(status_code=500, detail="Error fetching user permissions")
