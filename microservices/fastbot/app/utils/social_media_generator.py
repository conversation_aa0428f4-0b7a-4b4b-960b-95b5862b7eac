from typing import Dict
from pydantic import BaseModel, Field
from app.gen_models.gemini_model import client
from app.gen_models.openai_model import generate_image
from app.models.schema import SocialMediaPlatform
from app.utils.logger import get_logger

logger = get_logger(__name__)

class SocialMediaResponse(BaseModel):
    content: str = Field(description="The repurposed social media content")


async def generate_instagram_image(content: str, organization_id: str = "default") -> str:
    """Generate an Instagram image based on the content."""
    try:
        # Create an Instagram-optimized image prompt
        image_prompt = f"""
Create a realistic, visually appealing Instagram post image inspired by the following content:

\"\"\"{content}\"\"\"

Design Guidelines:
- Format: Square (1:1 aspect ratio), ideal for Instagram feed
- Style: Realistic with a touch of modern, professional design — suitable for a social media audience
- Visual Elements: Clearly reflect the theme, subject, or message of the content. Use imagery, symbols, or scenes that communicate the core idea
- Colors: Use vibrant, high-contrast colors that are trendy and engaging on digital platforms
- Typography: If including text, use bold, legible fonts with good contrast; limit to key phrases or headlines
- Composition: Use clean, well-balanced layouts that draw the eye — consider visual hierarchy and focal points with correct spellings
- Realism: Render natural-looking textures, lighting, and human elements (if relevant) to enhance realism
- Emotion: Evoke appropriate emotion or vibe that aligns with the content (e.g., motivation, curiosity, calm, excitement)
- Shareability: Make the image visually striking enough to prompt shares and saves

Final Output:
- Realistic image with high aesthetic quality
- Suitable for direct posting to Instagram
"""


        # Generate the image using the existing image generation function
        image_url = await generate_image(image_prompt, organization_id)

        if image_url:
            # logger.info(f"Generated Instagram image successfully: {image_url}")
            return image_url
        else:
            logger.error("Failed to generate Instagram image")
            # Fallback to a text description if image generation fails
            return f"Instagram image for: {content[:100]}..."

    except Exception as e:
        logger.error(f"Error generating Instagram image: {str(e)}")
        # Fallback to a text description if image generation fails
        return f"Instagram image for: {content[:100]}..."

async def generate_platform_content(platform: SocialMediaPlatform, content: str, organization_id: str = "default") -> str:
    """Generate optimized content for a specific social media platform."""

    # For Instagram, generate an image instead of text
    if platform == SocialMediaPlatform.INSTAGRAM:
        # logger.info(f"Generating Instagram image for organization: {organization_id}")
        return await generate_instagram_image(content, organization_id)

    # Platform-specific prompts for optimal content generation
    platform_prompts = {
        SocialMediaPlatform.TWITTER: f"""
        Repurpose this content for Twitter (max 280 characters):

        Original content: {content}

        Requirements:
        - Keep it under 280 characters
        - Make it engaging and shareable
        - Include 1-2 relevant hashtags
        - Use a hook to grab attention
        - Professional but accessible tone
        """,

        SocialMediaPlatform.FACEBOOK: f"""
        Repurpose this content for Facebook (max 2200 characters):

        Original content: {content}

        Requirements:
        - Storytelling format that encourages engagement
        - Conversational and community-focused tone
        - Include a question to spark discussion
        - Use emojis sparingly but effectively
        - Professional yet friendly approach
        """,

        SocialMediaPlatform.LINKEDIN: f"""
        Repurpose this content for LinkedIn (max 3000 characters):

        Original content: {content}

        Requirements:
        - Professional and authoritative tone
        - Include industry insights and thought leadership
        - Add actionable takeaways
        - Use professional language
        - Include 2-3 relevant hashtags
        """,

        SocialMediaPlatform.INSTAGRAM: f"""
        Repurpose this content for Instagram (max 2200 characters):

        Original content: {content}

        Requirements:
        - Visual storytelling approach
        - Engaging and lifestyle-focused
        - Include 5-8 relevant hashtags
        - Use emojis to enhance readability
        - Inspirational and relatable tone
        """
    }

    try:
        prompt = platform_prompts.get(platform, platform_prompts[SocialMediaPlatform.TWITTER])

        # Use structured output to get clean response without introductory text
        response = client.models.generate_content(
            model="gemini-2.0-flash",
            contents=prompt,
            config={
                'response_mime_type': 'application/json',
                'response_schema': SocialMediaResponse,
            },
        )

        # Extract the content from the structured response
        repurposed_content = response.parsed.content

        # Ensure character limits are respected
        max_chars = {
            SocialMediaPlatform.TWITTER: 280,
            SocialMediaPlatform.FACEBOOK: 2200,
            SocialMediaPlatform.LINKEDIN: 3000,
            SocialMediaPlatform.INSTAGRAM: 2200
        }

        if len(repurposed_content) > max_chars[platform]:
            repurposed_content = repurposed_content[:max_chars[platform]-3] + "..."

        # logger.info(f"Generated {platform.value} content: {len(repurposed_content)} characters")
        return repurposed_content

    except Exception as e:
        logger.error(f"Error generating content for {platform.value}: {str(e)}")
        # Return a simple fallback
        fallback = f"Check out this content: {content[:100]}..."
        return fallback[:280] if platform == SocialMediaPlatform.TWITTER else fallback

async def generate_all_platform_content(content: str, platforms: list, organization_id: str = "default") -> Dict[str, str]:
    """Generate content for all specified platforms."""

    results = {}

    for platform in platforms:
        try:
            platform_enum = SocialMediaPlatform(platform)
            repurposed_content = await generate_platform_content(platform_enum, content, organization_id)
            results[platform] = repurposed_content
            # logger.info(f"Successfully generated content for {platform}")
        except Exception as e:
            logger.error(f"Failed to generate content for {platform}: {str(e)}")
            results[platform] = f"Error generating content for {platform}"

    return results
