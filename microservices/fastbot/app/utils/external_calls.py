import httpx
from app.core.config import settings
from app.utils.logger import get_logger
from fastapi import HTTPException
import json
import httpx
import redis
from app.core.config import settings

# Create an async Redis client
redis_client = None  # Global variable

async def init_redis():
    """Initialize the Redis client asynchronously."""
    global redis_client
    if redis_client is None:
        redis_client = redis.asyncio.from_url(settings.REDIS_URL, encoding="utf8", decode_responses=True)

logger = get_logger(__name__)

timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)
AUTH_SERVICE_URL, NOTIFICATION_SERVICE_URL = (
    settings.AUTH_SERVICE_URL,
    settings.NOTIFICATION_SERVICE_URL,
)


async def fetch_user_organisation(user_id: str) -> str:
    """
    Fetch the organization ID for the current user by making an HTTP
    request to the organization service.

    Args:
        user_id (str): The ID of the user whose organization ID is being fetched.

    Returns:
        str: The organization ID of the user.

    Raises:
        HTTPException: If fetching the organization ID fails.
    """

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/user/organisations",
                params={"user_id": user_id},
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Failed to fetch organization ID for user.",
                )
            return data.get("data")[0]
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the organisation service: {str(e)}",
        )


# TODO: send the user id also, so as to confirm user is part of that
# organisation and then it'll return the user details
# (name, role, permissions) it can  then be cached also. so there would be no
# need to call the fetch user endpoint again.
async def verify_organization(organisation_id: str) -> str:
    """
    Verify the organization by making
    an HTTP request to the authentication service.

    Args:
        organisation_id (str): The ID of the organization to verify.

    Returns:
        str: The verified organization ID.

    Raises:
        HTTPException: If the organization verification fails.
    """
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/verify-organisation/{organisation_id}",
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Organization verification failed.",
                )
            return data.get("data")["id"]
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the authentication service: {str(e)}",
        )


async def fetch_user_permissions(user_id: str, organisation_id: str) -> dict:
    """
    Fetch user permissions by making an HTTP request to the user service.

    Args:
        user_id (str): The ID of the user to fetch permissions for.
        organisation_id (str): The ID of the organization.

    Returns:
        dict: A dictionary containing the user's permissions.

    Raises:
        HTTPException: If fetching the user permissions fails.
    """

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/user/{user_id}/permissions",
                params={"organisation_id": organisation_id},
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Failed to fetch user permissions.",
                )
            return data.get("data")
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500, detail=f"Error connecting to the user service: {str(e)}"
        )


async def fetch_user_details(user_id: str) -> dict:
    """
    Fetch the user details by making an HTTP request to the authentication service.

    Args:
        user_id (str): The ID of the user to fetch details for.
        organisation_id (str): The ID of the organisation the user to fetch details for.

    Returns:
        dict: A dictionary containing the user's details.

    Raises:
        HTTPException: If fetching the user details fails.
    """
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/user/{user_id}",
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Failed to fetch user details.",
                )
            return data.get("data")
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the authentication service: {str(e)}",
        )


async def send_notification_request(
    organization_id: str,
    message: str,
    auth_token: str,
    timeout: int = 10,
) -> dict:
    """
    Sends a notification to the /notify endpoint.

    Args:
        organization_id (str): The ID of the organization.
        message (str): The notification message.
        auth_token (str): The JWT authentication token.
        api_url (str): The base URL of the API service.
        timeout (int): The timeout for the request in seconds.

    Returns:
        dict: The response from the /notify endpoint.

    Raises:
        HTTPException: If sending the notification fails.
    """
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
    }
    payload = {"organization_id": organization_id, "message": message}
    # logger.info(f"Auth token: {auth_token}")

    async with httpx.AsyncClient(
        timeout=timeout,
    ) as client:
        response = await client.post(
            f"{NOTIFICATION_SERVICE_URL}/notify", json=payload, headers=headers
        )
        # logger.info(f"{response.status_code, response.json()}")


async def fetch_user_roles(organisation_id: str, user_id: str) -> dict:
    """
    Fetch user roles by making an HTTP request to the user service.

    Args:
        user_id (str): The ID of the user to fetch roles for.
        organisation_id (str): The ID of the organization.

    Returns:
        dict: A dictionary containing the user's roles.

    Raises:
        HTTPException: If fetching the user roles fails.
    """

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/user/roles/{organisation_id}/{user_id}",
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Failed to fetch user roles.",
                )
            return data.get("data")
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500, detail=f"Error connecting to the user service: {str(e)}"
        )

async def get_organisation_info_cached(organisation_id: str):
    """Retrieve organization info with caching."""
    cache_key = f"organisation:{organisation_id}"

    cached_data = await redis_client.get(cache_key)
    # logger.info(f"cached org info: {cached_data}")
    if cached_data:
        return json.loads(cached_data)

    async with httpx.AsyncClient() as client:
        response = await client.get(f"{AUTH_SERVICE_URL}/org_details/{organisation_id}")
        # logger.info(f"Retrieved org info: {response}")
    if response.status_code != 200:
        return {"error": "Failed to retrieve organisation info"}

    data = response.json()

    await redis_client.setex(cache_key, 3600, json.dumps(data))

    return data

async def get_user_info_cached(organisation_id: str, user_id: str):
    """Retrieve user info with caching, including roles and permissions."""
    cache_key = f"user:{organisation_id}:{user_id}"

    cached_data = await redis_client.get(cache_key)
    if cached_data:
        # logger.info(f"cached user info: {cached_data}")

        return json.loads(cached_data)

    async with httpx.AsyncClient() as client:
        response = await client.get(f"{AUTH_SERVICE_URL}/organisation/{organisation_id}/user/{user_id}")
        # logger.info(f"Retrieved user info: {response}")
    if response.status_code != 200:
        return {"error": "Failed to retrieve user info"}

    data = response.json()

    await redis_client.setex(cache_key, 1800, json.dumps(data))

    return data
