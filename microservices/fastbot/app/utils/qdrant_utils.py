import json

import redis.asyncio as redis
from app.core.config import settings
# from app.utils.embedding import get_embedding  
from app.gen_models.gemini_model import get_embedding
from app.utils.logger import get_logger
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, PointStruct, VectorParams, Filter, FieldCondition, MatchValue

logger = get_logger(__name__)

VECTOR_DIM, QDRANT_HOST, QDRANT_PORT = (
    int(settings.VECTOR_DIM),
    settings.QDRANT_HOST,
    settings.QDRANT_PORT,
)

# Initialize Qdrant Client with fallback for local development
def get_qdrant_client():
    """Get Qdrant client with automatic fallback to localhost if Docker hostname fails."""
    try:
        # Try the configured host first (usually 'qdrant' for Docker)
        client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
        # Test the connection
        client.get_collections()
        # logger.info(f"Connected to Qdrant at {QDRANT_HOST}:{QDRANT_PORT}")
        return client
    except Exception as e:
        logger.warning(f"Failed to connect to Qdrant at {QDRANT_HOST}:{QDRANT_PORT}: {e}")
        if QDRANT_HOST != "localhost":
            try:
                # Fallback to localhost for local development
                # logger.info("Attempting fallback connection to localhost:6333")
                client = QdrantClient(host="localhost", port=QDRANT_PORT)
                client.get_collections()
                # logger.info("Successfully connected to Qdrant at localhost:6333")
                return client
            except Exception as fallback_error:
                logger.error(f"Fallback connection to localhost also failed: {fallback_error}")
                raise fallback_error
        else:
            raise e

qdrant_client = get_qdrant_client()
redis_client = redis.from_url(
    settings.REDIS_URL, encoding="utf8", decode_responses=True
)


def ensure_qdrant_collection(organization_id: str):
    """Ensure Qdrant collection exists or create it."""
    KNOWLEDGEBASE_COLLECTION = f"kb_{organization_id}"
    # logger.info(f"Ensuring collection for organization {organization_id}: {KNOWLEDGEBASE_COLLECTION}")
    try:
        collection_info = qdrant_client.get_collection(collection_name=KNOWLEDGEBASE_COLLECTION)
        # logger.info(f"Qdrant collection '{KNOWLEDGEBASE_COLLECTION}' exists with {collection_info.points_count} points.")
    except Exception as e:
        logger.warning(f"Collection '{KNOWLEDGEBASE_COLLECTION}' not found; creating collection: {e}")
        qdrant_client.recreate_collection(
            collection_name=KNOWLEDGEBASE_COLLECTION,
            vectors_config=VectorParams(size=VECTOR_DIM, distance=Distance.COSINE),
        )
        # logger.info(f"Qdrant collection '{KNOWLEDGEBASE_COLLECTION}' created for organization {organization_id}.")
    finally:
        return KNOWLEDGEBASE_COLLECTION


def add_embedding_to_qdrant(
    organization_id: str, doc_id: str, embedding: list, metadata: dict
):
    """Legacy function for single document storage - kept for backward compatibility."""
    try:
        KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
        # logger.info(
            f"Upserting document {doc_id} into Qdrant for org {organization_id} with knowledgebase {KNOWLEDGEBASE_COLLECTION}."
        )

        point = PointStruct(
            id=doc_id,
            vector=embedding,
            payload={"organization_id": organization_id, **metadata},
        )
        qdrant_client.upsert(collection_name=KNOWLEDGEBASE_COLLECTION, points=[point])
        # logger.info(
            "Upserted document %s for organization %s.", doc_id, organization_id
        )
    except Exception as e:
        logger.error("Error upserting into Qdrant: %s", e)
        raise


async def add_chunked_document_to_qdrant(
    organization_id: str, chunk_metadata_list: list
):
    """
    Store a document as multiple chunks in Qdrant for better retrieval.
    Each chunk is stored as a separate point with its own embedding.

    Args:
        organization_id: Organization identifier
        chunk_metadata_list: List of tuples (chunk_id, embedding, metadata)
    """
    try:
        KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
        # logger.info(
            f"Upserting {len(chunk_metadata_list)} chunks into Qdrant for org {organization_id}"
        )

        # Create points for all chunks
        points = []
        for chunk_id, embedding, metadata in chunk_metadata_list:
            point = PointStruct(
                id=chunk_id,
                vector=embedding,
                payload={"organization_id": organization_id, **metadata},
            )
            points.append(point)

        # Batch upsert all chunks
        qdrant_client.upsert(collection_name=KNOWLEDGEBASE_COLLECTION, points=points)

        # Log success with document info
        doc_id = chunk_metadata_list[0][2].get("document_id", "unknown")
        filename = chunk_metadata_list[0][2].get("filename", "unknown")
        # logger.info(
            f"Successfully upserted {len(points)} chunks for document {filename} (ID: {doc_id}) in org {organization_id}"
        )

    except Exception as e:
        logger.error(f"Error upserting chunked document into Qdrant: {e}")
        raise


def remove_embedding_from_qdrant(
    organization_id: str, doc_id: str,
):
    """
    Remove document and all its chunks from Qdrant.
    Handles both legacy single documents and new chunked documents.
    """
    try:
        KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
        # logger.info(
            f"Removing document {doc_id} and its chunks from Qdrant for org {organization_id}"
        )

        # First, try to remove the main document (legacy format)
        try:
            qdrant_client.delete(
                collection_name=KNOWLEDGEBASE_COLLECTION,
                points_selector=[doc_id]
            )
            # logger.info(f"Removed main document {doc_id}")
        except Exception as e:
            logger.debug(f"No main document found for {doc_id}: {e}")

        # Then, remove all chunks for this document
        # Search for all chunks belonging to this document
        try:
            chunk_filter = Filter(
                must=[
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=doc_id)
                    ),
                    FieldCondition(
                        key="organization_id",
                        match=MatchValue(value=organization_id)
                    )
                ]
            )

            # Find all chunks for this document
            chunk_results = qdrant_client.scroll(
                collection_name=KNOWLEDGEBASE_COLLECTION,
                scroll_filter=chunk_filter,
                limit=1000,  # Should be enough for most documents
                with_payload=False,  # We only need IDs
            )

            chunk_ids = [point.id for point in chunk_results[0]]

            if chunk_ids:
                # Delete all chunks
                qdrant_client.delete(
                    collection_name=KNOWLEDGEBASE_COLLECTION,
                    points_selector=chunk_ids
                )
                # logger.info(f"Removed {len(chunk_ids)} chunks for document {doc_id}")
            else:
                # logger.info(f"No chunks found for document {doc_id}")

        except Exception as e:
            logger.warning(f"Error removing chunks for document {doc_id}: {e}")

        # logger.info(f"Successfully removed document {doc_id} and all its chunks for org {organization_id}")

    except Exception as e:
        logger.error("Error removing from Qdrant: %s", e)
        raise


async def get_knowledge_from_kb(organization_id: str, query: str, top: int = 10) -> list:
    """
    HYBRID RETRIEVAL: Combines semantic search with keyword matching for better results.
    This general approach works for ALL types of content, not just specific domains.
    """
    try:
        # Get semantic search results
        semantic_results = await _semantic_search(organization_id, query, top * 2)

        # Get keyword-based results for better coverage
        keyword_results = await _keyword_search(organization_id, query, top)

        # Combine and deduplicate results
        combined_results = _combine_and_rank_results(semantic_results, keyword_results, query, top)

        # logger.info(f"Hybrid retrieval returned {len(combined_results)} results for query: {query}")
        return combined_results

    except Exception as e:
        logger.error(f"Hybrid retrieval failed, falling back to basic: {e}")
        return await _basic_knowledge_retrieval(organization_id, query, top)


async def _semantic_search(organization_id: str, query: str, top: int = 10) -> list:
    """
    Semantic search using embeddings - finds conceptually similar content.
    """
    try:
        from app.gen_models.gemini_model import get_embedding
        query_embedding = await get_embedding(query)
        collection_name = ensure_qdrant_collection(organization_id)

        organization_filter = Filter(
            must=[
                FieldCondition(
                    key="organization_id",
                    match=MatchValue(value=organization_id)
                )
            ]
        )

        results = qdrant_client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            query_filter=organization_filter,
            limit=top,
            with_payload=True,
        )

        semantic_results = []
        for result in results:
            chunk_text = result.payload.get("chunk_text", result.payload.get("text_snippet", ""))
            if chunk_text:
                semantic_results.append({
                    "text": chunk_text,
                    "score": result.score,
                    "type": "semantic",
                    "metadata": result.payload
                })

        # logger.info(f"Semantic search found {len(semantic_results)} results")
        return semantic_results

    except Exception as e:
        logger.error(f"Semantic search failed: {e}")
        return []

async def _keyword_search(organization_id: str, query: str, top: int = 10) -> list:
    """
    Keyword-based search - finds chunks containing query terms.
    This ensures we don't miss relevant content due to embedding limitations.
    """
    try:
        collection_name = ensure_qdrant_collection(organization_id)

        # Get all chunks for this organization
        all_points = qdrant_client.scroll(
            collection_name=collection_name,
            scroll_filter=Filter(
                must=[
                    FieldCondition(
                        key="organization_id",
                        match=MatchValue(value=organization_id)
                    )
                ]
            ),
            limit=100,  # Get all chunks
            with_payload=True
        )

        keyword_results = []
        query_words = query.lower().split()

        for point in all_points[0]:
            chunk_text = point.payload.get("chunk_text", point.payload.get("text_snippet", ""))
            if not chunk_text:
                continue

            chunk_lower = chunk_text.lower()

            # Calculate keyword match score
            matches = 0
            for word in query_words:
                if word in chunk_lower:
                    matches += 1

            if matches > 0:
                # Score based on percentage of query words found
                keyword_score = matches / len(query_words)
                keyword_results.append({
                    "text": chunk_text,
                    "score": keyword_score,
                    "type": "keyword",
                    "metadata": point.payload,
                    "matches": matches
                })

        # Sort by keyword score
        keyword_results.sort(key=lambda x: x["score"], reverse=True)

        # logger.info(f"Keyword search found {len(keyword_results)} results")
        return keyword_results[:top]

    except Exception as e:
        logger.error(f"Keyword search failed: {e}")
        return []

def _combine_and_rank_results(semantic_results: list, keyword_results: list, query: str, top: int) -> list:
    """
    Combine semantic and keyword results, removing duplicates and ranking by relevance.
    This general approach ensures the best content is found regardless of domain.
    """
    try:
        # Create a map to track unique chunks
        seen_chunks = {}
        combined_results = []

        # Process semantic results first (they usually have good relevance)
        for result in semantic_results:
            chunk_text = result["text"]
            chunk_hash = hash(chunk_text[:100])  # Use first 100 chars as identifier

            if chunk_hash not in seen_chunks:
                seen_chunks[chunk_hash] = True
                combined_results.append({
                    "text": chunk_text,
                    "semantic_score": result["score"],
                    "keyword_score": 0,
                    "combined_score": result["score"],
                    "metadata": result["metadata"]
                })

        # Add keyword results, boosting scores for chunks that appear in both
        for result in keyword_results:
            chunk_text = result["text"]
            chunk_hash = hash(chunk_text[:100])

            if chunk_hash in seen_chunks:
                # Boost existing chunk that was found by both methods
                for combined in combined_results:
                    if hash(combined["text"][:100]) == chunk_hash:
                        combined["keyword_score"] = result["score"]
                        # Hybrid score: semantic + keyword boost
                        combined["combined_score"] = combined["semantic_score"] + (result["score"] * 0.5)
                        break
            else:
                # Add new chunk found only by keyword search
                seen_chunks[chunk_hash] = True
                combined_results.append({
                    "text": chunk_text,
                    "semantic_score": 0,
                    "keyword_score": result["score"],
                    "combined_score": result["score"] * 0.8,  # Slightly lower than semantic
                    "metadata": result["metadata"]
                })

        # Sort by combined score
        combined_results.sort(key=lambda x: x["combined_score"], reverse=True)

        # Return just the text content
        final_results = [result["text"] for result in combined_results[:top]]

        # logger.info(f"Combined ranking returned {len(final_results)} results")
        return final_results

    except Exception as e:
        logger.error(f"Result combination failed: {e}")
        # Fallback: return semantic results only
        return [r["text"] for r in semantic_results[:top]]

async def _basic_knowledge_retrieval(organization_id: str, query: str, top: int = 10) -> list:
    """
    Basic knowledge retrieval - fallback implementation.
    Removed Redis caching to prevent stale data issues.
    """
    KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
    # logger.info(
        f"Searching KB for query '{query}' in org {organization_id} of knowledgebase {KNOWLEDGEBASE_COLLECTION}."
    )

    try:
        query_embedding = await get_embedding(query)
    except Exception as e:
        logger.error("Error generating embedding for KB query: %s", e)
        return []

    try:
        # Add safety filter to ensure we only get documents for this organization
        organization_filter = Filter(
            must=[
                FieldCondition(
                    key="organization_id",
                    match=MatchValue(value=organization_id)
                )
            ]
        )

        # Search for more results to account for chunked documents
        results = qdrant_client.search(
            collection_name=KNOWLEDGEBASE_COLLECTION,
            query_vector=query_embedding,
            query_filter=organization_filter,
            limit=top * 2,  # Get more results to allow for deduplication
            with_payload=True,
        )

        # logger.info(f"Found {len(results)} raw results for org {organization_id}")
    except Exception as e:
        logger.error("Error querying Qdrant for KB: %s", e)
        return []

    # Enhanced processing for chunked documents
    knowledge_results = []
    seen_documents = set()

    for res in results:
        if not res.payload.get("organization_id") == organization_id:
            continue

        # Get the full chunk text if available, otherwise fall back to text_snippet
        chunk_text = res.payload.get("chunk_text", res.payload.get("text_snippet", ""))
        if not chunk_text:
            continue

        # Add document context information
        filename = res.payload.get("filename", "Unknown")
        chunk_index = res.payload.get("chunk_index", 0)
        total_chunks = res.payload.get("total_chunks", 1)
        document_id = res.payload.get("document_id", "unknown")

        # Create enhanced result with context
        enhanced_result = {
            "text": chunk_text,
            "filename": filename,
            "chunk_info": f"Part {chunk_index + 1} of {total_chunks}" if total_chunks > 1 else "Complete document",
            "document_id": document_id,
            "score": res.score,
            "metadata": {
                "processing_engine": res.payload.get("processing_engine", "unknown"),
                "document_type": res.payload.get("document_type", "unknown"),
                "page_count": res.payload.get("page_count", 0),
                "has_tables": res.payload.get("has_tables", False),
                "has_images": res.payload.get("has_images", False),
            }
        }

        knowledge_results.append(enhanced_result)
        seen_documents.add(document_id)

        # Stop when we have enough unique results
        if len(knowledge_results) >= top:
            break

    # For backward compatibility, also return simple text list
    simple_results = [result["text"] for result in knowledge_results]

    # logger.info(f"Returning {len(simple_results)} enhanced results from {len(seen_documents)} unique documents")

    return simple_results


async def get_enhanced_knowledge_from_kb(organization_id: str, query: str, top: int = 10) -> dict:
    """
    Get enhanced knowledge retrieval results with full metadata.
    Uses simple RAG system for reliable results.
    """
    try:
        # Use simple RAG system for reliable results
        from app.utils.simple_rag import get_context_simple_rag

        context = await get_context_simple_rag(organization_id, query, top)

        # Convert to expected format
        if context:
            return {
                "chunks": [{"text": context, "score": 1.0, "metadata": {"source": "simple_rag"}}],
                "query_analysis": {"method": "simple_rag"},
                "retrieval_metadata": {"system": "simple_rag"},
                "total_results": 1
            }
        else:
            return {
                "chunks": [],
                "query_analysis": {"method": "simple_rag"},
                "retrieval_metadata": {"system": "simple_rag"},
                "total_results": 0
            }

    except Exception as e:
        logger.error(f"Error in enhanced knowledge retrieval: {e}")
        # Fallback to basic retrieval
        basic_results = await _basic_knowledge_retrieval(organization_id, query, top)
        return {
            "chunks": [{"text": text, "score": 1.0, "metadata": {}} for text in basic_results],
            "query_analysis": {"method": "basic"},
            "retrieval_metadata": {"fallback": True},
            "total_results": len(basic_results)
        }


async def get_complete_document_context(organization_id: str, query: str, max_documents: int = 2) -> dict:
    """
    Get complete document context for queries that need full document information.
    Uses simple RAG system which already provides complete document context.
    """
    try:
        from app.utils.simple_rag import get_context_simple_rag

        # Simple RAG already provides complete document context
        context = await get_context_simple_rag(organization_id, query, top_k=max_documents)

        if not context:
            return {
                "chunks": [],
                "documents": [],
                "total_results": 0,
                "context_type": "complete_document"
            }

        # Simple RAG already provides complete document context
        return {
            "chunks": [{"text": context, "score": 1.0, "metadata": {"source": "simple_rag"}}],
            "documents": [{"document_id": "simple_rag", "filename": "Retrieved Context", "total_chunks": 1, "relevance_score": 1.0}],
            "total_results": 1,
            "context_type": "complete_document",
            "query_analysis": {"method": "simple_rag"}
        }

    except Exception as e:
        logger.error(f"Error in complete document context retrieval: {e}")
        return {
            "chunks": [],
            "documents": [],
            "total_results": 0,
            "context_type": "error",
            "error": str(e)
        }


def add_org_info_to_qdrant(organisation_id: str, org_info: dict):
    """
    Add organization info as a fixed document (with id "org_info") in the organisation's Qdrant collection.
    If the document already exists, an exception is raised.
    """
    collection_name = ensure_qdrant_collection(organisation_id)
    doc_id = "org_info"
    # Attempt to retrieve the document. (This example assumes that a missing document throws an Exception.)
    try:
        existing = qdrant_client.retrieve(collection_name=collection_name, id=doc_id)
        if existing:
            raise Exception("Organization info already exists.")
    except Exception:
        # If retrieval fails (e.g. document not found), we proceed.
        pass

    # Upsert the organization info with a dummy vector (since no embedding is needed)
    point = PointStruct(
        id=doc_id,
        vector=[0.0] * VECTOR_DIM,
        payload=org_info,
    )
    qdrant_client.upsert(collection_name=collection_name, points=[point])
    # logger.info("Organization info added to collection %s.", collection_name)


def update_org_info_in_qdrant(organisation_id: str, org_info: dict):
    """
    Update the organization info in Qdrant. (The document id remains fixed as "org_info".)
    """
    collection_name = ensure_qdrant_collection(organisation_id)
    doc_id = "org_info"
    point = PointStruct(
        id=doc_id,
        vector=[0.0] * VECTOR_DIM,
        payload=org_info,
    )
    qdrant_client.upsert(collection_name=collection_name, points=[point])
    # logger.info("Organization info updated in collection %s.", collection_name)
