import logging
from logging.config import fileConfig

from alembic import context
from app.core.config import settings
from app.database.database import Base
from app.models.models import *
from sqlalchemy import engine_from_config, pool

logger = logging.getLogger(__name__)

# Read database URL from .env
DATABASE_URI = (
    settings.PROD_DATABASE_URL.replace("postgresql+asyncpg", "postgresql")
    if settings.ENV == "PRODUCTION"
    else settings.LOCAL_DATABASE_URL.replace("postgresql+asyncpg", "postgresql")
)

if not DATABASE_URI:
    raise ValueError("DATABASE_URL is not set in the .env file.")

# logger.info(f"Running in {settings.ENV} mode with db uri {DATABASE_URI}")
# Set the database URL in Alembic config
config = context.config
config.set_main_option("sqlalchemy.url", DATABASE_URI)


# Configure logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set target metadata for autogeneration
target_metadata = Base.metadata


# Modified migration function to accept connection parameter.
def run_migrations(connection) -> None:
    """Run Alembic migrations using the provided connection."""
    context.configure(connection=connection, target_metadata=target_metadata)
    with context.begin_transaction():
        context.run_migrations()


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )
    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
        future=True,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


# def run_migrations_online() -> None:
#     """Run migrations in 'online' mode.
#      In this scenario we need to create an Engine
#     and associate a connection with the context.

#     """
#     connectable = create_async_engine(settings.LOCAL_DATABASE_URL, poolclass=pool.NullPool)

#     async def do_run_migrations():
#         async with connectable.connect() as connection:
#             await connection.run_sync(run_migrations)

#     asyncio.run(do_run_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
