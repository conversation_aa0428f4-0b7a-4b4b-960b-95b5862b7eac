from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool
from app.database.session import Base
from alembic import context
from dotenv import load_dotenv
from app.models.model import *
import os
from app.core.settings import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


load_dotenv()
# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

if settings.ENV == "TESTING":
    DATABASE_URI = settings.TESTING_DATABASE_URI
    # logger.info(f"This is testing mode and this is the db uri {DATABASE_URI}")

elif settings.ENV == 'DEVELOPMENT':
    DATABASE_URI = settings.LOCAL_DATABASE_URI
    # logger.info(f"This is development mode and this is the db uri {DATABASE_URI}")

elif settings.ENV == 'PRODUCTION':
    DATABASE_URI = settings.PROD_DATABASE_URI

config.set_main_option('sqlalchemy.url', DATABASE_URI)
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
