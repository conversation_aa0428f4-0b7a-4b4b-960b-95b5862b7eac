# Unified Comments System

This document describes the unified comments system that normalizes comment data structures between Instagram and Facebook APIs to create a consistent response format for frontend components.

## Overview

The unified comments system provides:
- **Standardized comment schema** that consolidates all comment-related data from both Instagram and Facebook
- **Transformation functions** to convert platform-specific responses into the unified format
- **Unified API endpoints** that return normalized comment data
- **Platform detection** to automatically identify the source platform
- **Comprehensive error handling** and logging

## Architecture

### Core Components

1. **Unified Schema** (`app/schemas/unified_comment.py`)
   - `UnifiedComment`: Main comment structure
   - `UnifiedCommentAuthor`: Author information
   - `UnifiedCommentMedia`: Media attachments
   - `UnifiedCommentReaction`: Reaction data
   - `UnifiedCommentReply`: Reply structure

2. **Transformers** (`app/transformers/comment_transformer.py`)
   - Platform-specific transformation functions
   - Batch processing capabilities
   - Error handling and logging

3. **API Endpoints** (`app/routes/unified_comments.py`)
   - `/unified/comments/{scheduled_content_id}`: Get unified comments
   - `/unified/comments/{scheduled_content_id}/replies/{comment_id}`: Get replies
   - `/platforms/summary/{scheduled_content_id}`: Platform statistics

## Unified Comment Schema

The `UnifiedComment` schema includes all available fields from both platforms:

```python
class UnifiedComment(BaseModel):
    # Core fields (both platforms)
    id: str
    content: str
    created_time: datetime
    author: UnifiedCommentAuthor
    platform: PlatformType
    
    # Engagement metrics
    like_count: int
    reply_count: int
    
    # Hierarchical structure
    parent_id: Optional[str]
    replies: List[UnifiedCommentReply]
    
    # Media and attachments
    media: Optional[UnifiedCommentMedia]  # Instagram
    attachment: Optional[UnifiedCommentAttachment]  # Facebook
    
    # Reactions and interactions
    reactions: List[UnifiedCommentReaction]
    likes: List[Dict[str, Any]]
    
    # Platform-specific metadata
    is_hidden: Optional[bool]
    extra_data: Dict[str, Any]
    raw_data: Optional[Dict[str, Any]]  # For debugging
```

## Platform Differences

### Instagram API Fields
- `from`, `id`, `like_count`, `text`, `media{...}`, `parent_id`, `replies{...}`, `timestamp`, `username`
- Media attachments with detailed metadata
- Nested replies structure
- Simple like counts

### Facebook API Fields  
- `id`, `attachment`, `created_time`, `from`, `is_hidden`, `like_count`, `message`, `object`, `parent`, `reactions`, `likes`, `comments`
- Rich reaction types (like, love, haha, etc.)
- Attachment objects
- Detailed user information

## Usage Examples

### Getting Unified Comments

```python
# Get all comments in unified format
GET /api/v1/socials/comments/unified/{scheduled_content_id}

# Filter by platform
GET /api/v1/socials/comments/unified/{scheduled_content_id}?platform=instagram

# Pagination
GET /api/v1/socials/comments/unified/{scheduled_content_id}?limit=20&offset=40
```

### Response Format

```json
{
  "comments": [
    {
      "id": "18067688915055635",
      "content": "This is a great post!",
      "created_time": "2025-05-27T17:33:45+00:00",
      "author": {
        "id": "17841453087020874",
        "username": "john_doe",
        "name": "John Doe"
      },
      "platform": "instagram",
      "like_count": 5,
      "reply_count": 2,
      "parent_id": null,
      "replies": [],
      "media": {
        "id": "18082306612685122",
        "media_type": "image",
        "media_url": "https://example.com/image.jpg"
      },
      "reactions": [],
      "is_hidden": false,
      "extra_data": {}
    }
  ],
  "total": 25,
  "limit": 10,
  "offset": 0,
  "platform": null
}
```

### Platform Summary

```python
# Get comment statistics by platform
GET /api/v1/socials/comments/platforms/summary/{scheduled_content_id}
```

```json
{
  "success": true,
  "data": {
    "scheduled_content_id": "content_123",
    "platform_stats": {
      "instagram": {"total": 15, "top_level": 12, "replies": 3},
      "facebook": {"total": 10, "top_level": 8, "replies": 2},
      "unknown": {"total": 0, "top_level": 0, "replies": 0}
    },
    "total_comments": 25
  }
}
```

## Transformation Functions

### Instagram Comments
```python
from app.transformers.comment_transformer import transform_instagram_comment_to_unified

unified_comment = transform_instagram_comment_to_unified(instagram_data)
```

### Facebook Comments
```python
from app.transformers.comment_transformer import transform_facebook_comment_to_unified

unified_comment = transform_facebook_comment_to_unified(facebook_data)
```

### Database Comments (Auto-detection)
```python
from app.transformers.comment_transformer import transform_database_comment_to_unified

unified_comment = transform_database_comment_to_unified(db_comment)
```

### Batch Processing
```python
from app.transformers.comment_transformer import batch_transform_comments

unified_comments = batch_transform_comments(
    comments_data, 
    PlatformType.INSTAGRAM,
    include_raw_data=False
)
```

## Error Handling

The system includes comprehensive error handling:
- **Graceful degradation**: Invalid comments are logged and skipped
- **Platform detection**: Automatic fallback for unknown platforms
- **Field validation**: Missing fields use sensible defaults
- **Batch processing**: Individual failures don't break entire operations

## Testing

Run the test suite:
```bash
# Unit tests for transformers
python -m pytest tests/test_comment_transformer.py

# Integration tests for API endpoints
python -m pytest tests/test_unified_comments_api.py

# All tests
python -m pytest tests/
```

## Benefits

1. **Frontend Simplification**: Single component can handle both Instagram and Facebook comments
2. **Data Consistency**: All comment data follows the same structure
3. **Feature Parity**: No loss of platform-specific features
4. **Maintainability**: Centralized transformation logic
5. **Extensibility**: Easy to add new platforms or fields
6. **Debugging**: Raw data preservation for troubleshooting

## Migration Guide

### For Frontend Developers

Replace platform-specific comment handling:

```javascript
// Before: Platform-specific handling
if (platform === 'instagram') {
  return <InstagramComment comment={comment} />;
} else if (platform === 'facebook') {
  return <FacebookComment comment={comment} />;
}

// After: Unified handling
return <UnifiedComment comment={comment} />;
```

### For Backend Developers

Use unified endpoints instead of platform-specific ones:

```python
# Before: Platform-specific endpoints
GET /api/v1/socials/instagram/{id}/comment
GET /api/v1/socials/facebook/{id}/comment

# After: Unified endpoint
GET /api/v1/socials/comments/unified/{id}
```

## Future Enhancements

- **Real-time updates**: WebSocket support for live comment updates
- **Advanced filtering**: Filter by date range, author, content, etc.
- **Comment analytics**: Engagement metrics and sentiment analysis
- **Multi-platform posting**: Unified comment creation across platforms
- **Comment moderation**: Unified moderation tools and workflows
