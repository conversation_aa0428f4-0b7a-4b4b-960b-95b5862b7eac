"""
Integration tests for unified comments API endpoints.

This module tests the unified comments endpoints to ensure they return
properly normalized comment data from both Instagram and Facebook.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.models.model import Comment, Post
from app.schemas.unified_comment import PlatformType


@pytest.fixture
def client():
    """Create a test client"""
    return TestClient(app)


@pytest.fixture
def mock_db_session():
    """Mock database session"""
    return Mock(spec=AsyncSession)


@pytest.fixture
def sample_instagram_comment():
    """Sample Instagram comment from database"""
    comment = Mock(spec=Comment)
    comment.comment_id = "ig_comment_123"
    comment.content = "Great Instagram post!"
    comment.created_time = datetime(2025, 5, 27, 17, 33, 45)
    comment.sender = {"id": "ig_user_123", "username": "instagram_user"}
    comment.parent_id = None
    comment.media = {"id": "media_123", "media_type": "IMAGE"}
    comment.extra_data = {
        "username": "instagram_user",
        "like_count": 5,
        "replies": [],
        "hidden": False
    }
    comment.reactions = []
    comment.attachments = None
    return comment


@pytest.fixture
def sample_facebook_comment():
    """Sample Facebook comment from database"""
    comment = Mock(spec=Comment)
    comment.comment_id = "fb_comment_123"
    comment.content = "Great Facebook post!"
    comment.created_time = datetime(2025, 5, 27, 18, 0, 0)
    comment.sender = {"id": "fb_user_123", "name": "Facebook User"}
    comment.parent_id = None
    comment.media = {}
    comment.extra_data = {
        "object": "some_object",
        "like_count": 8,
        "replies": [],
        "hidden": False
    }
    comment.reactions = [{"type": "LIKE", "id": "reactor_1"}]
    comment.attachments = {"type": "photo", "url": "https://example.com/photo.jpg"}
    return comment


@pytest.fixture
def sample_post():
    """Sample post from database"""
    post = Mock(spec=Post)
    post.post_id = "post_123"
    post.schedulecontent_id = "scheduled_content_123"
    return post


class TestUnifiedCommentsEndpoints:
    """Test unified comments API endpoints"""
    
    @patch('app.routes.unified_comments.get_db')
    @patch('app.routes.unified_comments.verify_organization')
    async def test_get_unified_comments_success(
        self, 
        mock_verify_org, 
        mock_get_db,
        client,
        mock_db_session,
        sample_post,
        sample_instagram_comment,
        sample_facebook_comment
    ):
        """Test successful retrieval of unified comments"""
        # Setup mocks
        mock_verify_org.return_value = "org_123"
        mock_get_db.return_value = mock_db_session
        
        # Mock database queries
        mock_db_session.execute.return_value.scalars.return_value.first.return_value = sample_post
        mock_db_session.scalar.return_value = 2  # Total count
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = [
            sample_instagram_comment,
            sample_facebook_comment
        ]
        
        # Make request
        response = client.get(
            "/api/v1/socials/comments/unified/scheduled_content_123",
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert "comments" in data
        assert "total" in data
        assert "limit" in data
        assert "offset" in data
        
        # Should have transformed comments
        comments = data["comments"]
        assert len(comments) >= 0  # May be filtered by platform detection
        
        # Each comment should have unified structure
        for comment in comments:
            assert "id" in comment
            assert "content" in comment
            assert "created_time" in comment
            assert "author" in comment
            assert "platform" in comment
            assert comment["platform"] in ["instagram", "facebook"]
    
    @patch('app.routes.unified_comments.get_db')
    @patch('app.routes.unified_comments.verify_organization')
    async def test_get_unified_comments_not_found(
        self,
        mock_verify_org,
        mock_get_db,
        client,
        mock_db_session
    ):
        """Test retrieval when scheduled content is not found"""
        # Setup mocks
        mock_verify_org.return_value = "org_123"
        mock_get_db.return_value = mock_db_session
        
        # Mock database query to return None (not found)
        mock_db_session.execute.return_value.scalars.return_value.first.return_value = None
        
        # Make request
        response = client.get(
            "/api/v1/socials/comments/unified/nonexistent_content",
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Should return 404
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()
    
    @patch('app.routes.unified_comments.get_db')
    @patch('app.routes.unified_comments.verify_organization')
    async def test_get_unified_comments_with_platform_filter(
        self,
        mock_verify_org,
        mock_get_db,
        client,
        mock_db_session,
        sample_post,
        sample_instagram_comment
    ):
        """Test retrieval with platform filter"""
        # Setup mocks
        mock_verify_org.return_value = "org_123"
        mock_get_db.return_value = mock_db_session
        
        # Mock database queries
        mock_db_session.execute.return_value.scalars.return_value.first.return_value = sample_post
        mock_db_session.scalar.return_value = 1
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = [
            sample_instagram_comment
        ]
        
        # Make request with platform filter
        response = client.get(
            "/api/v1/socials/comments/unified/scheduled_content_123?platform=instagram",
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        # Should include platform in response
        assert data.get("platform") == "instagram"
    
    @patch('app.routes.unified_comments.get_db')
    @patch('app.routes.unified_comments.verify_organization')
    async def test_get_comment_platform_summary(
        self,
        mock_verify_org,
        mock_get_db,
        client,
        mock_db_session,
        sample_post,
        sample_instagram_comment,
        sample_facebook_comment
    ):
        """Test platform summary endpoint"""
        # Setup mocks
        mock_verify_org.return_value = "org_123"
        mock_get_db.return_value = mock_db_session
        
        # Mock database queries
        mock_db_session.execute.return_value.scalars.return_value.first.return_value = sample_post
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = [
            sample_instagram_comment,
            sample_facebook_comment
        ]
        
        # Make request
        response = client.get(
            "/api/v1/socials/comments/platforms/summary/scheduled_content_123",
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
        
        summary_data = data["data"]
        assert "scheduled_content_id" in summary_data
        assert "platform_stats" in summary_data
        assert "total_comments" in summary_data
        
        # Should have stats for each platform
        platform_stats = summary_data["platform_stats"]
        assert "instagram" in platform_stats
        assert "facebook" in platform_stats
        assert "unknown" in platform_stats
        
        # Each platform should have total, top_level, and replies counts
        for platform, stats in platform_stats.items():
            assert "total" in stats
            assert "top_level" in stats
            assert "replies" in stats


class TestUnifiedCommentsValidation:
    """Test validation and error handling"""
    
    @patch('app.routes.unified_comments.get_db')
    @patch('app.routes.unified_comments.verify_organization')
    async def test_invalid_pagination_parameters(
        self,
        mock_verify_org,
        mock_get_db,
        client,
        mock_db_session
    ):
        """Test validation of pagination parameters"""
        mock_verify_org.return_value = "org_123"
        mock_get_db.return_value = mock_db_session
        
        # Test invalid limit (too high)
        response = client.get(
            "/api/v1/socials/comments/unified/content_123?limit=200",
            headers={"Authorization": "Bearer test_token"}
        )
        assert response.status_code == 422  # Validation error
        
        # Test invalid offset (negative)
        response = client.get(
            "/api/v1/socials/comments/unified/content_123?offset=-1",
            headers={"Authorization": "Bearer test_token"}
        )
        assert response.status_code == 422  # Validation error
    
    def test_missing_authorization(self, client):
        """Test request without authorization header"""
        response = client.get(
            "/api/v1/socials/comments/unified/content_123"
        )
        # Should require authentication
        assert response.status_code in [401, 403, 422]


class TestCommentRepliesEndpoint:
    """Test comment replies endpoint"""
    
    @patch('app.routes.unified_comments.get_db')
    @patch('app.routes.unified_comments.verify_organization')
    async def test_get_comment_replies_success(
        self,
        mock_verify_org,
        mock_get_db,
        client,
        mock_db_session,
        sample_instagram_comment
    ):
        """Test successful retrieval of comment replies"""
        # Setup mocks
        mock_verify_org.return_value = "org_123"
        mock_get_db.return_value = mock_db_session
        
        # Create a reply comment
        reply_comment = Mock(spec=Comment)
        reply_comment.comment_id = "reply_123"
        reply_comment.content = "This is a reply"
        reply_comment.created_time = datetime(2025, 5, 27, 19, 0, 0)
        reply_comment.sender = {"id": "reply_user", "username": "replier"}
        reply_comment.parent_id = "ig_comment_123"
        reply_comment.extra_data = {"like_count": 1, "hidden": False}
        
        # Mock database queries
        mock_db_session.execute.return_value.scalars.return_value.first.return_value = sample_instagram_comment
        mock_db_session.scalar.return_value = 1  # Total replies count
        mock_db_session.execute.return_value.scalars.return_value.all.return_value = [reply_comment]
        
        # Make request
        response = client.get(
            "/api/v1/socials/comments/unified/content_123/replies/ig_comment_123",
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert "comments" in data
        assert "total" in data
        assert data["total"] == 1
        
        replies = data["comments"]
        assert len(replies) == 1
        assert replies[0]["content"] == "This is a reply"


if __name__ == "__main__":
    pytest.main([__file__])
