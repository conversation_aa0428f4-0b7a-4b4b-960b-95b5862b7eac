"""
Tests for comment transformation functions.

This module tests the transformation of Instagram and Facebook comment data
into the unified comment format.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

from app.schemas.unified_comment import (
    UnifiedComment,
    UnifiedCommentAuthor,
    UnifiedCommentMedia,
    PlatformType,
    MediaType
)
from app.transformers.comment_transformer import (
    transform_instagram_comment_to_unified,
    transform_facebook_comment_to_unified,
    transform_database_comment_to_unified,
    map_media_type,
    map_reaction_type,
    batch_transform_comments
)
from app.models.model import Comment


class TestMediaTypeMapping:
    """Test media type mapping functions"""
    
    def test_map_media_type_instagram(self):
        """Test Instagram media type mapping"""
        assert map_media_type("IMAGE") == MediaType.IMAGE
        assert map_media_type("VIDEO") == MediaType.VIDEO
        assert map_media_type("CAROUSEL_ALBUM") == MediaType.CAROUSEL_ALBUM
        assert map_media_type("STORY") == MediaType.STORY
        assert map_media_type("REEL") == MediaType.VIDEO
        
    def test_map_media_type_facebook(self):
        """Test Facebook media type mapping"""
        assert map_media_type("photo") == MediaType.IMAGE
        assert map_media_type("video") == MediaType.VIDEO
        assert map_media_type("mp4") == MediaType.VIDEO
        
    def test_map_media_type_unknown(self):
        """Test unknown media type mapping"""
        assert map_media_type("unknown_type") == MediaType.UNKNOWN
        assert map_media_type(None) == MediaType.UNKNOWN
        assert map_media_type("") == MediaType.UNKNOWN


class TestInstagramCommentTransformation:
    """Test Instagram comment transformation"""
    
    def test_transform_instagram_comment_basic(self):
        """Test basic Instagram comment transformation"""
        instagram_data = {
            "id": "18067688915055635",
            "text": "This is a great post!",
            "timestamp": "2025-05-27T17:33:45+0000",
            "from": {
                "id": "17841453087020874",
                "username": "john_doe"
            },
            "like_count": 5,
            "replies": {"data": []},
            "media": {
                "id": "18082306612685122",
                "media_type": "IMAGE",
                "media_url": "https://example.com/image.jpg"
            }
        }
        
        result = transform_instagram_comment_to_unified(instagram_data)
        
        assert isinstance(result, UnifiedComment)
        assert result.id == "18067688915055635"
        assert result.content == "This is a great post!"
        assert result.platform == PlatformType.INSTAGRAM
        assert result.like_count == 5
        assert result.author.id == "17841453087020874"
        assert result.author.username == "john_doe"
        assert result.media is not None
        assert result.media.media_type == MediaType.IMAGE
        
    def test_transform_instagram_comment_with_replies(self):
        """Test Instagram comment transformation with replies"""
        instagram_data = {
            "id": "comment_123",
            "text": "Parent comment",
            "timestamp": "2025-05-27T17:33:45+0000",
            "from": {"id": "user_123", "username": "parent_user"},
            "like_count": 3,
            "replies": {
                "data": [
                    {
                        "id": "reply_123",
                        "text": "This is a reply",
                        "timestamp": "2025-05-27T18:00:00+0000",
                        "username": "reply_user",
                        "like_count": 1
                    }
                ]
            }
        }
        
        result = transform_instagram_comment_to_unified(instagram_data)
        
        assert len(result.replies) == 1
        assert result.reply_count == 1
        assert result.replies[0].content == "This is a reply"
        assert result.replies[0].platform == PlatformType.INSTAGRAM


class TestFacebookCommentTransformation:
    """Test Facebook comment transformation"""
    
    def test_transform_facebook_comment_basic(self):
        """Test basic Facebook comment transformation"""
        facebook_data = {
            "id": "fb_comment_123",
            "message": "Great Facebook post!",
            "created_time": "2025-05-27T17:33:45+0000",
            "from": {
                "id": "fb_user_123",
                "name": "John Doe"
            },
            "like_count": 8,
            "attachment": {
                "type": "photo",
                "url": "https://example.com/photo.jpg"
            },
            "reactions": {
                "data": [
                    {"type": "LIKE", "id": "reactor_1", "name": "Jane Doe"}
                ]
            }
        }
        
        result = transform_facebook_comment_to_unified(facebook_data)
        
        assert isinstance(result, UnifiedComment)
        assert result.id == "fb_comment_123"
        assert result.content == "Great Facebook post!"
        assert result.platform == PlatformType.FACEBOOK
        assert result.like_count == 8
        assert result.author.id == "fb_user_123"
        assert result.author.name == "John Doe"
        assert result.attachment is not None
        assert result.attachment.attachment_type == "photo"
        assert len(result.reactions) == 1


class TestDatabaseCommentTransformation:
    """Test database Comment model transformation"""
    
    def test_transform_database_comment_instagram(self):
        """Test database comment transformation for Instagram"""
        # Mock Comment model
        db_comment = Mock(spec=Comment)
        db_comment.comment_id = "db_comment_123"
        db_comment.content = "Database comment"
        db_comment.created_time = datetime(2025, 5, 27, 17, 33, 45)
        db_comment.sender = {"id": "sender_123", "username": "db_user"}
        db_comment.parent_id = None
        db_comment.media = {"id": "media_123", "media_type": "IMAGE"}
        db_comment.extra_data = {
            "username": "db_user",
            "like_count": 2,
            "replies": [],
            "hidden": False
        }
        db_comment.reactions = []
        db_comment.attachments = None
        
        result = transform_database_comment_to_unified(db_comment)
        
        assert isinstance(result, UnifiedComment)
        assert result.id == "db_comment_123"
        assert result.content == "Database comment"
        assert result.like_count == 2
        assert result.platform == PlatformType.INSTAGRAM  # Should detect as Instagram


class TestBatchTransformation:
    """Test batch comment transformation"""
    
    def test_batch_transform_instagram_comments(self):
        """Test batch transformation of Instagram comments"""
        comments_data = [
            {
                "id": "comment_1",
                "text": "First comment",
                "timestamp": "2025-05-27T17:33:45+0000",
                "from": {"id": "user_1", "username": "user1"},
                "like_count": 1
            },
            {
                "id": "comment_2", 
                "text": "Second comment",
                "timestamp": "2025-05-27T18:00:00+0000",
                "from": {"id": "user_2", "username": "user2"},
                "like_count": 3
            }
        ]
        
        results = batch_transform_comments(
            comments_data, 
            PlatformType.INSTAGRAM,
            include_raw_data=False
        )
        
        assert len(results) == 2
        assert all(isinstance(comment, UnifiedComment) for comment in results)
        assert all(comment.platform == PlatformType.INSTAGRAM for comment in results)
        assert all(comment.raw_data is None for comment in results)
        
    def test_batch_transform_with_errors(self):
        """Test batch transformation with some invalid data"""
        comments_data = [
            {
                "id": "valid_comment",
                "text": "Valid comment",
                "timestamp": "2025-05-27T17:33:45+0000",
                "from": {"id": "user_1", "username": "user1"},
                "like_count": 1
            },
            {
                # Missing required fields - should fail
                "id": "invalid_comment"
            }
        ]
        
        results = batch_transform_comments(
            comments_data,
            PlatformType.INSTAGRAM
        )
        
        # Should only return the valid comment
        assert len(results) == 1
        assert results[0].id == "valid_comment"


class TestErrorHandling:
    """Test error handling in transformations"""
    
    def test_transform_with_missing_fields(self):
        """Test transformation with missing required fields"""
        incomplete_data = {
            "id": "incomplete_comment"
            # Missing text, timestamp, from, etc.
        }
        
        # Should not raise exception but handle gracefully
        try:
            result = transform_instagram_comment_to_unified(incomplete_data)
            # Should have default values for missing fields
            assert result.content == ""
            assert result.like_count == 0
        except Exception:
            # If it does raise an exception, that's also acceptable
            # as long as it's handled in batch processing
            pass
            
    def test_transform_with_invalid_datetime(self):
        """Test transformation with invalid datetime format"""
        invalid_data = {
            "id": "comment_123",
            "text": "Comment with bad date",
            "timestamp": "invalid_date_format",
            "from": {"id": "user_123", "username": "user"},
            "like_count": 1
        }
        
        # Should handle invalid datetime gracefully
        try:
            result = transform_instagram_comment_to_unified(invalid_data)
            # Should either use a default datetime or handle the error
            assert result.id == "comment_123"
        except Exception:
            # Exception is acceptable as long as batch processing handles it
            pass


if __name__ == "__main__":
    pytest.main([__file__])
