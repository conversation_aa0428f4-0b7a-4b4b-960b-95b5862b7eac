#!/usr/bin/env python3
"""
Demo script for the unified comments system.

This script demonstrates how the unified comment system works by showing
sample transformations from Instagram and Facebook comment data to the
unified format.

Run this script after installing dependencies:
pip install pydantic fastapi
"""

import json
from datetime import datetime
from typing import Dict, Any

# Sample Instagram comment data (as received from Instagram API)
SAMPLE_INSTAGRAM_COMMENT = {
    "id": "18067688915055635",
    "text": "This is an amazing Instagram post! 🔥",
    "timestamp": "2025-05-27T17:33:45+0000",
    "from": {
        "id": "17841453087020874",
        "username": "instagram_user"
    },
    "like_count": 15,
    "replies": {
        "data": [
            {
                "id": "17926464957061739",
                "text": "I totally agree!",
                "timestamp": "2025-05-27T17:50:47+0000",
                "username": "reply_user",
                "like_count": 3
            }
        ]
    },
    "media": {
        "id": "18082306612685122",
        "media_type": "IMAGE",
        "media_url": "https://scontent.cdninstagram.com/v/example.jpg",
        "thumbnail_url": "https://scontent.cdninstagram.com/v/thumb.jpg"
    }
}

# Sample Facebook comment data (as received from Facebook API)
SAMPLE_FACEBOOK_COMMENT = {
    "id": "fb_comment_123456789",
    "message": "Great Facebook post! Really enjoyed reading this.",
    "created_time": "2025-05-27T18:15:30+0000",
    "from": {
        "id": "fb_user_987654321",
        "name": "John Doe"
    },
    "like_count": 8,
    "attachment": {
        "type": "photo",
        "url": "https://example.com/attachment.jpg",
        "title": "Shared Photo"
    },
    "reactions": {
        "data": [
            {"type": "LIKE", "id": "reactor_1", "name": "Jane Smith"},
            {"type": "LOVE", "id": "reactor_2", "name": "Bob Johnson"}
        ]
    },
    "comments": {
        "data": [
            {
                "id": "fb_reply_123",
                "message": "Thanks for sharing!",
                "created_time": "2025-05-27T18:30:00+0000",
                "from": {"id": "fb_replier", "name": "Alice Brown"},
                "like_count": 2
            }
        ]
    },
    "is_hidden": False
}

def demo_unified_format():
    """Demonstrate the unified comment format"""
    
    print("=" * 80)
    print("UNIFIED COMMENTS SYSTEM DEMONSTRATION")
    print("=" * 80)
    print()
    
    print("🎯 OBJECTIVE:")
    print("Create a standardized comment schema that consolidates all comment-related")
    print("data from both Instagram and Facebook into a single, consistent format.")
    print()
    
    print("📊 PLATFORM COMPARISON:")
    print()
    
    print("📱 INSTAGRAM API FIELDS:")
    instagram_fields = [
        "id", "text", "timestamp", "from{id, username}", 
        "like_count", "replies{...}", "media{...}", "parent_id"
    ]
    for field in instagram_fields:
        print(f"  • {field}")
    print()
    
    print("📘 FACEBOOK API FIELDS:")
    facebook_fields = [
        "id", "message", "created_time", "from{id, name}",
        "like_count", "attachment{...}", "reactions{...}", 
        "comments{...}", "is_hidden", "parent"
    ]
    for field in facebook_fields:
        print(f"  • {field}")
    print()
    
    print("🔄 UNIFIED SCHEMA BENEFITS:")
    benefits = [
        "Single component can handle both Instagram and Facebook comments",
        "Consistent data structure across all platforms",
        "No loss of platform-specific features or data",
        "Easier frontend development and maintenance",
        "Extensible for future platforms"
    ]
    for benefit in benefits:
        print(f"  ✅ {benefit}")
    print()
    
    print("📋 SAMPLE DATA TRANSFORMATION:")
    print()
    
    print("📱 Instagram Comment (Raw API Response):")
    print(json.dumps(SAMPLE_INSTAGRAM_COMMENT, indent=2))
    print()
    
    print("📘 Facebook Comment (Raw API Response):")
    print(json.dumps(SAMPLE_FACEBOOK_COMMENT, indent=2))
    print()
    
    print("🔄 UNIFIED FORMAT (What frontend receives):")
    unified_example = {
        "comments": [
            {
                "id": "18067688915055635",
                "content": "This is an amazing Instagram post! 🔥",
                "created_time": "2025-05-27T17:33:45+00:00",
                "author": {
                    "id": "17841453087020874",
                    "username": "instagram_user",
                    "name": None
                },
                "platform": "instagram",
                "like_count": 15,
                "reply_count": 1,
                "parent_id": None,
                "replies": [
                    {
                        "id": "17926464957061739",
                        "content": "I totally agree!",
                        "created_time": "2025-05-27T17:50:47+00:00",
                        "author": {"id": "", "username": "reply_user"},
                        "like_count": 3,
                        "platform": "instagram"
                    }
                ],
                "media": {
                    "id": "18082306612685122",
                    "media_type": "image",
                    "media_url": "https://scontent.cdninstagram.com/v/example.jpg",
                    "thumbnail_url": "https://scontent.cdninstagram.com/v/thumb.jpg"
                },
                "attachment": None,
                "reactions": [],
                "is_hidden": False,
                "extra_data": {}
            },
            {
                "id": "fb_comment_123456789",
                "content": "Great Facebook post! Really enjoyed reading this.",
                "created_time": "2025-05-27T18:15:30+00:00",
                "author": {
                    "id": "fb_user_987654321",
                    "username": None,
                    "name": "John Doe"
                },
                "platform": "facebook",
                "like_count": 8,
                "reply_count": 1,
                "parent_id": None,
                "replies": [
                    {
                        "id": "fb_reply_123",
                        "content": "Thanks for sharing!",
                        "created_time": "2025-05-27T18:30:00+00:00",
                        "author": {"id": "fb_replier", "name": "Alice Brown"},
                        "like_count": 2,
                        "platform": "facebook"
                    }
                ],
                "media": None,
                "attachment": {
                    "attachment_type": "photo",
                    "url": "https://example.com/attachment.jpg",
                    "title": "Shared Photo"
                },
                "reactions": [
                    {"reaction_type": "like", "user_id": "reactor_1", "name": "Jane Smith"},
                    {"reaction_type": "love", "user_id": "reactor_2", "name": "Bob Johnson"}
                ],
                "is_hidden": False,
                "extra_data": {}
            }
        ],
        "total": 2,
        "limit": 10,
        "offset": 0,
        "platform": None
    }
    
    print(json.dumps(unified_example, indent=2))
    print()
    
    print("🚀 API ENDPOINTS:")
    endpoints = [
        "GET /api/v1/socials/comments/unified/{scheduled_content_id}",
        "GET /api/v1/socials/comments/unified/{scheduled_content_id}?platform=instagram",
        "GET /api/v1/socials/comments/unified/{scheduled_content_id}/replies/{comment_id}",
        "GET /api/v1/socials/comments/platforms/summary/{scheduled_content_id}"
    ]
    for endpoint in endpoints:
        print(f"  📡 {endpoint}")
    print()
    
    print("💻 FRONTEND USAGE:")
    print("""
    // Before: Platform-specific handling
    if (platform === 'instagram') {
      return <InstagramComment comment={comment} />;
    } else if (platform === 'facebook') {
      return <FacebookComment comment={comment} />;
    }
    
    // After: Unified handling
    return <UnifiedComment comment={comment} />;
    """)
    
    print("🧪 TESTING:")
    print("  • Unit tests for transformation functions")
    print("  • Integration tests for API endpoints")
    print("  • Error handling and edge cases")
    print("  • Performance testing with large datasets")
    print()
    
    print("✅ IMPLEMENTATION COMPLETE!")
    print("The unified comments system is ready for use.")
    print("Frontend developers can now build a single comment component")
    print("that works seamlessly with data from both Instagram and Facebook.")
    print()
    print("=" * 80)

if __name__ == "__main__":
    demo_unified_format()
