from typing import Annotated

import httpx
from dotenv import load_dotenv
from fastapi import APIRouter, Depends, HTTPException, Query, status
from jose import jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.config import settings
from app.database.session import get_db
from app.models.model import SocialMediaAccount
from app.schemas.schema import InitialiseFacebook
from app.utils.dependency import (
    get_current_user,
    verify_organization,
)
from app.utils.logger import get_logger
from app.utils.success_response import fail_response, success_response

# Load environment variables
load_dotenv()

router = APIRouter()

# Logger
logger = get_logger(__name__)


# Linkedin credentials
LINKEDIN_CLIENT_ID = settings.LINKEDIN_CLIENT_ID
LINKEDIN_CLIENT_SECRET = settings.LINKEDIN_CLIENT_SECRET
LINKEDIN_REDIRECT_URI = settings.LINKEDIN_REDIRECT_URI


# LinkedIn OAuth URLs
AUTHORIZATION_URL = "https://www.linkedin.com/oauth/v2/authorization"
LINKEDIN_API_URL = "https://api.linkedin.com/v2"
TOKEN_URL = "https://www.linkedin.com/oauth/v2/accessToken"
USERINFO_URL = "https://api.linkedin.com/v2/userinfo"
JWKS_URI = "https://www.linkedin.com/oauth/openid/jwks"
UGC_POSTS_ENDPOINT = f"{LINKEDIN_API_URL}/ugcPosts"


@router.get("/login")
async def login():
    """
    Redirect the user to LinkedIn's authorization endpoint.
    """
    params = {
        "response_type": "code",
        "client_id": LINKEDIN_CLIENT_ID,
        "redirect_uri": LINKEDIN_REDIRECT_URI,
        "scope": "openid profile email w_member_social",
    }
    auth_url = f"{AUTHORIZATION_URL}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
    return auth_url

@router.get("/callback")
async def callback(code: str = Query(...)):
    """
    Handle LinkedIn's redirect with the authorization code.
    Exchange the code for tokens and validate the ID token.
    """
    async with httpx.AsyncClient() as client:
        # Exchange the authorization code for access and ID tokens
        token_response = await client.post(
            TOKEN_URL,
            data={
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": LINKEDIN_REDIRECT_URI,
                "client_id": LINKEDIN_CLIENT_ID,
                "client_secret": LINKEDIN_CLIENT_SECRET,
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if token_response.status_code != 200:
            raise HTTPException(
                status_code=400, detail="Failed to retrieve tokens"
            )

        tokens = token_response.json()
        id_token = tokens.get("id_token")
        access_token = tokens.get("access_token")

        if not id_token or not access_token:
            raise HTTPException(
                status_code=400, detail="Invalid token response"
            )

        # Fetch JWKS for ID Token validation
        jwks_response = await client.get(JWKS_URI)
        if jwks_response.status_code != 200:
            raise HTTPException(status_code=400, detail="Failed to fetch JWKS")

        jwks = jwks_response.json()
        print(jwks)  # Debugging: Log the JWKS response structure

        # Select the first key from the JWKS (adjust based on your requirements)
        key = next(iter(jwks.get("keys", [])), None)
        if not key:
            raise HTTPException(status_code=400, detail="No keys found in JWKS")

        # Log key details for debugging
        print(f"Selected Key: {key}")

        # Ensure the key has the required fields
        required_fields = {"kty", "e", "n", "kid"}
        if not required_fields.issubset(key):
            raise HTTPException(
                status_code=400,
                detail="Selected key does not contain required fields",
            )

        # Validate the ID Token
        try:
            claims = jwt.decode(id_token, key, audience=LINKEDIN_CLIENT_ID, algorithms=["RS256"])
        except Exception as e:
            raise HTTPException(
                status_code=400, detail=f"ID Token validation failed: {str(e)}"
            )

        return {"message": "Authentication successful", "claims": claims, "access_token": access_token}

@router.post("/initialise_linkedin")
async def connect_to_backend(
    token: Annotated[str, Depends(get_current_user)],
    user_details: InitialiseFacebook,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    try:
        # Check if the social media account already exists
        result = await db_session.execute(
            select(SocialMediaAccount).filter_by(
                platform="facebook",
                social_media_user_id=user_details.social_media_user_id,
            )
        )
        linkedin_social_account = result.scalars().first()

        # TODO: Uncomment this later
        # if linkedin_social_account:
        #     raise HTTPException(
        #         status_code=status.HTTP_409_CONFLICT,
        #         detail="This social media account has already been linked to an organisation",
        #     )

        # await check_permissions(token.get('user_id'), organisation_id, 'can connect socials')

        # logger.info(f'user with social_id {user_details.username} doesn"t exist')
        
        linkedin_social_account = SocialMediaAccount(
            platform="linkedin",
            username=user_details.username,
            social_media_user_id=user_details.social_media_user_id,
            organisation_id=organisation_id,
            access_token=user_details.access_token,
            login_status=True,
        )

        db_session.add(linkedin_social_account)
        await db_session.commit()
        await db_session.refresh(linkedin_social_account)

        # logger.info(
            f"Created social media account: {linkedin_social_account.platform} -- {linkedin_social_account.username}"
        )

        # logger.info("Returning successful response")
        return success_response(
            200,
            "Social media account created successfully",
            {
                "username": linkedin_social_account.username,
                "organisation_id": linkedin_social_account.organisation_id,
                "social_media_user_id": linkedin_social_account.social_media_user_id,
                "platform": linkedin_social_account.platform,
                "login_status": linkedin_social_account.login_status,
            },
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")

@router.get("/profile")
async def get_profile(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
    ):
    """
    Retrieve the user's profile using their access token.
    """
    # check the social profile exists
    result = await db_session.execute(
        select(SocialMediaAccount).filter(
            SocialMediaAccount.platform == "linkedin",
            SocialMediaAccount.organisation_id == organisation_id,
            SocialMediaAccount.access_token is not None,
        )
    )
    db_social = result.scalars().first()
    if not db_social:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A linkedin account has not been connected",
        )
    
    async with httpx.AsyncClient() as client:
        headers = {"Authorization": f"Bearer {db_social.access_token}"}
        profile_response = await client.get(USERINFO_URL, headers=headers)

        if profile_response.status_code != 200:
            raise HTTPException(
                status_code=profile_response.status_code,
                detail="Failed to retrieve profile",
            )

        return profile_response.json()

@router.post("/share_text")
async def share_text(
    text: str, 
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
    ):
    """
    Create a simple text share on LinkedIn.
    """

    # check the social profile exists
    result = await db_session.execute(
        select(SocialMediaAccount).filter(
            SocialMediaAccount.platform == "linkedin",
            SocialMediaAccount.organisation_id == organisation_id,
            SocialMediaAccount.access_token is not None,
        )
    )
    db_social = result.scalars().first()
    if not db_social:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A linkedin account has not been connected",
        )

    headers = {
        "Authorization": f"Bearer {db_social.access_token}",
        "X-Restli-Protocol-Version": "2.0.0"
    }
    
    data = {
        "author": f"urn:li:person:{db_social.social_media_user_id}", 
        "lifecycleState": "PUBLISHED",
        "specificContent": {
            "com.linkedin.ugc.ShareContent": {
                "shareCommentary": {"text": text},
                "shareMediaCategory": "NONE"
            }
        },
        "visibility": {
            "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
        }
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(f"{LINKEDIN_API_URL}/ugcPosts", headers=headers, json=data)

    if response.status_code != 201:
        raise HTTPException(status_code=response.status_code, detail="Failed to share text")

    return {"message": "Text shared successfully", "post_id": response.headers.get("X-RestLi-Id")}

