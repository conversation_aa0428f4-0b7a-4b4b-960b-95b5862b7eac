import asyncio
import base64
import json
import secrets
import time
from datetime import datetime, timedelta, timezone
from typing import Annotated, List, Optional
from urllib.parse import quote, urlencode

import httpx
import psycopg2
import tweepy
from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import RedirectResponse
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from tweepy import Client

from app.core.config import settings
from app.database.session import get_db
from app.models.model import Post, SocialMediaAccount
from app.routes.facebook import download_image_from_url
from app.schemas.schema import (
    X_DM,
    X_DM_CONVERSATION,
    X_DM_WITH,
    CommentResponse,
    InitialiseTwitter,
)
from app.schemas.twitter import (
    CreateDmConversationResponse,
    GetDirectMessagesResponse,
    SendMessageToConversationResponse,
    SendMessageToUserResponse,
)
from app.utils.dependency import (
    create_JWT_Response,
    get_challenge,
    get_social_details,
    verify_organization,
)
from app.utils.logger import get_logger
from app.utils.redis_cache import redis_client
from app.utils.success_response import fail_response, success_response

logger = get_logger(__name__)
router = APIRouter()

oauth2_handlers = {}

bearer_token = settings.TWITTER_BEARER_TOKEN
consumer_key = settings.TWITTER_API_KEY
consumer_secret = settings.TWITTER_API_SECRET_KEY

timeout = httpx.Timeout(20.0, connect=20.0, read=25.0, write=30.0)
code_challenge = get_challenge()


if not redis_client.ping():
    raise ConnectionError("Unable to connect to Redis.")
# logger.info("Connected to Redis successfully for twitter.")

# Base64 encoded client credentials
Token = base64.b64encode(
    f"{settings.TWITTER_CLIENT_ID}:{settings.TWITTER_CLIENT_SECRET}".encode()
).decode()

callback_url = settings.TWITTER_CALLBACK_URL

redis_expiry_time = timedelta(minutes=120)


async def store_oauth_state(state: str) -> None:
    """Store OAtuh state in Redis"""
    oauth_state = {"state": state, "created_at": time.time()}
    # serialize the handler using pickle
    serialized_data = json.dumps(oauth_state)
    # store the state in redis with 10 minute expiry
    redis_client.setex(
        f"twitter_oauth_state:{state}",
        timedelta(minutes=10),
        serialized_data
    )


async def get_oauth_state(state: str):
    """Retrieve the stored oauth state"""
    # logger.info("Decoding the state data")
    data = redis_client.get(f"twitter_oauth_state:{state}")
    if not data:
        return None, "Invalid state parameter"

    try:
        oauth_state = json.loads(data)
        # check if state is still within timeframe
        if time.time() - oauth_state.get("created_at") > 600:
            redis_client.delete(f"twitter_oauth_state:{state}")
            return None, "Expired state parameter"
        return oauth_state, "Valid state parameter"
    except Exception as e:
        logger.error(f"Error deserializing OAuth state: {e}")
        return None, "Error deserializing OAuth state"


async def refresh_and_update_token(
    db_session: AsyncSession, organisation_id: str, refresh_token: str
):
    """
    Refresh the Twitter access token and update it in the database.
    """
    # logger.info(f"Refreshing access token for organisation: {organisation_id}")
    try:
        header = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {Token}"
        }
        form_data = {
            "refresh_token": refresh_token,
            "grant_type": "refresh_token",
            "client_id": settings.TWITTER_CLIENT_ID
        }

        # Make the request to refresh the token
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                "https://api.x.com/2/oauth2/token",
                headers=header,
                data=form_data
            )
        response.raise_for_status()
        token_data = response.json()

        # Extract the new tokens
        new_access_token = token_data.get("access_token")
        new_refresh_token = token_data.get("refresh_token", refresh_token)

        # Update the token in the database
        social_account = await db_session.execute(
            select(SocialMediaAccount).filter_by(
                organisation_id=organisation_id, platform="twitter"
            )
        )
        social_account = social_account.scalars().first()

        if not social_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Social media account not found.",
            )

        social_account.access_token = new_access_token
        social_account.refresh_token = new_refresh_token
        await db_session.commit()
        # logger.info("Access token updated successfully.")
        cache_key = f"x-access-token:{social_account.id}"

        redis_client.setex(cache_key, redis_expiry_time, json.dumps(new_access_token))
        return new_access_token

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
        raise
    except httpx.RequestError as e:
        logger.error(f"Request error: {str(e)}")
        raise
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise


async def get_valid_access_token(
    social_account: SocialMediaAccount, db_session: AsyncSession
) -> str:
    """
    Validates the current access token or refreshes it if expired.
    """
    # logger.info("Validating or refreshing access token...")
    # get the access token from redis cache if still there
    cache_key = f"x-access-token:{social_account.id}"
    cache_data = redis_client.get(cache_key)
    if cache_data:
        # logger.info("access token retrieved")
        return json.loads(cache_data)

    try:
        # Validate the access token
        client = tweepy.Client(bearer_token=social_account.access_token)
        user = client.get_me(user_auth=False)
        if user:
            # logger.info("Access token is valid.")
            redis_client.setex(cache_key, redis_expiry_time, json.dumps(social_account.access_token))
            # logger.info('returning access token')
            return social_account.access_token
    except tweepy.Unauthorized:
        logger.warning("Access token is invalid or expired. Attempting to refresh...")
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error validating access token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate access token",
        )

    return await refresh_and_update_token(
        db_session, social_account.organisation_id, social_account.refresh_token
    )


async def get_users_tweets_cached(
    organisation_id: str,
    client,
    db_social_id: str,
    start_date: datetime = None,
    end_date: datetime = None
):
    try:
        # check if the data is cached
        # logger.info('retrieving data from get_users_tweets_cached')
        cache_key = f"x_users_tweets:{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("Data available in the cache")
            return json.loads(cache_data)

        # logger.info("Data not available in the cache")
        tweets_response = client.get_users_tweets(
            id=db_social_id,
            expansions=["attachments.media_keys"],
            start_time=start_date.strftime("%Y-%m-%dT%H:%M:%SZ") if start_date else None,
            end_time=end_date.strftime("%Y-%m-%dT%H:%M:%SZ") if end_date else None,
            tweet_fields=[
                "created_at", "author_id", "text",
                "public_metrics",
                "non_public_metrics",
                "organic_metrics",
            ],
            media_fields=[
                "duration_ms", "media_key",
                "public_metrics",
                "preview_image_url",
                "type", "url"
            ],
            user_fields=[
                "location", "name",
                "username", "public_metrics"
            ],
            place_fields=[
                "country", "id",
                "full_name", "country_code",
                "geo", "name"
            ],
            max_results=100,
            user_auth=False,
        )

        # logger.info('returning the response from X')

        # Convert `tweepy.Response` to a JSON-serializable dictionary
        response_dict = {
            "data": [tweet.data for tweet in tweets_response.data] if tweets_response.data else [],
            "includes": {key: [obj.data for obj in value] for key, value in tweets_response.includes.items()} if tweets_response.includes else {},
            "errors": tweets_response.errors if tweets_response.errors else [],
            "meta": tweets_response.meta if tweets_response.meta else {}
        }
        # logger.info(response_dict)
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(response_dict))
        return response_dict
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail='an unexpected error occurred')


# merge the metrics into one metric response
def merge_metrics(tweet, media_dict):
    combined_metrics = {}
    # logger.info(f"formatting tweet data begins: {tweet} {media_dict}")
    for metrics in [tweet.get("public_metrics", {}), tweet.get("non_public_metrics", {}), tweet.get("organic_metrics", {})]:
        for key, value in metrics.items():
            if key not in combined_metrics:
                combined_metrics[key] = value
    # extract media
    # logger.info("Extracting image data from tweets")
    media_urls = []
    attachments = tweet.get("attachments", {})
    if attachments:
        media_keys = attachments.get("media_keys", [])
        # logger.info("adding image data to tweet")

        for key in media_keys:
            if key in media_dict:
                media_urls.append(media_dict[key])

    return {
        # "post_id": tweet["id"],
        "text": tweet["text"],
        "created_at": tweet["created_at"],
        "metrics": combined_metrics,
        "media": media_urls if media_urls else None
    }


def extract_images_from_response(tweet, media_dict):
    # extract media
    # logger.info("Extracting image data from tweets")
    media_urls = []
    attachments = tweet.get("attachments", {})
    if attachments:
        media_keys = attachments.get("media_keys", [])
        # logger.info("adding image data to tweet")

        for key in media_keys:
            if key in media_dict:
                media_urls.append(media_dict[key])
                tweet[key] = media_dict[key]
    return media_urls


async def upload_image_to_X(image, access_token):
    '''upload images to X'''
    try:
        # download image
        image_data = await download_image_from_url(image)
        filename, img_bytes, content_type = image_data["file"]

        size = len(img_bytes.getvalue())
        # logger.info(f"File: {filename}, Size: {size}, Content-Type: {content_type}")

        async with httpx.AsyncClient(timeout=timeout) as client:
            # step 1: INIT
            init_response = await client.post(
                f"{settings.TWITTER_IMAGE_URL}",
                params={
                    "command": "INIT",
                    "total_bytes": str(size),
                    "media_type": content_type
                },
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Authorization": f"Bearer {access_token}"
                }
            )
            init_response.raise_for_status()

            init_data = init_response.json()
            media_id = init_data.get("data")['id']
            # logger.info(f"INIT response: {init_data}")

            # step 2: APPEND - Upload the media binary data
            append_response = await client.post(
                f"{settings.TWITTER_IMAGE_URL}",
                params={
                    "command": "APPEND",
                    "media_id": f"{media_id}",
                    "segment_index": 0
                },
                headers={
                    "Content-Type": "multipart/form-data",
                    "Authorization": f"Bearer {access_token}"
                },
                files={
                    "media": image_data["file"]
                }
            )
            append_response.raise_for_status()
            # logger.info(f"APPEND response: {append_response.json()}")

            # step 3: FINALIZE
            finalize_response = await client.post(
                f"{settings.TWITTER_IMAGE_URL}",
                params={
                    "command": "FINALIZE",
                    "media_id": media_id
                },
                headers={
                    "Content-Type": "multipart/form-data",
                    "Authorization": f"Bearer {access_token}"
                }
            )
            finalize_response.raise_for_status()
            finalize_data = finalize_response.json()
            # logger.info(f"FINALIZE response: {finalize_data}")

            # if procssing data is present, check respones till successful
            processing_info = finalize_data.get("processing_info")
            if processing_info:
                state = processing_info.get("state")
                while state != "succeeded":
                    ttw = processing_info.get("check_after_secs", 5)
                    await asyncio.sleep(ttw)
                    status_response = await client.get(
                        f"{settings.TWITTER_IMAGE_URL}",
                        params={
                            "command": "STATUS",
                            "media_id": media_id
                        },
                        headers={
                            "Authorization": f"Bearer {access_token}"
                        }
                    )
                    status_response.raise_for_status()
                    status_data = status_response.json()
                    processing_info = status_data.get("processing_info")
                    state = processing_info.get("state")
                    # logger.info(f"Media processing status: {processing_info}")
                return media_id
            else:
                return media_id
    except Exception as e:
        # logger.info(f"An error occurred while uploading images: {str(e)}")
        raise


scope = [
    "tweet.read",
    "tweet.write",
    "tweet.moderate.write",
    "users.read",
    "offline.access",
    "follows.read",
    "follows.write",
    "space.read",
    "mute.read",
    "mute.write",
    "like.read",
    "like.write",
    "list.read",
    "list.write",
    "block.read",
    "block.write",
    "bookmark.read",
    "bookmark.write",
    "media.write",
    "dm.read",
    # "post.read",
    # "user.read",
]


@router.get("/login")
async def login_to_twitter():
    """Create a login session to login to twitter"""
    try:
        # create the auth url
        state = secrets.token_urlsafe(32)
        params = {
            "response_type": "code",
            "client_id": settings.TWITTER_CLIENT_ID,
            "redirect_uri": callback_url,
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "scope": " ".join(scope),
        }

        # save the state to the redis db
        await store_oauth_state(state)

        base_url = "https://x.com/i/oauth2/authorize"
        query_string = urlencode(params, quote_via=quote)

        authorization_url = f"{base_url}?{query_string}"
        # logger.info(f"Auth url: {authorization_url}")
        return {"authorization_url": authorization_url}

    except Exception as e:
        logger.error(f"An error occurred: {e}")


@router.get("/callback")
async def callback_from_twitter(request: Request):
    """Process the data received from twitter callback"""
    try:
        state = request.query_params.get("state")
        code = request.query_params.get("code")
        error = request.query_params.get("error")
        error_description = request.query_params.get("error_description")

        if error:
            # logger.info(f"An error occurred: {error_description}")
            redirect_url = (
                f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
                f"error={error} {error_description}&social_type=twitter"
            )
            return RedirectResponse(
                url=redirect_url,
                status_code=status.HTTP_302_FOUND
            )

        # get the state from redis
        redis_data, msg = await get_oauth_state(state)
        if not redis_data:
            redirect_url = (
                f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
                f"error={msg}&social_type=twitter"
            )
            return RedirectResponse(
                url=redirect_url,
                status_code=status.HTTP_302_FOUND
            )

        if not code:
            redirect_url = (
                f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
                f"error={error} {error_description}&social_type=twitter"
            )
            return RedirectResponse(
                url=redirect_url,
                status_code=status.HTTP_302_FOUND
            )

        redis_client.delete(f"twitter_oauth_state:{state}")
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                "https://api.x.com/2/oauth2/token",
                data={
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": callback_url,
                    "code_verifier": "ellumai"
                },
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Authorization": f"Basic {Token}"
                }
            )
            response.raise_for_status()
            data = response.json()

            # get the user details from X
            client = Client(bearer_token=data.get("access_token"))
            response = client.get_me(
                user_fields=["created_at", "description", "username"], user_auth=False
            )
            # logger.info(f"User response: {response}")
            user_response = {
                "description": response[0]["description"],
                "id": str(response[0]["id"]),
                "name": response[0]["name"],
                "username": response[0]["username"]
            }

            response = {
                "message": "Twitter account linked successfully!",
                "access_token": data.get("access_token"),
                "refresh_token": data.get("refresh_token"),
                "social_type": "twitter",
                "user_details": user_response,
            }
            # get the JWT encoded version
            encoded_response = await create_JWT_Response(response)
            return RedirectResponse(
                url=f"{settings.FRONTEND_SOCIALS_ENDPOINT}?token={encoded_response}",
                status_code=status.HTTP_302_FOUND
            )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Error completing authentication: {e}")
        return fail_response(500, "Error completing authentication")


@router.post("/connect_twitter")
async def connect_twitter_to_backend(
    user_details: InitialiseTwitter,
    # token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Link a Twitter social media platform."""
    try:
        # await check_permissions(token.get('user_id'), organisation_id, 'can connect socials')

        # Create a new social media account entry
        db_user = SocialMediaAccount(
            organisation_id=organisation_id,
            platform="twitter",
            username=user_details.username,
            access_token=user_details.access_token,
            refresh_token=user_details.refresh_token,
            social_media_user_id=user_details.social_media_user_id,
            login_status=True,
        )
        db_session.add(db_user)
        await db_session.commit()
        await db_session.refresh(db_user)

        # Schedule immediate metrics collection in the background
        # We'll use asyncio.create_task to run this in the background without blocking
        asyncio.create_task(
            update_twitter_metrics_immediately(db_user.id)
        )

        # Return a success response
        return success_response(
            200,
            "Account linked successfully",
            {
                "username": db_user.username,
                "organisation_id": db_user.organisation_id,
                "social_id": db_user.social_media_user_id,
                "platform": db_user.platform,
                "login_status": db_user.login_status,
            },
        )
    except IntegrityError as e:
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):
            await db_session.rollback()
            logger.error(f"UniqueViolationError: {str(e)}")
            return fail_response(409, "This social media account has already been linked to an organisation")
        elif getattr(e.orig, 'pgcode', None) == '23505':
            await db_session.rollback()
            logger.error(f"UniqueViolationError: {str(e)}")
            return fail_response(409, "This social media account has already been linked to an organisation")
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error linking account: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/me")
async def get_me(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Get the authenticated user's details."""
    try:
        user_account = await get_social_details(organisation_id, "twitter", db_session)

        # check if the data is in the cache
        cache_key = f"x_user_detail_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("data is available in the cache")
            return success_response(
                200,
                "Authenticated user's details retrieved successfully",
                json.loads(cache_data),
            )

        # logger.info("data not available in cache")
        access_token = await get_valid_access_token(user_account, db_session)
        client = Client(bearer_token=access_token)
        response = client.get_me(
            expansions=["pinned_tweet_id"],
            tweet_fields=["attachments", "author_id", "created_at", "text"],
            user_fields=["created_at", "description", "username"],
            user_auth=False,
        )

        # save it to the cache
        user_data = {
            "id": response.data.id,
            "name": response.data.name,
            "username": response.data.username,
            "created_at": response.data.created_at.isoformat(),
            "description": response.data.description,
            "pinned_tweet_id": response.includes["tweets"][0].id
            if "tweets" in response.includes
            else None,
            "pinned_tweet_text": response.includes["tweets"][0].text
            if "tweets" in response.includes
            else None,
        }
        # logger.info("saving to redis cache")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(user_data))
        return success_response(
            200,
            "Authenticated user's details retrieved successfully",
            user_data,
        )
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"An unexpected error occurred: {str(e)}",
        )


# ### TWEETS ENDPOINTS ####
# @router.post("/post-tweet")
async def post_tweet(
    tweet: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    schedulecontent_id: str,
    img_urls: Optional[List[str]] = None,
    db_session: AsyncSession = Depends(get_db),
):
    """Make a post for a specific organisation"""
    try:
        # Get the organisation SocialMediaAccount credentials
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # upload images to X
        media_keys = []
        # if img_urls:
        #     for img in img_urls:
        #         res = await upload_image_to_X(img, access_token)
        #         media_keys.append(res)

        # Initialize the client for the authenticated account
        # logger.info("making call to X from tweepy")
        client = tweepy.Client(bearer_token=access_token)
        response = client.create_tweet(
            text=tweet,
            # media_ids=media_keys,
            user_auth=False
        )

        # save to the db
        # logger.info("tweet made to X, saving to db")
        db_post = Post(
            social_media_account_id=db_social.id,
            post_id=response.data["id"],
            schedulecontent_id=schedulecontent_id,
        )
        db_session.add(db_post)
        await db_session.commit()
        # logger.info("saved tweet to db, generating response")
        await db_session.refresh(db_post)
        post_data = {
            "id": db_post.id,
            "post_id": db_post.post_id,
            "schedulecontent_id": db_post.schedulecontent_id,
        }
        return success_response(
            200,
            "Tweet posted successfully",
            post_data,
        )
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.response.json()['detail']
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"An unexpected error occurred: {str(e)}",
        )


# @router.delete("/{tweet_id}")
async def delete_tweet(
    tweet_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Delete a tweet."""
    try:
        db_social = await get_social_details(organisation_id, "twitter", db_session)
        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account and get data from db
        result = await db_session.execute(
            select(Post)
            .filter_by(
                post_id=tweet_id
            )
        )
        db_tweet = result.scalars().first()
        if not db_tweet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested tweet not found"
            )
        client = tweepy.Client(bearer_token=access_token)
        response = client.delete_tweet(id=tweet_id, user_auth=False)

        if db_tweet:
            db_session.delete(db_tweet)
            await db_session.commit()
        return success_response(
            200, "Tweet deleted successfully", response.data["deleted"]
        )
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"An unexpected error occurred: {str(e)}",
        )


# Function to update Twitter metrics immediately after connecting an account
async def update_twitter_metrics_immediately(account_id: str):
    """
    Update metrics for a Twitter account immediately after it's connected
    This function runs in the background and doesn't block the API response
    """
    # logger.info(f"Starting immediate metrics collection for Twitter account ID: {account_id}")

    try:
        # Import here to avoid circular imports
        from app.tasks.twitter_metrics import (
            fetch_and_store_twitter_metrics_for_account,
        )

        # Call the task function to update metrics for this specific account
        await fetch_and_store_twitter_metrics_for_account(account_id)

        # logger.info(f"Successfully scheduled metrics update for Twitter account ID: {account_id}")

    except Exception as e:
        logger.error(f"Error updating metrics for Twitter account ID {account_id}: {str(e)}")

# ######### TWEET METRICS ###########

@router.get("/{tweet_id}/tweet")
async def get_tweet(
    tweet_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Get a single tweet and its metrics."""
    try:
        db_social = await get_social_details(organisation_id, "twitter", db_session)

        # Check if the tweet exists in the database
        results = await db_session.execute(select(Post).filter_by(post_id=tweet_id))
        db_tweet = results.scalars().first()

        if not db_tweet:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tweet not found in the database.",
            )

        cache_key = f"x_tweet_{organisation_id}_{tweet_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("Data available in the cache")
            return success_response(
                200,
                "tweet details returned successfully",
                json.loads(cache_data),
            )

        # logger.info("Data not available in the cache")
        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)
        client = tweepy.Client(bearer_token=access_token)

        # Fetch tweet details
        # logger.info("fetching tweet data")
        tweet_response = client.get_tweet(
            id=tweet_id,
            expansions=["attachments.media_keys", "geo.place_id"],
            tweet_fields=["created_at", "public_metrics", "note_tweet", "non_public_metrics", "organic_metrics"],
            media_fields=[
                "duration_ms", "media_key",
                "public_metrics",
                "preview_image_url",
                "type", "url"
            ],
            user_fields=[
                "location", "name",
                "username", "public_metrics"
            ],
            place_fields=[
                "country", "id",
                "full_name", "country_code",
                "geo", "name"
            ],
            user_auth=False,
        )

        if not tweet_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tweet not found on Twitter.",
            )

        tweet_data = tweet_response.data
        # logger.info("tweet_data available")

        media_dict = {
            m["media_key"]: {
                "type": m["type"],
                "url": m["url"]
            } for m in tweet_response.includes.get("media", [])
        }
        # logger.info("formatting the tweet data metrics")
        tweet_detail = merge_metrics(tweet_data, media_dict)

        # Fetch location demographics
        # logger.info("fetching the liking users data for demographics")
        liking_users_response = client.get_liking_users(
            id=tweet_id, user_fields=["location"], user_auth=False
        )
        location_counts = {}
        liking_users = liking_users_response.data
        if liking_users:
            for user in liking_users:
                location = user.location

                if location:
                    location_counts[location] = location_counts.get(location, 0) + 1

        tweet_detail["location_demographic"] = location_counts
        tweet_detail["created_at"] = tweet_detail["created_at"].isoformat()

        # logger.info(tweet_detail)
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(tweet_detail))
        return success_response(
            200, "Tweet and metrics retrieved successfully", tweet_detail
        )
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.get("/overview")
async def get_overview(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Get an overview of followers, engagements, impressions, and reach over the last 3 months."""
    try:
        # Check if data is in Redis cache
        cache_key = f"x_overview_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("Data is available in the cache")
            return success_response(
                200,
                "Overview retrieved successfully",
                json.loads(cache_data),
            )

        # logger.info("Data not available in cache, fetching from database")

        # We'll skip the database query for now since the table doesn't exist yet
        # This will be fixed when the migration is properly applied
        metrics = None
        # logger.info("Skipping database query for now")

        if metrics:
            # Format the data
            overview_data = {
                "followers": metrics.followers_count,
                "engagements": metrics.engagements,
                "impressions": metrics.impressions,
                "reach": metrics.reach,
                "updated_at": metrics.collected_at.isoformat() if metrics.collected_at else None
            }

            # Cache the data
            # logger.info("Saving to Redis cache")
            redis_client.setex(cache_key, redis_expiry_time, json.dumps(overview_data))

            return success_response(
                200,
                "Overview retrieved successfully",
                overview_data,
            )

        # If no data in database, fall back to the Twitter API
        # logger.info("No data in database, falling back to Twitter API")
        db_social = await get_social_details(organisation_id, "twitter", db_session)
        access_token = await get_valid_access_token(db_social, db_session)
        client = tweepy.Client(bearer_token=access_token)

        # Calculate the date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - relativedelta(months=3)

        # logger.info('Getting the followers count')
        # Fetch followers count
        user_response = client.get_me(
            user_fields=["public_metrics"],
            user_auth=False,
        )
        # logger.info(user_response)
        followers_count = user_response.data.public_metrics["followers_count"]

        # Fetch tweets within the date range
        tweets_response = await get_users_tweets_cached(
            organisation_id,
            client,
            db_social.social_media_user_id,
            start_date,
            end_date
        )

        # Aggregate metrics
        engagements = 0
        impressions = 0
        reach = 0
        for tweet in tweets_response.get("data"):
            engagements += tweet.get("public_metrics").get("like_count", 0)
            engagements += tweet.get("public_metrics").get("retweet_count", 0)
            engagements += tweet.get("public_metrics").get("reply_count", 0)
            impressions += tweet.get("public_metrics").get("impression_count", 0)
            reach += tweet.get("organic_metrics").get("impression_count", 0)
        overview_data = {
            "followers": followers_count,
            "engagements": engagements,
            "impressions": impressions,
            "reach": reach,
            "updated_at": datetime.now().isoformat()
        }
        # logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(overview_data))

        # Skip saving to database for now since the table doesn't exist yet
        # This will be fixed when the migration is properly applied
        # logger.info("Skipping saving to database for now")

        return success_response(
            200,
            "Overview retrieved successfully",
            overview_data,
        )
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.get("/top_tweets")
async def get_top_performing_contents(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    limit: int = 5,
):
    """
    Retrieve the top-performing tweets based on engagement metrics.
    """
    try:
        # Check if data is in Redis cache
        cache_key = f"x_top_contents_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("Data is available in the cache")
            return success_response(
                200,
                "Top-performing tweets retrieved successfully.",
                json.loads(cache_data),
            )

        # logger.info("Data not available in cache, fetching from database")

        # We'll skip the database query for now since the table doesn't exist yet
        # This will be fixed when the migration is properly applied
        top_tweets = []
        # logger.info("Skipping database query for now")

        if top_tweets:
            # Format the data
            formatted_response = []
            for tweet in top_tweets:
                tweet_data = {
                    "text": tweet.text,
                    "created_at": tweet.tweet_created_at.isoformat() if tweet.tweet_created_at else None,
                    "metrics": json.loads(tweet.metrics) if tweet.metrics else {
                        "likes": tweet.likes,
                        "retweets": tweet.retweets,
                        "replies": tweet.replies,
                        "impressions": tweet.impressions,
                        "url_clicks": tweet.url_clicks,
                        "profile_clicks": tweet.profile_clicks,
                        "engagement_rate": tweet.engagement_rate
                    },
                    "media": json.loads(tweet.media) if tweet.media else None
                }
                formatted_response.append(tweet_data)

            # Cache the data
            # logger.info("Saving to Redis cache")
            redis_client.setex(cache_key, redis_expiry_time, json.dumps(formatted_response))

            return success_response(
                200,
                "Top-performing tweets retrieved successfully.",
                formatted_response,
            )

        # If no data in database, fall back to the Twitter API
        # logger.info("No data in database, falling back to Twitter API")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        client = tweepy.Client(bearer_token=access_token)

        # Retrieve the account's tweets
        # logger.info('Retrieving the top contents from Twitter API')
        response = await get_users_tweets_cached(
            organisation_id,
            client,
            db_social.social_media_user_id,
        )

        # Check if response contains data
        if not response:
            return success_response(200, "No tweets found for this account.", [])

        # Sort tweets by engagement (likes + retweets + replies)
        tweets = sorted(
            response.get("data"),
            key=lambda tweet: (
                tweet.get("public_metrics").get("like_count", 0)
                + tweet.get("public_metrics").get("retweet_count", 0)
                + tweet.get("public_metrics").get("reply_count", 0)
                + tweet.get("public_metrics").get("impression_count", 0)
                + tweet.get("non_public_metrics", {}).get("impression_count", 0)
                + tweet.get("non_public_metrics", {}).get("url_link_clicks", 0)
                + tweet.get("non_public_metrics", {}).get("user_profile_clicks", 0)
                + tweet.get("organic_metrics", {}).get("impression_count", 0)
                + tweet.get("organic_metrics", {}).get("reply_count", 0)
                + tweet.get("organic_metrics", {}).get("retweet_count", 0)
                + tweet.get("organic_metrics", {}).get("like_count", 0)
                + tweet.get("promoted_metrics", {}).get("impressions_count", 0)
                + tweet.get("promoted_metrics", {}).get("like_count", 0)
                + tweet.get("promoted_metrics", {}).get("reply_count", 0)
                + tweet.get("promoted_metrics", {}).get("retweet_count", 0)
            ),
            reverse=True,
        )

        # Select the top-performing tweets
        top_tweets = tweets[:limit]

        media_dict = {
            m["media_key"]: {
                "type": m["type"],
                "url": m["url"]
            } for m in response.get("includes", {}).get("media", [])
        }

        formatted_response = [merge_metrics(metrics, media_dict) for metrics in top_tweets]

        # logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(formatted_response))

        # Skip saving to database for now since the table doesn't exist yet
        # This will be fixed when the migration is properly applied
        # logger.info("Skipping saving to database for now")

        return success_response(
            200,
            "Top-performing tweets retrieved successfully.",
            formatted_response,
        )

    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            "An unexpected error occurred",
        )


@router.get("/post-impressions")
async def get_post_impressions(
    organisation_id: Annotated[str, Depends(verify_organization)],
    pagination_token: Optional[str] = None,
    months: int = 3,
    db_session: AsyncSession = Depends(get_db),
):
    """Get post impressions over a specified number of months. Default is 3 months"""
    try:
        # Get the organisation SocialMediaAccount credentials
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        cache_key = f"x_post_impressions_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("data is available in the cache")
            return success_response(
                200, "Post impressions retrieved successfully.", json.loads(cache_data)
            )

        # logger.info("data not available in cache")
        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        client = tweepy.Client(bearer_token=access_token)

        # Calculate the date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - relativedelta(months=months)

        # Fetch tweets within the date range
        response = await get_users_tweets_cached(
            organisation_id,
            client,
            db_social.social_media_user_id,
            start_date,
            end_date
        )

        # Check if response contains data
        if not response:
            return success_response(200, "No tweets found for this account.", [])

        # Calculate impressions for each month
        impressions_data = {}
        for tweet in response.get("data", []):
            tweet_date = datetime.strptime(tweet.get("created_at"), "%Y-%m-%dT%H:%M:%S.%fZ").strftime("%Y-%m")
            impressions = tweet.get("public_metrics").get("impression_count", 0)
            if tweet_date in impressions_data:
                impressions_data[tweet_date] += impressions
            else:
                impressions_data[tweet_date] = impressions

        # Format the data
        formatted_data = [
            {"month": month, "impressions": impressions}
            for month, impressions in sorted(impressions_data.items())
        ]

        # logger.info("saving to redis cache")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(formatted_data))
        return success_response(
            200,
            "Post impressions retrieved successfully.",
            formatted_data
        )

    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.get("/post-engagement")
async def get_post_engagement(
    organisation_id: Annotated[str, Depends(verify_organization)],
    pagination_token: Optional[str] = None,
    months: int = 3,
    db_session: AsyncSession = Depends(get_db),
):
    """Get post engagement over a specified number of months. Default is 3 months"""
    try:
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting data for post engagements")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )
        # check if data is in cache
        cache_key = f"x_post_engagement_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("data is available in the cache")
            return success_response(
                200, "Post engagements retrieved successfully.", json.loads(cache_data)
            )

        # Validate and refresh access token if necessary
        # logger.info("not available in cache")
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        client = tweepy.Client(bearer_token=access_token)

        # Calculate the date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - relativedelta(months=months)

        # Fetch tweets within the date range
        response = await get_users_tweets_cached(
            organisation_id,
            client,
            db_social.social_media_user_id,
            start_date,
            end_date
        )

        # Check if response contains data
        # logger.info(f"response: {response}")
        if not response:
            return success_response(200, "No tweets found for this account.", [])

        # Calculate engagements for each month
        # logger.info('response found')
        engagements_data = {}

        for tweet in response.get("data", []):
            tweet_date = datetime.strptime(tweet.get("created_at"), "%Y-%m-%dT%H:%M:%S.%fZ").strftime("%Y-%m")
            engagements = (
                tweet.get("public_metrics").get("like_count", 0)
                + tweet.get("public_metrics").get("retweet_count", 0)
                + tweet.get("public_metrics").get("reply_count", 0)
            )
            if tweet_date in engagements_data:
                engagements_data[tweet_date] += engagements
            else:
                engagements_data[tweet_date] = engagements

        # Format the data
        # logger.info('formatting the data')
        formatted_data = [
            {"month": month, "engagements": engagements}
            for month, engagements in sorted(engagements_data.items())
        ]

        # logger.info("saving to redis cache")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(formatted_data))
        return success_response(
            200,
            "Post engagements retrieved successfully.",
            formatted_data
        )

    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.get("/click-rate")
async def get_click_rate(
    organisation_id: Annotated[str, Depends(verify_organization)],
    pagination_token: Optional[str] = None,
    months: int = 3,
    db_session: AsyncSession = Depends(get_db),
):
    """Get click rate over a specified number of months. Default is 3 months"""
    try:
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting social details and valid access token")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # check if the data is available in the cache
        cache_key = f"x_click_rate_{organisation_id}_{months}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("data is available in the cache")
            return success_response(
                200, "Click rate retrieved successfully.", json.loads(cache_data)
            )

        # if not, fetch the data from the API
        # logger.info("data is not available in the cache")

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)
        # Initialize the client for the authenticated account

        client = tweepy.Client(bearer_token=access_token)

        # Calculate the date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - relativedelta(months=months)
        start_date_str = start_date.strftime("%Y-%m-%dT%H:%M:%SZ")
        end_date_str = end_date.strftime("%Y-%m-%dT%H:%M:%SZ")

        # logger.info(f"fetching users tweets from {start_date_str} to {end_date_str}")
        # Fetch tweets within the date range
        response = await get_users_tweets_cached(
            organisation_id,
            client,
            db_social.social_media_user_id,
            start_date,
            end_date
        )

        # Check if response contains data
        if not response:
            return success_response(200, "No tweets found for this account.", [])

        # Calculate link clicks for each month
        # logger.info("calculating the click rate for each socials media post")
        click_rate_data = {}
        for tweet in response.get("data"):
            tweet_date = datetime.strptime(tweet.get("created_at"), "%Y-%m-%dT%H:%M:%S.%fZ").strftime("%Y-%m")
            link_clicks = tweet.get("public_metrics").get("url_link_clicks", 0)
            impressions = tweet.get("public_metrics").get("impression_count", 0)
            if tweet_date in click_rate_data:
                click_rate_data[tweet_date]["clicks"] += link_clicks
                click_rate_data[tweet_date]["impressions"] += impressions
            else:
                click_rate_data[tweet_date] = {
                    "clicks": link_clicks,
                    "impressions": impressions,
                }

        # Format the data
        # logger.info("formatting the click rate data")
        formatted_data = [
            {
                "month": month,
                "click_rate": (data["clicks"] / data["impressions"] * 100)
                if data["impressions"] > 0
                else 0,
            }
            for month, data in sorted(click_rate_data.items())
        ]
        # save it to the redis cache
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(formatted_data))
        return success_response(
            200,
            "Post click rates retrieved successfully.",
            formatted_data
        )

    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


# @router.get("/tweets_all")
# async def get_all_tweets(
#     organisation_id: Annotated[str, Depends(verify_organization)],
#     max_results: int = 10,
#     db_session: AsyncSession = Depends(get_db),
# ):
#     """
#     Retrieve all tweets for the authenticated account linked to the given organisation.
#     """
#     try:
#         # Get the organisation SocialMediaAccount credentials
#         db_social = await get_social_details(
#             organisation_id=organisation_id,
#             platform_name="twitter",
#             db_session=db_session,
#         )
#         # check if the data is in the cache
#         cache_key = f"x_get_tweets_{organisation_id}"
#         cache_data = redis_client.get(cache_key)
#         if cache_data:
#             # logger.info("data is available in the cache")
#             return success_response(
#                 200,
#                 "Tweets retrieved successfully.",
#                 json.loads(cache_data),
#             )

#         # logger.info("data not available in cache")
#         # Validate and refresh access token if necessary
#         access_token = await get_valid_access_token(db_social, db_session)

#         # get the tweets available from the app
#         db_tweets = db_session.query(Post).join(SocialMediaAccount).filter_by(social_media_account_id=db_social.id).all()

#         if not db_tweets:
#             return success_response(200, "Tweets retrieved", [])

#         # for tweet in db_tweets:
#         #     if tweet.post_id

#         # Initialize the client for the authenticated account
#         client = tweepy.Client(bearer_token=access_token)

#         # Retrieve the account's tweets
#         response = client.get_users_tweets(
#             id=db_social.social_media_user_id,
#             exclude=["retweets", "replies"],
#             max_results=max_results,
#             tweet_fields=["created_at", "text", "author_id", "public_metrics"],
#         )

#         # Check if response contains data
#         if not response.data:
#             return success_response(200, "No tweets found for this account.", [])

#         # Format the data
#         tweets = [
#             {
#                 "id": tweet.id,
#                 "text": tweet.text,
#                 "created_at": tweet.created_at,
#                 "public_metrics": tweet.public_metrics,
#             }
#             for tweet in response.data
#         ]

#         # logger.info("saving to redis cache")
#         redis_client.setex(cache_key, redis_expiry_time, json.dumps(tweets))
#         return success_response(200, "Tweets retrieved successfully.", tweets)

#     except tweepy.BadRequest as e:
#         logger.error(f"Bad request error: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             detail=f"Bad request: {str(e.args[0])}",
#         )
#     except tweepy.Unauthorized as e:
#         logger.error(f"Unauthorized error: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_401_UNAUTHORIZED,
#             detail="You do not have permission to access this resource",
#         )
#     except tweepy.Forbidden as e:
#         logger.error(f"Forbidden error: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="You do not have permission to access this resource",
#         )
#     except tweepy.NotFound as e:
#         logger.error(f"Not found error: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail="Requested resource not found",
#         )
#     except tweepy.TooManyRequests as e:
#         logger.error(f"Too many requests error: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_429_TOO_MANY_REQUESTS,
#             detail="Too many requests. Please try again later",
#         )
#     except tweepy.TweepyException as e:
#         logger.error(f"Tweepy error: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="An unexpected error occurred",
#         )
#     except HTTPException as e:
#         return fail_response(e.status_code, e.detail)
#     except Exception as e:
#         logger.error(f"Unexpected error: {e}")
#         return fail_response(
#             status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
#         )


@router.get("/{scheduled_content_id}/comments")
async def get_comments(
    organisation_id: Annotated[str, Depends(verify_organization)],
    scheduled_content_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Get all comments on a post"""
    try:
        # get data from redis
        cache_key = f"twitter_comments_{scheduled_content_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("data available on cache")
            return success_response(200, "Comments of post received", json.loads(cache_data))
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting social details and valid access token")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # if not, fetch the data from the API
        # logger.info("data is not available in the cache")

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)
        result = await db_session.execute(
            select(Post)
            .join(SocialMediaAccount)
            .filter(
                Post.schedulecontent_id == scheduled_content_id,
                SocialMediaAccount.id == db_social.id,
                SocialMediaAccount.platform == "twitter",
            )
        )
        db_post = result.scalar_one_or_none()
        if not db_post:
            raise HTTPException(status_code=404, detail="Requested content not found")

        # Initialize the client for the authenticated account
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.TWITTER_BASE_URL}/tweets/search/recent",
                params={
                    "query": f"conversation_id: {db_post.post_id}",
                    "max_results":"10",
                    "tweet.fields": "referenced_tweets,text,author_id,public_metrics,created_at,attachments",
                    "expansions": "attachments.media_keys,author_id",
                    "media.fields": "url,public_metrics",
                    "sort_order": "recency",
                    "user.fields": "username,public_metrics,location,name"
                },
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            )
            response.raise_for_status()

            replies_response = response.json()
            # logger.info(replies_response)
            # logger.info(replies_response.get("error"))
            replies_data = replies_response.get("data", [])

            # format the comment response i.e data
            twitter_comments = [
                {
                    "comment_id": comment.get("id"),
                    "created_at": comment.get("created_at"),
                    "metrics": comment.get("public_metrics", {}),
                    "text": comment.get("text"),
                    "user_details": [user for user in replies_response.get("includes", {}).get("users", []) if comment.get("author_id") == user.get("id")]

                }
                for comment in replies_data
            ]
            # # logger.info(f"\n\n\n\n{twitter_comments}")

            # save to redis cache
            # logger.info('saving to redis cache')
            redis_client.setex(cache_key, 300, json.dumps(twitter_comments))

            return success_response(200, "Dm conversation for a participant retrieved", twitter_comments)
    except httpx.HTTPStatusError as e:
        logger.error(f"An error occurred during the conversation: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="An unexpected error occurred")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.post("/comments")
# reply to a comment
async def reply_a_comment(
    organisation_id: Annotated[str, Depends(verify_organization)],
    comment_data: CommentResponse,
    db_session: AsyncSession = Depends(get_db),
):
    """Make a post for a specific organisation"""
    try:
        # Get the organisation SocialMediaAccount credentials
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # upload images to X
        media_keys = []
        # if img_urls:
        #     for img in img_urls:
        #         res = await upload_image_to_X(img, access_token)
        #         media_keys.append(res)

        # Initialize the client for the authenticated account
        # logger.info("making call to X from tweepy")
        client = tweepy.Client(bearer_token=access_token)
        response = client.create_tweet(
            text=comment_data.text,
            # media_ids=media_keys,
            in_reply_to_tweet_id=comment_data.comment_id,
            user_auth=False
        )

        return success_response(
            200,
            "comment posted successfully",
            response.data,
        )
    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.response.json()['detail']
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"An unexpected error occurred: {str(e)}",
        )

# ###### DM ######
@router.get("/get_dm", response_model=GetDirectMessagesResponse)
async def get_direct_messages(
    organisation_id: Annotated[str, Depends(verify_organization)],
    participant_id: Optional[str] = None,
    conversation_id: Optional[str] = None,
    db_session: AsyncSession = Depends(get_db),
) -> GetDirectMessagesResponse:
    """
    Retrieve all the direct messages of a user

    ## if participant_id is passed:
    * Retrieves Direct Message events associated with a one-to-one conversation. The :participant_id path parameter is the User ID of the account having the conversation with the authenticated user making this request.
    ## if conversation_id is passed
    * Retrieves Direct Message events associated with a specific conversation ID, as indicated by the :dm_conversation_id path parameter

    ## if both participant_id and conversation_id are not passed
    * Retrieves Direct Message events associated with a user, including both one-to-one and group conversations. Events from up to 30 days ago are available

    ## if both participant_id and conversation_id are passed
    * Raises an error

    """
    try:
        # Get the organisation SocialMediaAccount credentials
        if (participant_id and conversation_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only one of 'participant_id' or 'conversation_id' should be provided"
            )
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )
        # check if the data is in the cache
        cache_key = f"x_dm_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            # logger.info("data is available in the cache")
            return success_response(
                200,
                "Tweets retrieved successfully.",
                json.loads(cache_data),
            )

        # logger.info("data not available in cache")
        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        client = tweepy.Client(bearer_token=access_token)

        # Retrieve the account's tweets
        response = client.get_direct_message_events(
            participant_id=participant_id if participant_id else None,
            dm_conversation_id=conversation_id if conversation_id else None,
            dm_event_fields=[
                "attachments",
                "created_at",
                "dm_conversation_id",
                "entities",
                "event_type",
                "id",
                "participant_ids",
                "referenced_tweets",
                "sender_id",
                "text"
            ],
            expansions=[
                "attachments.media_keys", "participant_ids", "referenced_tweets.id", "sender_id"
            ],
            media_fields=["media_key", "url", "type"],
            user_fields=["id", "name", "username"],
            tweet_fields=["id", "text", "author_id", "in_reply_to_user_id", "created_at"],
            user_auth=False,
        )

        # logger.info(f"response from tweepy: {response}")
        # redis_client.setex("sample_key", redis_expiry_time, json.dumps(response))

        # Check if response contains data
        if not response.data:
            return success_response(200, "No tweets found for this account.", [])

        # Format the data
        media_dict = {
            m["media_key"]: {
                "type": m["type"],
                "url": m["url"]
            } for m in response.includes.get("media", [])}
        tweets = [
            {
                "id": tweet.id,
                "text": tweet.text,
                "created_at": tweet.created_at,
                "sender_id": tweet.sender_id,
                "conversation_id": tweet.dm_conversation_id,
                "event_type": tweet.event_type,
                "participants_id": tweet.participant_ids,
                "media": extract_images_from_response(tweet, media_dict)
            }
            for tweet in response.data
        ]
        # logger.info(f"dm messages: {tweets}")

        # logger.info("saving to redis cache")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(tweets))
        return success_response(200, "Tweets retrieved successfully.", tweets)

    except tweepy.BadRequest as e:
        logger.error(f"Bad request error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bad request: {str(e.args[0])}",
        )
    except tweepy.Unauthorized as e:
        logger.error(f"Unauthorized error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="You do not have permission to access this resource",
        )
    except tweepy.Forbidden as e:
        logger.error(f"Forbidden error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this resource",
        )
    except tweepy.NotFound as e:
        logger.error(f"Not found error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested resource not found",
        )
    except tweepy.TooManyRequests as e:
        logger.error(f"Too many requests error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later",
        )
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.post("/dm_conversations/with", response_model=SendMessageToUserResponse)
async def send_a_new_message_to_a_user(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: X_DM_WITH,
    db_session: AsyncSession = Depends(get_db),
):
    """Creates a one-to-one Direct Message. This method either creates a new 1-1 conversation or retrieves the current conversation and adds the Direct Message to it. The :participant_id path parameter is the User ID of the account receiving the message. """
    try:
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting social details and valid access token")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # if not, fetch the data from the API
        # logger.info("data is not available in the cache")

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{settings.TWITTER_BASE_URL}/dm_conversations/with/{message.participant_id}/messages",
                json={
                    "text": message.text,
                },
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            )
            response.raise_for_status()

            dm_response = response.json()
            dm_data = dm_response.get("data")

            return success_response(200, "Dm conversation for a participant delivered", dm_data)
    except httpx.HTTPStatusError as e:
        logger.error(f"An error occurred during the conversation: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="An error occurred")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.post("/dm_conversations", response_model=CreateDmConversationResponse)
async def create_a_new_DM_conversation(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: X_DM,
    db_session: AsyncSession = Depends(get_db),
):
    """Creates a new group conversation and adds a Direct Message to it. These requests require a list of conversation participants. Note that you can create multiple conversations with the same participant list. These requests will always return a new conversation ID"""
    try:
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting social details and valid access token")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # if not, fetch the data from the API
        # logger.info("data is not available in the cache")

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{settings.TWITTER_BASE_URL}/dm_conversations",
                json={
                    "conversation_type": "Group",
                    "message": {
                        "text": message.text,
                    },
                    "participant_ids": message.participant_ids,
                },
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            )
            response.raise_for_status()

            dm_response = response.json()
            dm_data = dm_response.get("data")

            return success_response(200, "Dm conversation for a participant retrieved", dm_data)
    except httpx.HTTPStatusError as e:
        logger.error(f"An error occurred during the conversation: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="An unexpected error occurred")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.post("/dm_conversation", response_model=SendMessageToConversationResponse)
async def send_a_new_message_to_a_DM_conversation(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: X_DM_CONVERSATION,
    db_session: AsyncSession = Depends(get_db),
):
    """Creates a Direct Message and adds it to an existing conversation. The :dm_conversation_id path parameter is the ID of the conversation that the message will be added to. """
    try:
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting social details and valid access token")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{settings.TWITTER_BASE_URL}/dm_conversations/{message.conversation_id}/messages",
                json={
                    "text": message.text,
                },
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
            )
            response.raise_for_status()

            dm_response = response.json()
            dm_data = dm_response.get("data")

            return success_response(200, "Dm conversation for a participant delivered", dm_data)
    except httpx.HTTPStatusError as e:
        logger.error(f"An error occurred during the conversation: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="An error occurred")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )


@router.delete("/dm_conversation/{event_id}")
async def delete_a_dm(
    organisation_id: Annotated[str, Depends(verify_organization)],
    event_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Delete a DM Event that you own"""
    try:
        # Get the organisation SocialMediaAccount credentials
        # logger.info("getting social details and valid access token")
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="twitter",
            db_session=db_session,
        )

        # Validate and refresh access token if necessary
        access_token = await get_valid_access_token(db_social, db_session)

        # Initialize the client for the authenticated account
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.delete(
                f"{settings.TWITTER_BASE_URL}/dm_events/{event_id}",
                headers={
                    "Authorization": f"Bearer {access_token}"
                }
            )
            response.raise_for_status()

            dm_response = response.json()
            dm_data = dm_response.get("data")

            return success_response(200, "Dm conversation for a participant delivered", dm_data)
    except httpx.HTTPStatusError as e:
        logger.error(f"An error occurred during the conversation: {str(e)}")
        raise HTTPException(status_code=e.response.status_code, detail="An error occurred")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return fail_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR, "An unexpected error occurred"
        )
