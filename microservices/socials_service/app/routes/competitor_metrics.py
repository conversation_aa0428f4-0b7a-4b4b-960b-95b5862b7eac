import json
import logging
from typing import Annotated, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.database.session import get_db
from app.models import model
from app.schemas import schema
from app.utils.dependency import rate_limiter, verify_organization
from app.utils.redis_cache import redis_client

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/organization-competitor-metrics/{org_competitor_id}", response_model=List[schema.TwitterCompetitorMetrics], dependencies=[Depends(rate_limiter)])
async def get_organization_competitor_metrics(
    org_competitor_id: str,
    organization_id: Annotated[str, Depends(verify_organization)],
    limit: int = Query(10, description="Number of metrics records to return"),
    db: AsyncSession = Depends(get_db)
):
    """Get historical metrics for a competitor tracked by an organization"""
    # First, verify the organization has access to this competitor
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor))
        .filter(
            model.OrganizationCompetitor.id == org_competitor_id,
            model.OrganizationCompetitor.organization_id == organization_id
        )
    )
    org_competitor = result.scalars().first()
    
    if org_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found for this organization")
    
    # Get metrics for this competitor
    metrics_result = await db.execute(
        select(model.TwitterCompetitorMetrics)
        .filter(model.TwitterCompetitorMetrics.competitor_id == org_competitor.competitor_id)
        .order_by(model.TwitterCompetitorMetrics.collected_at.desc())
        .limit(limit)
    )
    metrics = metrics_result.scalars().all()
    
    return metrics

@router.get("/organization-competitor-metrics-summary/{org_competitor_id}", dependencies=[Depends(rate_limiter)])
async def get_organization_competitor_metrics_summary(
    org_competitor_id: str,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """Get a summary of the latest metrics for a competitor tracked by an organization"""
    # First, verify the organization has access to this competitor
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor))
        .filter(
            model.OrganizationCompetitor.id == org_competitor_id,
            model.OrganizationCompetitor.organization_id == organization_id
        )
    )
    org_competitor = result.scalars().first()
    
    if org_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found for this organization")
    
    # Try to get from cache first
    cache_key = f"competitor_metrics:{org_competitor.competitor_id}"
    cached_data = redis_client.get(cache_key)
    
    if cached_data:
        # logger.info(f"Cache hit for competitor metrics: {org_competitor.competitor.username}")
        return json.loads(cached_data.replace("'", "\""))
    
    # If not in cache, get from database
    metrics_result = await db.execute(
        select(model.TwitterCompetitorMetrics)
        .filter(model.TwitterCompetitorMetrics.competitor_id == org_competitor.competitor_id)
        .order_by(model.TwitterCompetitorMetrics.collected_at.desc())
        .limit(1)
    )
    latest_metrics = metrics_result.scalars().first()
    
    if not latest_metrics:
        return {
            "id": org_competitor.competitor_id,
            "username": org_competitor.competitor.username,
            "message": "No metrics available yet"
        }
    
    # Create summary
    summary = {
        "id": org_competitor.competitor_id,
        "username": org_competitor.competitor.username,
        "followers_count": latest_metrics.followers_count,
        "following_count": latest_metrics.following_count,
        "tweet_count": latest_metrics.tweet_count,
        "engagement_rate": latest_metrics.engagement_rate,
        "updated_at": latest_metrics.collected_at.isoformat()
    }
    
    # Cache the summary
    redis_client.set(cache_key, str(summary), ex=86400)  # Cache for 24 hours
    
    return summary

@router.get("/organization-competitors-comparison", dependencies=[Depends(rate_limiter)])
async def compare_organization_competitors(
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """Compare all competitors tracked by an organization"""
    # Get all competitors for this organization
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor))
        .filter(model.OrganizationCompetitor.organization_id == organization_id)
    )
    org_competitors = result.scalars().all()
    
    if not org_competitors:
        return {"message": "No competitors found for this organization"}
    
    comparison_data = []
    
    for org_competitor in org_competitors:
        # Try to get from cache first
        cache_key = f"competitor_metrics:{org_competitor.competitor_id}"
        cached_data = redis_client.get(cache_key)
        
        if cached_data:
            comparison_data.append(json.loads(cached_data.replace("'", "\"")))
            continue
        
        # If not in cache, get from database
        metrics_result = await db.execute(
            select(model.TwitterCompetitorMetrics)
            .filter(model.TwitterCompetitorMetrics.competitor_id == org_competitor.competitor_id)
            .order_by(model.TwitterCompetitorMetrics.collected_at.desc())
            .limit(1)
        )
        latest_metrics = metrics_result.scalars().first()
        
        if not latest_metrics:
            continue
        
        # Create summary
        summary = {
            "id": org_competitor.competitor_id,
            "username": org_competitor.competitor.username,
            "followers_count": latest_metrics.followers_count,
            "following_count": latest_metrics.following_count,
            "tweet_count": latest_metrics.tweet_count,
            "engagement_rate": latest_metrics.engagement_rate,
            "updated_at": latest_metrics.collected_at.isoformat()
        }
        
        comparison_data.append(summary)
    
    return {"competitors": comparison_data}
