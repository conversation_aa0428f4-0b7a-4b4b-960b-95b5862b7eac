"""
Unified comments router providing platform-agnostic comment endpoints.

This module provides endpoints that return normalized comment data from both
Instagram and Facebook in a consistent format.
"""

import logging
from typing import Annotated, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from app.utils.dependency import verify_organization
from app.database.session import get_db
from app.models.model import Comment, Post
from app.schemas.unified_comment import (
    UnifiedCommentResponse,
    UnifiedCommentCreateRequest,
    UnifiedCommentCreateResponse,
    PlatformType
)
from app.transformers.comment_transformer import (
    transform_database_comment_to_unified,
    create_unified_comment_response
)
from app.utils.success_response import fail_response, success_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/unified/comments", tags=["Unified Comments"])


@router.get("/{scheduled_content_id}", response_model=UnifiedCommentResponse)
async def get_unified_comments(
    scheduled_content_id: str,
    _: Annotated[str, Depends(verify_organization)],
    limit: int = Query(10, ge=1, le=100, description="Number of comments to return"),
    offset: int = Query(0, ge=0, description="Number of comments to skip"),
    platform: Optional[PlatformType] = Query(
        None, 
        description="Filter by platform (instagram/facebook)"
    ),
    include_replies: bool = Query(
        True, 
        description="Whether to include replies in the response"
    ),
    db_session: AsyncSession = Depends(get_db)
):
    """
    Get all comments for a scheduled content item in unified format.
    
    This endpoint returns comments from both Instagram and Facebook in a 
    standardized format, allowing frontend components to handle both platforms
    with the same code.
    """
    try:
        # Get the post for the scheduled content
        result = await db_session.execute(
            select(Post).where(
                Post.schedulecontent_id == scheduled_content_id
            )
        )
        post = result.scalars().first()
        if not post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested content not found"
            )

        # Build query for comments
        query_conditions = [
            Comment.post_id == post.post_id,
            Comment.parent_id.is_(None),  # Only top-level comments
            or_(
                Comment.extra_data['hidden'].as_boolean() is not True,
                Comment.extra_data['hidden'] is None
            )
        ]

        # Get total count for pagination metadata
        total = await db_session.scalar(
            select(func.count()).where(*query_conditions)
        )

        # Get comments with pagination
        result = await db_session.execute(
            select(Comment)
            .where(*query_conditions)
            .order_by(Comment.created_time.desc())
            .offset(offset)
            .limit(limit)
        )
        db_comments = result.scalars().all()

        # Transform comments to unified format
        unified_comments = []
        for db_comment in db_comments:
            try:
                unified_comment = transform_database_comment_to_unified(
                    db_comment
                )
                
                # Filter by platform if specified
                if platform and unified_comment.platform != platform:
                    continue
                    
                unified_comments.append(unified_comment)
            except Exception as e:
                logger.error(
                    f"Error transforming comment {db_comment.comment_id}: {e}"
                )
                continue

        # If platform filter was applied, we need to adjust the total count
        if platform:
            # This is an approximation - for exact count we'd need a more complex query
            total = len(unified_comments)

        return create_unified_comment_response(
            comments=unified_comments,
            total=total,
            limit=limit,
            offset=offset,
            platform=platform
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching unified comments: {e}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/{scheduled_content_id}/replies/{comment_id}")
async def get_unified_comment_replies(
    scheduled_content_id: str,
    comment_id: str,
    _: Annotated[str, Depends(verify_organization)],
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db_session: AsyncSession = Depends(get_db)
):
    """
    Get replies to a specific comment in unified format.
    """
    try:
        # Verify the parent comment exists and belongs to the scheduled content
        parent_result = await db_session.execute(
            select(Comment)
            .join(Post, Comment.post_id == Post.post_id)
            .where(
                Comment.comment_id == comment_id,
                Post.schedulecontent_id == scheduled_content_id
            )
        )
        parent_comment = parent_result.scalars().first()
        if not parent_comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Parent comment not found"
            )

        # Get total count of replies
        total = await db_session.scalar(
            select(func.count()).where(
                Comment.parent_id == comment_id,
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not True,
                    Comment.extra_data['hidden'] is None
                )
            )
        )

        # Get replies with pagination
        result = await db_session.execute(
            select(Comment)
            .where(
                Comment.parent_id == comment_id,
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not True,
                    Comment.extra_data['hidden'] is None
                )
            )
            .order_by(Comment.created_time.asc())
            .offset(offset)
            .limit(limit)
        )
        reply_comments = result.scalars().all()

        # Transform replies to unified format
        unified_replies = []
        for reply_comment in reply_comments:
            try:
                unified_reply = transform_database_comment_to_unified(
                    reply_comment
                )
                unified_replies.append(unified_reply)
            except Exception as e:
                logger.error(
                    f"Error transforming reply {reply_comment.comment_id}: {e}"
                )
                continue

        return create_unified_comment_response(
            comments=unified_replies,
            total=total,
            limit=limit,
            offset=offset
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching comment replies: {e}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/platforms/summary/{scheduled_content_id}")
async def get_comment_platform_summary(
    scheduled_content_id: str,
    _: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Get a summary of comments by platform for a scheduled content item.
    
    Returns counts and basic statistics for comments from each platform.
    """
    try:
        # Get the post for the scheduled content
        result = await db_session.execute(
            select(Post).where(
                Post.schedulecontent_id == scheduled_content_id
            )
        )
        post = result.scalars().first()
        if not post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested content not found"
            )

        # Get all comments for analysis
        result = await db_session.execute(
            select(Comment).where(
                Comment.post_id == post.post_id,
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not True,
                    Comment.extra_data['hidden'] is None
                )
            )
        )
        all_comments = result.scalars().all()

        # Analyze comments by platform
        platform_stats = {
            "instagram": {"total": 0, "top_level": 0, "replies": 0},
            "facebook": {"total": 0, "top_level": 0, "replies": 0},
            "unknown": {"total": 0, "top_level": 0, "replies": 0}
        }

        for comment in all_comments:
            try:
                unified_comment = transform_database_comment_to_unified(comment)
                platform_key = unified_comment.platform.value
                
                platform_stats[platform_key]["total"] += 1
                if comment.parent_id is None:
                    platform_stats[platform_key]["top_level"] += 1
                else:
                    platform_stats[platform_key]["replies"] += 1
                    
            except Exception as e:
                logger.error(f"Error analyzing comment {comment.comment_id}: {e}")
                platform_stats["unknown"]["total"] += 1
                if comment.parent_id is None:
                    platform_stats["unknown"]["top_level"] += 1
                else:
                    platform_stats["unknown"]["replies"] += 1

        return success_response(
            200,
            "Platform summary retrieved successfully",
            {
                "scheduled_content_id": scheduled_content_id,
                "platform_stats": platform_stats,
                "total_comments": sum(stats["total"] for stats in platform_stats.values())
            }
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error generating platform summary: {e}")
        return fail_response(500, "An unexpected error occurred")
