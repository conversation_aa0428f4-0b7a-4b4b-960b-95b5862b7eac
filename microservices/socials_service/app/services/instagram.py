import asyncio
from datetime import datetime
import traceback
from typing import Any, Dict, Optional, Tuple, Union

import httpx
from fastapi import HTTPException

from app.core.config import settings
from app.schemas.instagram_schema import InstagramCreate
from app.utils.logger import get_logger
from app.models.model import (
    Comment, Conversation, Message, SocialMediaAccount,
    InstagramAudienceDemographics, InstagramMediaMetrics,
    InstagramAccountMetrics
)
from app.database.session import SessionLocal
from app.utils.dependency import get_social_details
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.dialects.postgresql import insert
from app.utils.success_response import fail_response

logger = get_logger(__name__)
timeout = httpx.Timeout(30.0, connect=30.0, read=30.0, write=30.0)


class InstagramAPIError(Exception):
    """Exception raised for Instagram API errors"""
    def __init__(
        self,
        status_code: int,
        message: str,
        response_data: Optional[Dict[str, Any]] = None
    ):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data
        super().__init__(f"Instagram API Error ({status_code}): {message}")


class InstagramRateLimitError(InstagramAPIError):
    """Exception raised for Instagram API rate limit errors"""
    def __init__(self, message: str, retry_after: Optional[str] = None):
        super().__init__(429, message)
        self.retry_after = retry_after


class InstagramAuthError(InstagramAPIError):
    """Exception raised for Instagram API authentication errors"""
    def __init__(self, message: str):
        super().__init__(401, message)


# Enhanced API request function with better error handling
async def make_instagram_api_request(
    method: str,
    url: str,
    headers: Dict[str, str],
    data: Optional[Dict[str, Any]] = None,
    handle_errors: bool = True
) -> Tuple[Union[Dict[str, Any], None], Union[Exception, None]]:
    """
    Make an Instagram API request with comprehensive error handling

    Args:
        method: HTTP method (GET, POST, etc.)
        url: API endpoint URL
        headers: HTTP headers
        data: Request data (for GET: query params, for POST/PUT: JSON body)
        handle_errors: If True, handle errors internally and return (None, error)
        If False, raise exceptions

    Returns:
        Tuple of (response_data, error)
        If successful: (response_data, None)
        If error and handle_errors=True: (None, error)

    Raises:
        Various exceptions if handle_errors=False
    """
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            if method == "GET":
                response = await client.get(url, headers=headers, params=data)
            elif method == "POST":
                response = await client.post(url, headers=headers, json=data)
            elif method == "DELETE":
                response = await client.delete(url, headers=headers, params=data)
            elif method == "PUT":
                response = await client.put(url, headers=headers, json=data)
            else:
                error = ValueError(f"Unsupported HTTP method: {method}")
                if handle_errors:
                    return None, error
                raise error

            # Handle specific error status codes
            if response.status_code == 401 or response.status_code == 403:
                error_data = None
                try:
                    error_data = response.json()
                    error_message = error_data.get('error', {}).get(
                        'message', 'Authentication error'
                    )
                except Exception as e:
                    logger.error(f'Failed to parse error response: {str(e)}')
                    error_message = (
                        f"Authentication error: HTTP {response.status_code}"
                    )

                error = InstagramAuthError(error_message)
                logger.error(f"Instagram auth error: {error_message}")
                if handle_errors:
                    return None, error
                raise error

            elif response.status_code == 429:
                retry_after = response.headers.get('Retry-After', '60')
                error_message = (
                    f"Rate limit exceeded. Retry after {retry_after} seconds"
                )
                error = InstagramRateLimitError(error_message, retry_after)
                logger.warning(error_message)
                if handle_errors:
                    return None, error
                raise error

            elif response.status_code >= 400 and response.status_code != 408:
                error_data = None
                try:
                    error_data = response.json()
                    error_message = error_data.get('error', {}).get(
                        'message', f"HTTP error {response.status_code}"
                    )
                except Exception as e:
                    logger.error(f'Failed to parse error response: {str(e)}')
                    error_message = f"HTTP error {response.status_code}"

                error = InstagramAPIError(
                    response.status_code,
                    error_message,
                    error_data
                )
                logger.error(f"Instagram API error: {error_message}")
                if handle_errors:
                    return None, error
                raise error

            # Process successful response
            try:
                response_data = response.json()
                return response_data, None
            except ValueError as e:
                error = InstagramAPIError(
                    response.status_code,
                    f"Invalid JSON response: {str(e)}"
                )
                if handle_errors:
                    return None, error
                raise error

    except httpx.TimeoutException as e:
        error = InstagramAPIError(408, f"Request timeout: {str(e)}")
        logger.error(f"Instagram request timeout: {str(e)}")
        if handle_errors:
            return None, error
        raise error

    except httpx.RequestError as e:
        error = InstagramAPIError(500, f"Request error: {str(e)}")
        logger.error(f"Instagram request error: {str(e)}")
        if handle_errors:
            return None, error
        raise error

    except Exception as e:
        error = InstagramAPIError(500, f"Unexpected error: {str(e)}")
        logger.error(f"Unexpected error in Instagram API request: {str(e)}")
        logger.error(traceback.format_exc())
        if handle_errors:
            return None, error
        raise error


async def create_media_container(
    data: InstagramCreate,
    access_token: str
):
    """
    Creates a media container for single images/videos or carousel items.
    """
    url = f"{settings.INSTAGRAM_API_BASE_URL}/me/media"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "media_type": data.media_type.upper(),
        "is_carousel_item": data.is_carousel_item
    }

    if data.media_type.upper() == "IMAGE":
        if not data.image_url:
            raise HTTPException(status_code=400, detail="Image URL is required for IMAGE media type.")

        payload["image_url"] = str(data.image_url)
    else:
        if not data.video_url:
            raise HTTPException(status_code=400, detail="Video URL is required for VIDEO media type.")
        payload["video_url"] = str(data.video_url)

    try:
        response_data, error = await make_instagram_api_request(
            "POST", url, headers, payload
        )
        if error:
            raise HTTPException(status_code=500, detail=str(error))

        if response_data and "id" in response_data:
            return response_data["id"]
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to create media container - no ID returned"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def publish_media(creation_id: str, access_token):
    """
    Publishes a media container.
    """

    url = f"{settings.INSTAGRAM_API_BASE_URL}/me/media_publish"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "creation_id": creation_id,
    }

    try:
        response_data, error = await make_instagram_api_request(
            "POST", url, headers, payload
        )
        if error:
            raise HTTPException(status_code=500, detail=str(error))

        if response_data and "id" in response_data:
            return response_data["id"]
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to publish media - no ID returned"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# conversations API

def to_datetime(value: str) -> datetime:
    return datetime.strptime(value, "%Y-%m-%dT%H:%M:%S%z")


async def get_conversations_from_api(
    db_session: AsyncSession,
    social_account: SocialMediaAccount
):
    """get a list of your app user's conversations for an Instagram professional account"""
    try:
        # logger.info("getting conversations from api")
        url = f"{settings.INSTAGRAM_API_BASE_URL}/me/conversations"
        headers = {"Authorization": f"Bearer {social_account.access_token}"}
        params = {"fields": "participants,updated_time,id"}

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url, params=params, headers=headers)
            response.raise_for_status()
            response_data = response.json()

        for item in response_data["data"]:
            new_convo = Conversation(
                social_media_account_id=social_account.id,
                convo_id=item.get('id'),
                updated_time=to_datetime(item.get('updated_time')),
                participants=item.get("participants").get("data", [])
            )
            await db_session.merge(new_convo)
        await db_session.commit()
        # logger.info("conversations added to the db")

        return
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.text)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f'A server error occurred: {str(e)}')
        return fail_response(500, "An unexpected error occurred")


async def get_messages_in_conversations(
    conversation_id: str,
    db_session: AsyncSession,
    social_account_token: str
):
    """get a list of earliest twenty messages in the db and saves it in the db"""
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{conversation_id}"
    headers = {"Authorization": f"Bearer {social_account_token}"}
    params = {
        "fields": "messages{id,created_time,from,to,message,attachments,reactions,story,shares}"}

    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(
            url=url, headers=headers, params=params
        )
        response.raise_for_status()
        response_data = response.json()
        messages = response_data.get("messages").get("data", [])
        convo_id = response_data.get("id")

    # clean the messages response
    # logger.info(f"found {len(messages)} messages")
    for message in messages:
        sender = message.get("from")
        recipient = message.get("to", {}).get("data", [])[0]
        text_message = message.get("message")
        created_time = to_datetime(message.get("created_time"))
        msg_id = message.get("id")
        attachments = message.get("attachments", {}).get("data", [])
        reactions = message.get("reactions", {}).get("data", [])
        shares = message.get("shares", {}).get("data", [])
        # save to the db
        new_entry = Message(
            platform="instagram",
            sender=sender,
            recipient=recipient,
            message=text_message,
            created_time=created_time,
            message_id=msg_id,
            conversation_id=convo_id,
            attachments=attachments,
            reactions=reactions,
            shares=shares
        )
        await db_session.merge(new_entry)
    await db_session.commit()
    # logger.info("saved messages to the db")
    return


async def fetch_messages_for_convo(convo_id: str, social_account_token: str):
    async with SessionLocal() as session:
        await get_messages_in_conversations(
            conversation_id=convo_id,
            social_account_token=social_account_token,
            db_session=session
        )


async def fetch_conversations_and_messages(
    social_media_details: SocialMediaAccount
):
    async with SessionLocal() as db_session:
        # fetch and save conversations from the API
        await get_conversations_from_api(
            db_session=db_session,
            social_account=social_media_details
        )

        # get the convo ids from the db
        result = await db_session.execute(
            select(Conversation.convo_id).where(
                Conversation.social_media_account_id == social_media_details.id
            )
        )
        convo_ids = [rows[0] for rows in result.fetchall()]

    # Step 3: Fetch all messages concurrently
    # logger.info(f"fetching messages for : {len(convo_ids)} conversations")
    await asyncio.gather(*[
        fetch_messages_for_convo(convo_id, social_media_details.access_token)
        for convo_id in convo_ids
    ])
    return


# comments services
async def save_comments_to_db(
    ig_media_id: str,
    access_token: str
):
    """
    Gets all comments on a specific Instagram media object and save to the db.
    """
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{ig_media_id}/comments"
    headers = {"Content-Type": "application/json"}
    params = {
        "access_token": access_token,
        "fields": "from,id,like_count,text,media{id,media_type,media_url,thumbnail_url,alt_text,caption},parent_id,replies{text,like_count,username,timestamp,id,media{id,media_type,media_url,thumbnail_url,alt_text,caption}},timestamp,username"
    }

    async with SessionLocal() as db_session:
        try:
            # logger.info("updating comments to the db")
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                response_data = response.json()
                comments = response_data.get("data", [])
                for comment in comments:
                    # save to db
                    metadata = {
                        "replies": comment.get("replies", {}).get("data", []),
                        "hidden": comment.get("hidden", False),
                        "like_count": comment.get("like_count", 0)
                    }
                    try:
                        stmt = insert(Comment).values(
                            content=comment.get("text"),
                            media=comment.get("media", {}),
                            comment_id=comment.get("id"),
                            post_id=ig_media_id,
                            parent_id=comment.get("parent_id"),
                            sender=comment.get("from", {}),
                            created_time=to_datetime(comment.get("timestamp")),
                            extra_data=metadata
                        ).on_conflict_do_update(
                            index_elements=['post_id', 'comment_id'],
                            set_={
                                "media": comment.get("media", {}),
                                "comment_id": comment.get("id"),
                                "post_id": ig_media_id,
                                "parent_id": comment.get("parent_id"),
                                "sender": comment.get("from", {}),
                                "created_time": to_datetime(comment.get("timestamp")),
                                "extra_data": metadata
                                # add other fields to update if needed
                            }
                        )
                        await db_session.execute(stmt)
                    except Exception as e:
                        logger.error(f'An exception occurred: {str(e)}')
                await db_session.commit()

            # logger.info("saving comments to the db")
            return

        except httpx.HTTPStatusError as e:
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.text)
        except HTTPException as e:
            return fail_response(e.status_code, e.detail)
        except Exception as e:
            await db_session.rollback()
            return fail_response(500, str(e))


# Overview Metrics DB Service
async def get_instagram_overview_metrics(
    db_session: AsyncSession,
    organisation_id: str,
    instagram_user_id: str = None,
):
    """
    Fetch the latest Instagram overview metrics for an org/account.
    Returns the most recent InstagramAccountMetrics record.
    """

    account = await get_social_details(organisation_id, "instagram", db_session)
    instagram_user_id = account.social_media_user_id
    # await fetch_and_store_account_metrics(account, db_session)
    filters = [InstagramAccountMetrics.organisation_id == organisation_id]
    if instagram_user_id:
        filters.append(InstagramAccountMetrics.instagram_user_id == instagram_user_id)
    q = (
        select(InstagramAccountMetrics)
        .where(*filters)
        .order_by(InstagramAccountMetrics.collected_at.desc())
        .limit(1)
    )
    result = await db_session.execute(q)
    return result.scalars().first()


# Audience Growth Trend Service
async def get_instagram_audience_growth_trend(
    db_session: AsyncSession,
    organisation_id: str,
    start: datetime = None,
    end: datetime = None,
    limit: int = 1000,
):
    """
    Fetch audience growth trend (followers over time) for an org/account.
    Optionally filter by start/end date.
    """
    filters = [InstagramAccountMetrics.organisation_id == organisation_id]
    if start:
        filters.append(InstagramAccountMetrics.collected_at >= start)
    if end:
        filters.append(InstagramAccountMetrics.collected_at <= end)
    q = (
        select(InstagramAccountMetrics.collected_at, InstagramAccountMetrics.followers_count)
        .where(*filters)
        .order_by(InstagramAccountMetrics.collected_at.asc())
        .limit(limit)
    )
    result = await db_session.execute(q)
    return [{"collected_at": row[0], "followers_count": row[1]} for row in result.fetchall()]


# Engagement Trend Service
async def get_instagram_engagement_trend(
    db_session: AsyncSession,
    organisation_id: str,
    start: datetime = None,
    end: datetime = None,
    limit: int = 1000,
):
    """
    Fetch engagement trend (engagements over time) for an org/account.
    Optionally filter by start/end date.
    """
    filters = [InstagramAccountMetrics.organisation_id == organisation_id]
    if start:
        filters.append(InstagramAccountMetrics.collected_at >= start)
    if end:
        filters.append(InstagramAccountMetrics.collected_at <= end)
    q = (
        select(InstagramAccountMetrics.collected_at, InstagramAccountMetrics.engagements)
        .where(*filters)
        .order_by(InstagramAccountMetrics.collected_at.asc())
        .limit(limit)
    )
    result = await db_session.execute(q)
    return [{"collected_at": row[0], "engagements": row[1]} for row in result.fetchall()]


# Post Engagement Trend Service
async def get_instagram_post_engagement_trend(
    db_session: AsyncSession,
    organisation_id: str,
    media_id: str,
    start: datetime = None,
    end: datetime = None,
    limit: int = 1000,
):
    """
    Fetch post engagement trend (engagement over time) for a given post/media.
    Optionally filter by start/end date.
    """
    filters = [
        InstagramMediaMetrics.organisation_id == organisation_id,
        InstagramMediaMetrics.media_id == media_id,
    ]
    if start:
        filters.append(InstagramMediaMetrics.collected_at >= start)
    if end:
        filters.append(InstagramMediaMetrics.collected_at <= end)
    q = (
        select(InstagramMediaMetrics.collected_at, InstagramMediaMetrics.reach)
        .where(*filters)
        .order_by(InstagramMediaMetrics.collected_at.asc())
        .limit(limit)
    )
    result = await db_session.execute(q)
    return [{"collected_at": row[0], "reach": row[1]} for row in result.fetchall()]


# Account Reach Trend Service
async def get_instagram_account_reach_trend(
    db_session: AsyncSession,
    organisation_id: str,
    start: datetime = None,
    end: datetime = None,
    limit: int = 1000,
):
    """
    Fetch account reach trend (reach over time) for an org/account.
    Optionally filter by start/end date.
    """
    filters = [InstagramAccountMetrics.organisation_id == organisation_id]
    if start:
        filters.append(InstagramAccountMetrics.collected_at >= start)
    if end:
        filters.append(InstagramAccountMetrics.collected_at <= end)
    q = (
        select(InstagramAccountMetrics.collected_at, InstagramAccountMetrics.reach)
        .where(*filters)
        .order_by(InstagramAccountMetrics.collected_at.asc())
        .limit(limit)
    )
    result = await db_session.execute(q)
    return [{"collected_at": row[0], "reach": row[1]} for row in result.fetchall()]


# Top Performing Posts Service
async def get_instagram_top_performing_posts(
    db_session: AsyncSession,
    organisation_id: str,
    limit: int = 5,
    order_by: str = "reach",  # or "engagement"
):
    """
    Fetch top performing Instagram posts for an org, ordered by reach or engagement.
    """
    if order_by not in ("reach", "views"):
        order_by = "reach"
    q = (
        select(InstagramMediaMetrics)
        .where(InstagramMediaMetrics.organisation_id == organisation_id)
        .order_by(getattr(InstagramMediaMetrics, order_by).desc())
        .limit(limit)
    )
    result = await db_session.execute(q)
    return result.scalars().all()


# Audience Demographics DB Service
async def get_instagram_audience_demographics(
    db_session: AsyncSession,
    organisation_id: str,
    breakdown_type: str = "country",
    instagram_user_id: str = None,
    limit: int = 45,
):
    """
    Fetch the latest Instagram audience demographics for an org and breakdown type.
    Returns the most recent value per breakdown_value (e.g., per country).
    """
    try:
        account = await get_social_details(organisation_id, "instagram", db_session)
        instagram_user_id = account.social_media_user_id
        filters = [
            InstagramAudienceDemographics.organisation_id == organisation_id,
            InstagramAudienceDemographics.breakdown_type == breakdown_type,
        ]
        if instagram_user_id:
            filters.append(InstagramAudienceDemographics.instagram_user_id == instagram_user_id)

        # Subquery to get the latest collected_at per breakdown_value
        subq = (
            select(
                InstagramAudienceDemographics.breakdown_value,
                func.max(InstagramAudienceDemographics.collected_at).label("max_collected_at")
            )
            .where(*filters)
            .group_by(InstagramAudienceDemographics.breakdown_value)
            .subquery()
        )

        # Join to get the full records
        q = (
            select(InstagramAudienceDemographics)
            .join(
                subq,
                (InstagramAudienceDemographics.breakdown_value == subq.c.breakdown_value) &
                (InstagramAudienceDemographics.collected_at == subq.c.max_collected_at)
            )
            .where(*filters)
            .order_by(InstagramAudienceDemographics.value.desc())
            .limit(limit)
        )
        result = await db_session.execute(q)
        return result.scalars().all()
    except Exception as e:
        logger.error(f"Error fetching Instagram audience demographics: {str(e)}")
        return []
