from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import (
    FastAPI,
    WebSocket,
    status,
    Depends,
    WebSocketDisconnect
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from datetime import datetime, timed<PERSON>ta
from typing import Annotated

from app.database.session import create_db_and_tables
from app.routes import router as socials_router
from app.tasks.scheduler import (
    check_and_post_scheduled_content, scheduled_metrics_update)
from app.utils.dependency import (
    verify_organization,
    get_current_user_from_websocket
)
from app.utils.logger import get_logger
from app.utils.redis_cache import redis_client
from app.services.websocket import (
    register_connection, unregister_connection)
from app.core.config import settings

logger = get_logger(__name__)

app = FastAPI(
    openapi_url="/api/v1/socials/openapi.json", docs_url="/api/v1/socials/docs"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Create the database tables
@app.on_event("startup")
async def on_startup():
    # Create database tables
    logger.info("Creating database tables...")
    try:
        await create_db_and_tables()
        logger.info("Database tables created successfully.")
    except Exception as e:
        logger.error(f"Error during database setup: {e}")


@app.on_event("startup")
async def setup_schedulers():
    app.state.scheduler = AsyncIOScheduler()

    # Schedule content posting
    app.state.scheduler.add_job(
        check_and_post_scheduled_content, "interval",
        seconds=settings.SCHEDULE_PERIOD
    )

    # Schedule metrics updates for all platforms
    # This will run the unified metrics update function that handles all platforms
    app.state.scheduler.add_job(
        scheduled_metrics_update, "interval", minutes=60,
        next_run_time=datetime.now() + timedelta(minutes=1)
        # Start first run after 5 minutes
    )

    app.state.scheduler.start()


@app.on_event("shutdown")
async def shutdown_apscheduler():
    app.state.scheduler.shutdown()


# Custom OpenAPI schema to include bearer token
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    # Base OpenAPI schema generation
    openapi_schema = get_openapi(
        title="KIMEV",
        version="1.0.0",
        description="Do cool AI Stuffs",
        routes=app.routes,
    )

    # Define multiple security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"},
    }
    for path, methods in openapi_schema["paths"].items():
        for method, details in methods.items():
            # Add or update security
            if "security" in details:
                details["security"].append({"Bearer": []})
            else:
                details["security"] = [{"Bearer": []}]

            # Ensure endpoint-level tags take precedence
            if "tags" in details and len(details["tags"]) > 1:
                if "Schedule" in details["tags"]:
                    details["tags"] = ["Schedule"]
                else:
                    details["tags"] = details["tags"][:1]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

app.include_router(
    socials_router,
    prefix="/api/v1/socials",
)


@app.get("/socials_status")
def root_status():
    """
    This is just an introduction to this server
    requests to this endpoint is to confirm if the server is up and running
    """
    return {"message": "Welcome to the socials Service"}


@app.websocket("/ws/messages")
async def messages_ws(
    websocket: WebSocket,
    organisation_id: Annotated[str, Depends(verify_organization)],
    user: Annotated[dict, Depends(get_current_user_from_websocket)]
):
    user_id = user.get("user_id")
    if not user_id:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        logger.info("WebSocket connection rejected: invalid token")
        return
    await websocket.accept()
    register_connection(organisation_id, websocket)
    logger.info(
        f"WebSocket connected: user {user.get('email')}"
    )
    try:
        while True:
            # Just keep the connection alive for now
            data = await websocket.receive_text()
            logger.info(
                f"Received from WS ({user_id}): {data}"
            )
    except WebSocketDisconnect:
        unregister_connection(organisation_id)
        logger.info(f"WebSocket disconnected: user {user_id}")


@app.get("/redis-test")
async def redis_test():
    if not redis_client:
        return {"error": "Redis is not connected"}
    redis_client.set("test_key", "test_value")
    value = redis_client.get("test_key")
    return {"test_key": value}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="127.0.0.1", port=8001)
