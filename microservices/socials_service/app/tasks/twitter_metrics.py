import asyncio
import json
import logging
import base64
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from app.schemas import schema
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.models import model
from app.database.session import SessionLocal
from app.utils.redis_cache import redis_client
from app.utils.logger import get_logger
from app.core.config import settings
import httpx
import tweepy

logger = get_logger(__name__)

# Constants
redis_expiry_time = 60 * 60 * 12  # 12 hours
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)

# Twitter API credentials
bearer_token = settings.TWITTER_BEARER_TOKEN
consumer_key = settings.TWITTER_API_KEY
consumer_secret = settings.TWITTER_API_SECRET_KEY
TWITTER_CLIENT_ID = settings.TWITTER_CLIENT_ID
TWITTER_CLIENT_SECRET = settings.TWITTER_CLIENT_SECRET

async def fetch_and_store_twitter_metrics():
    """
    Fetch all Twitter metrics for all organizations and store them in the database and Redis.
    This function is meant to be called by a scheduler twice a day.
    """
    # logger.info("Starting scheduled Twitter metrics update")

    async with SessionLocal() as db:
        try:
            # Get all Twitter social media accounts
            result = await db.execute(
                select(model.SocialMediaAccount).filter(
                    model.SocialMediaAccount.platform == "twitter",
                    model.SocialMediaAccount.login_status == True
                )
            )
            social_accounts = result.scalars().all()

            # logger.info(f"Found {len(social_accounts)} Twitter accounts to update")

            for account in social_accounts:
                try:
                    # Validate and refresh access token if necessary
                    access_token = await refresh_token_if_needed(account, db)

                    # Initialize the Twitter client
                    client = tweepy.Client(bearer_token=access_token)

                    # Fetch and store overview metrics
                    await fetch_and_store_overview_metrics(account, client, db)

                    # Fetch and store growth trends
                    await fetch_and_store_growth_trends(account, client, db)

                    # Fetch and store top performing tweets
                    await fetch_and_store_top_performing_tweets(account, client, db)

                    # logger.info(f"Successfully updated metrics for Twitter account: {account.username}")

                    # Sleep to avoid rate limiting
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"Error updating metrics for Twitter account {account.username}: {str(e)}")
                    continue

            # Commit all changes
            await db.commit()
            # logger.info("Completed scheduled Twitter metrics update")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in scheduled Twitter metrics update: {str(e)}")

async def refresh_token_if_needed(account, db: AsyncSession):
    """Validate and refresh the Twitter access token if needed"""
    try:
        # Check if token is cached
        cache_key = f"x-access-token:{account.id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            return json.loads(cache_data)

        # Try to validate the token
        try:
            client = tweepy.Client(bearer_token=account.access_token)
            user = client.get_me(user_auth=False)
            if user:
                # Token is valid, cache it
                redis_client.setex(cache_key, redis_expiry_time, json.dumps(account.access_token))
                return account.access_token
        except:
            # Token is invalid, refresh it
            pass

        # Refresh the token
        header = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {base64.b64encode(f'{TWITTER_CLIENT_ID}:{TWITTER_CLIENT_SECRET}'.encode()).decode()}",
        }
        data = {
            "grant_type": "refresh_token",
            "refresh_token": account.refresh_token,
        }

        # Make the request to refresh the token
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                "https://api.twitter.com/2/oauth2/token", headers=header, data=data
            )
            response.raise_for_status()
            token_data = response.json()

        # Extract the new tokens
        new_access_token = token_data.get("access_token")
        new_refresh_token = token_data.get("refresh_token", account.refresh_token)

        # Update the token in the database
        account.access_token = new_access_token
        account.refresh_token = new_refresh_token
        await db.commit()

        # Cache the new token
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(new_access_token))
        return new_access_token

    except Exception as e:
        logger.error(f"Error refreshing token: {e}")
        raise

async def fetch_and_store_overview_metrics(account, client, db: AsyncSession):
    """Fetch and store overview metrics for a Twitter account"""
    try:
        # Fetch user profile to get followers count
        user_response = client.get_me(
            user_fields=["public_metrics"],
            user_auth=False,
        )
        followers_count = user_response.data.public_metrics["followers_count"]

        # Calculate the date range for tweets (last 30 days)
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=30)

        # Fetch tweets within the date range
        tweets_response = client.get_users_tweets(
            id=account.social_media_user_id,
            expansions=["attachments.media_keys"],
            start_time=start_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            end_time=end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            tweet_fields=[
                "created_at", "author_id", "text",
                "public_metrics",
                "non_public_metrics",
                "organic_metrics",
            ],
            media_fields=[
                "duration_ms", "media_key",
                "public_metrics",
                "preview_image_url",
                "type", "url"
            ],
            user_fields=[
                "location", "name",
                "username", "public_metrics"
            ],
            max_results=100,
            user_auth=False,
        )

        # Aggregate metrics
        engagements = 0
        impressions = 0
        reach = 0

        if tweets_response.data:
            for tweet in tweets_response.data:
                # Add engagement metrics (likes + retweets + replies)
                engagements += tweet.public_metrics.get("like_count", 0)
                engagements += tweet.public_metrics.get("retweet_count", 0)
                engagements += tweet.public_metrics.get("reply_count", 0)

                # Add impression metrics
                impressions += tweet.public_metrics.get("impression_count", 0)

                # Add reach metrics (organic impressions)
                if hasattr(tweet, 'organic_metrics'):
                    reach += tweet.organic_metrics.get("impression_count", 0)

        # Create metrics record
        metrics = model.TwitterMetrics(
            organisation_id=account.organisation_id,
            twitter_user_id=account.social_media_user_id,
            followers_count=followers_count,
            engagements=engagements,
            impressions=impressions,
            reach=reach
        )

        # Add metrics to database
        db.add(metrics)

        # Cache metrics in Redis
        cache_key = f"x_overview_{account.organisation_id}"
        cache_data = {
            "followers": followers_count,
            "engagements": engagements,
            "impressions": impressions,
            "reach": reach,
            "updated_at": datetime.now().isoformat()
        }
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))

        # logger.info(f"Updated overview metrics for Twitter account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching overview metrics for {account.username}: {str(e)}")
        raise

async def fetch_and_store_growth_trends(account, client, db: AsyncSession):
    """Fetch and store growth trends for a Twitter account"""
    try:
        # Define date range (last 6 months)
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=6)

        # Fetch impression growth trend
        await fetch_and_store_impression_growth_trend(account, client, db, start_date, end_date)

        # Fetch engagement growth trend
        await fetch_and_store_engagement_growth_trend(account, client, db, start_date, end_date)

        # Fetch click rate growth trend
        await fetch_and_store_click_rate_growth_trend(account, client, db, start_date, end_date)

        # logger.info(f"Updated growth trends for Twitter account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching growth trends for {account.username}: {str(e)}")
        raise

async def fetch_and_store_impression_growth_trend(account, client, db: AsyncSession, start_date, end_date):
    """Fetch and store impression growth trend"""
    try:
        impression_trend_data = []
        current_date = start_date

        while current_date < end_date:
            # Define current chunk's start and end dates (1 month)
            chunk_end_date = current_date + relativedelta(months=1)
            if chunk_end_date > end_date:
                chunk_end_date = end_date

            # Fetch tweets for the current month
            tweets_response = client.get_users_tweets(
                id=account.social_media_user_id,
                start_time=current_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                end_time=chunk_end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                tweet_fields=["created_at", "public_metrics"],
                max_results=100,
                user_auth=False,
            )

            # Calculate total impressions for the month
            total_impressions = 0
            if tweets_response.data:
                for tweet in tweets_response.data:
                    total_impressions += tweet.public_metrics.get("impression_count", 0)

            # Add to trend data
            impression_trend_data.append({
                "month": current_date.strftime("%Y-%m"),
                "impressions": total_impressions
            })

            # Move to the next month
            current_date = chunk_end_date

        # Calculate growth percentages and store in database
        growth_trend = []
        for i in range(len(impression_trend_data)):
            current = impression_trend_data[i]

            # Create growth trend record
            growth = model.TwitterGrowthTrend(
                organisation_id=account.organisation_id,
                twitter_user_id=account.social_media_user_id,
                trend_type="impressions",
                month=current["month"],
                value=current["impressions"]
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = impression_trend_data[i - 1]
                if previous["impressions"] == 0:
                    growth_percentage = 100.0 if current["impressions"] > 0 else 0
                else:
                    growth_percentage = ((current["impressions"] - previous["impressions"]) / previous["impressions"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "impressions": current["impressions"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"x_post_impressions_{account.organisation_id}"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        # logger.info(f"Updated impression growth trend for Twitter account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching impression growth trend for {account.username}: {str(e)}")
        raise

async def fetch_and_store_engagement_growth_trend(account, client, db: AsyncSession, start_date, end_date):
    """Fetch and store engagement growth trend"""
    try:
        engagement_trend_data = []
        current_date = start_date

        while current_date < end_date:
            # Define current chunk's start and end dates (1 month)
            chunk_end_date = current_date + relativedelta(months=1)
            if chunk_end_date > end_date:
                chunk_end_date = end_date

            # Fetch tweets for the current month
            tweets_response = client.get_users_tweets(
                id=account.social_media_user_id,
                start_time=current_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                end_time=chunk_end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                tweet_fields=["created_at", "public_metrics"],
                max_results=100,
                user_auth=False,
            )

            # Calculate total engagements for the month
            total_engagements = 0
            if tweets_response.data:
                for tweet in tweets_response.data:
                    total_engagements += tweet.public_metrics.get("like_count", 0)
                    total_engagements += tweet.public_metrics.get("retweet_count", 0)
                    total_engagements += tweet.public_metrics.get("reply_count", 0)

            # Add to trend data
            engagement_trend_data.append({
                "month": current_date.strftime("%Y-%m"),
                "engagements": total_engagements
            })

            # Move to the next month
            current_date = chunk_end_date

        # Calculate growth percentages and store in database
        growth_trend = []
        for i in range(len(engagement_trend_data)):
            current = engagement_trend_data[i]

            # Create growth trend record
            growth = model.TwitterGrowthTrend(
                organisation_id=account.organisation_id,
                twitter_user_id=account.social_media_user_id,
                trend_type="engagement",
                month=current["month"],
                value=current["engagements"]
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = engagement_trend_data[i - 1]
                if previous["engagements"] == 0:
                    growth_percentage = 100.0 if current["engagements"] > 0 else 0
                else:
                    growth_percentage = ((current["engagements"] - previous["engagements"]) / previous["engagements"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "engagements": current["engagements"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"x_post_engagement_{account.organisation_id}"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        # logger.info(f"Updated engagement growth trend for Twitter account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching engagement growth trend for {account.username}: {str(e)}")
        raise

async def fetch_and_store_click_rate_growth_trend(account, client, db: AsyncSession, start_date, end_date):
    """Fetch and store click rate growth trend"""
    try:
        click_trend_data = []
        current_date = start_date

        while current_date < end_date:
            # Define current chunk's start and end dates (1 month)
            chunk_end_date = current_date + relativedelta(months=1)
            if chunk_end_date > end_date:
                chunk_end_date = end_date

            # Fetch tweets for the current month
            tweets_response = client.get_users_tweets(
                id=account.social_media_user_id,
                start_time=current_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                end_time=chunk_end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                tweet_fields=["created_at", "public_metrics", "non_public_metrics"],
                max_results=100,
                user_auth=False,
            )

            # Calculate click rate for the month
            total_clicks = 0
            total_impressions = 0
            if tweets_response.data:
                for tweet in tweets_response.data:
                    # Get URL clicks if available
                    if hasattr(tweet, 'non_public_metrics'):
                        total_clicks += tweet.non_public_metrics.get("url_link_clicks", 0)

                    # Get impressions
                    total_impressions += tweet.public_metrics.get("impression_count", 0)

            # Calculate click rate
            click_rate = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0

            # Add to trend data
            click_trend_data.append({
                "month": current_date.strftime("%Y-%m"),
                "click_rate": click_rate
            })

            # Move to the next month
            current_date = chunk_end_date

        # Calculate growth percentages and store in database
        growth_trend = []
        for i in range(len(click_trend_data)):
            current = click_trend_data[i]

            # Create growth trend record
            growth = model.TwitterGrowthTrend(
                organisation_id=account.organisation_id,
                twitter_user_id=account.social_media_user_id,
                trend_type="click_rate",
                month=current["month"],
                value=int(current["click_rate"] * 100)  # Store as integer (percentage * 100)
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = click_trend_data[i - 1]
                if previous["click_rate"] == 0:
                    growth_percentage = 100.0 if current["click_rate"] > 0 else 0
                else:
                    growth_percentage = ((current["click_rate"] - previous["click_rate"]) / previous["click_rate"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "click_rate": current["click_rate"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"x_click_rate_{account.organisation_id}_6"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        # logger.info(f"Updated click rate growth trend for Twitter account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching click rate growth trend for {account.username}: {str(e)}")
        raise

async def fetch_and_store_top_performing_tweets(account, client, db: AsyncSession):
    """Fetch and store top performing tweets"""
    try:
        # Fetch tweets from the last 30 days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        tweets_response = client.get_users_tweets(
            id=account.social_media_user_id,
            start_time=start_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            end_time=end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            expansions=["attachments.media_keys"],
            tweet_fields=[
                "created_at", "author_id", "text",
                "public_metrics",
                "non_public_metrics",
                "organic_metrics",
            ],
            media_fields=[
                "duration_ms", "media_key",
                "public_metrics",
                "preview_image_url",
                "type", "url"
            ],
            max_results=100,
            user_auth=False,
        )

        if not tweets_response.data:
            # logger.info(f"No tweets available for Twitter account: {account.username}")
            return

        # Process tweets and calculate engagement score
        tweets_with_scores = []
        media_dict = {}

        # Create media dictionary
        if tweets_response.includes and 'media' in tweets_response.includes:
            for media in tweets_response.includes['media']:
                media_dict[media.media_key] = {
                    "type": media.type,
                    "url": getattr(media, 'url', None)
                }

        for tweet in tweets_response.data:
            # Calculate engagement score
            engagement_score = (
                tweet.public_metrics.get("like_count", 0) * 2 +
                tweet.public_metrics.get("retweet_count", 0) * 3 +
                tweet.public_metrics.get("reply_count", 0) * 1 +
                tweet.public_metrics.get("impression_count", 0) * 0.1
            )

            # Extract media URLs
            media_urls = []
            if hasattr(tweet, 'attachments') and hasattr(tweet.attachments, 'media_keys'):
                for key in tweet.attachments.media_keys:
                    if key in media_dict:
                        media_urls.append(media_dict[key])

            # Create tweet data object
            tweet_data = {
                "id": tweet.id,
                "text": tweet.text,
                "created_at": tweet.created_at,
                "engagement_score": engagement_score,
                "public_metrics": tweet.public_metrics._json if hasattr(tweet.public_metrics, '_json') else vars(tweet.public_metrics),
                "media": media_urls,
                "permalink_url": f"https://twitter.com/user/status/{tweet.id}"
            }

            # Add non-public metrics if available
            if hasattr(tweet, 'non_public_metrics'):
                tweet_data["non_public_metrics"] = tweet.non_public_metrics._json if hasattr(tweet.non_public_metrics, '_json') else vars(tweet.non_public_metrics)

            # Add organic metrics if available
            if hasattr(tweet, 'organic_metrics'):
                tweet_data["organic_metrics"] = tweet.organic_metrics._json if hasattr(tweet.organic_metrics, '_json') else vars(tweet.organic_metrics)

            tweets_with_scores.append(tweet_data)

        # Sort tweets by engagement score and get top 5
        top_tweets = sorted(tweets_with_scores, key=lambda x: x["engagement_score"], reverse=True)[:5]

        # Store top tweets in database
        for tweet in top_tweets:
            # Extract metrics
            public_metrics = tweet.get("public_metrics", {})
            non_public_metrics = tweet.get("non_public_metrics", {})
            organic_metrics = tweet.get("organic_metrics", {})

            # Create top performing tweet record
            top_tweet = model.TwitterTopPerformingTweet(
                organisation_id=account.organisation_id,
                twitter_user_id=account.social_media_user_id,
                tweet_id=tweet["id"],
                text=tweet["text"],
                tweet_created_at=tweet["created_at"],
                permalink_url=tweet["permalink_url"],
                impressions=public_metrics.get("impression_count", 0),
                likes=public_metrics.get("like_count", 0),
                retweets=public_metrics.get("retweet_count", 0),
                replies=public_metrics.get("reply_count", 0),
                url_clicks=non_public_metrics.get("url_link_clicks", 0) if non_public_metrics else 0,
                profile_clicks=non_public_metrics.get("user_profile_clicks", 0) if non_public_metrics else 0
            )

            # Calculate engagement rate
            if top_tweet.impressions > 0:
                top_tweet.engagement_rate = ((top_tweet.likes + top_tweet.retweets + top_tweet.replies) / top_tweet.impressions) * 100

            # Set media and metrics
            top_tweet.set_media(tweet.get("media"))

            # Combine all metrics
            all_metrics = {
                "public_metrics": public_metrics,
                "non_public_metrics": non_public_metrics,
                "organic_metrics": organic_metrics
            }
            top_tweet.set_metrics(all_metrics)

            # Add to database
            db.add(top_tweet)

        # Cache top tweets in Redis
        cache_key = f"x_top_contents_{account.organisation_id}"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(top_tweets))

        # logger.info(f"Updated top performing tweets for Twitter account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching top performing tweets for {account.username}: {str(e)}")
        raise

# Function to be called by the scheduler
async def fetch_and_store_twitter_metrics_for_account(account_id: str):
    """
    Fetch and store Twitter metrics for a specific account
    This is used when a new account is connected to immediately collect metrics
    """
    # logger.info(f"Starting immediate Twitter metrics update for account ID: {account_id}")

    async with SessionLocal() as db:
        try:
            # Get the specific Twitter social media account
            result = await db.execute(
                select(model.SocialMediaAccount).filter(
                    model.SocialMediaAccount.id == account_id,
                    model.SocialMediaAccount.platform == "twitter",
                    model.SocialMediaAccount.login_status == True
                )
            )
            account = result.scalars().first()

            if not account:
                logger.error(f"Twitter account with ID {account_id} not found or not active")
                return

            # Validate and refresh access token if necessary
            access_token = await refresh_token_if_needed(account, db)

            # Initialize the Twitter client
            client = tweepy.Client(bearer_token=access_token)

            # Fetch and store overview metrics
            await fetch_and_store_overview_metrics(account, client, db)

            # Fetch and store growth trends
            end_date = datetime.now()
            start_date = end_date - relativedelta(months=6)
            await fetch_and_store_impression_growth_trend(account, client, db, start_date, end_date)
            await fetch_and_store_engagement_growth_trend(account, client, db, start_date, end_date)
            await fetch_and_store_click_rate_growth_trend(account, client, db, start_date, end_date)

            # Fetch and store top performing tweets
            await fetch_and_store_top_performing_tweets(account, client, db)

            # Commit all changes
            await db.commit()
            # logger.info(f"Completed immediate Twitter metrics update for account: {account.username}")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in immediate Twitter metrics update for account ID {account_id}: {str(e)}")

async def scheduled_twitter_metrics_update():
    """
    Function to be called by the scheduler to update Twitter metrics for all accounts
    """
    await fetch_and_store_twitter_metrics()
