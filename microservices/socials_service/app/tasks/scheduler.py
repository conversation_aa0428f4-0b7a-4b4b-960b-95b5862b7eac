import asyncio
from datetime import datetime

import pytz
from app.database.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.model import ScheduledContent
from app.routes.facebook import post_to_facebook
from app.routes.instagram import (
    create_ig_single_post, create_carousel_container)
from app.routes.twitter import post_tweet as post_to_twitter
from app.schemas.instagram_schema import (
    InstagramCreate, InstagramCarouselCreate)
from app.schemas.schema import ApprovalStatus, ScheduleContentStatus
from app.tasks.facebook_metrics import scheduled_facebook_metrics_update
from app.tasks.instagram_metrics import scheduled_instagram_metrics_update
from app.tasks.twitter_metrics import scheduled_twitter_metrics_update
from app.utils.logger import get_logger
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger(__name__)


async def check_and_post_scheduled_content():
    while True:
        async with SessionLocal() as db_session:
            await update_approval_status(db_session)
            await post_scheduled_content(db_session)
        await asyncio.sleep(60)


def create_instagram_create_object(media_url: str, media_type: str, is_carousel_item: bool) -> InstagramCreate:
    """
    Helper function to create InstagramCreate objects for both image and video types.
    """
    if media_type == "image":
        return InstagramCreate(
            image_url=media_url,
            media_type=media_type,
            is_carousel_item=is_carousel_item
        )
    else:
        return InstagramCreate(
            video_url=media_url,
            media_type=media_type,
            is_carousel_item=is_carousel_item
        )


async def post_scheduled_content(db_session: AsyncSession):
    logger.info("Starting post_scheduled_content")
    now = datetime.now(pytz.utc)

    result = await db_session.execute(
        select(ScheduledContent).filter(
            ScheduledContent.post_time <= now,
            ScheduledContent.status == ScheduleContentStatus.scheduled,
            ScheduledContent.approval_status == ApprovalStatus.approved,
        )
    )
    scheduled_contents = result.scalars().all()
    logger.info(f"Found {len(scheduled_contents)} scheduled contents to post.")

    for content in scheduled_contents:
        # Allow a 5-minute padding before and after the scheduled time
        time_difference = abs((content.post_time - now).total_seconds())

        if time_difference <= 300:
            # Atomically set status to 'publishing' if still 'scheduled'
            update_stmt = (
                update(ScheduledContent)
                .where(
                    ScheduledContent.id == content.id,
                    ScheduledContent.status == ScheduleContentStatus.scheduled
                )
                .values(status="publishing")
            )
            result = await db_session.execute(update_stmt)
            await db_session.commit()
            if result.rowcount == 0:
                logger.info(f"Content {content.id} already picked by another process, skipping.")
                continue  # Already picked by another process

            if "facebook" in content.platforms:
                try:
                    response = await post_to_facebook(
                        organisation_id=content.organization_id,
                        img_urls=content.media_links,
                        message=content.content,
                        schedule_content_id=content.id,
                        db_session=db_session,
                    )
                    logger.info(
                        f"Successfully posted content {content.id} to Facebook."
                    )
                    if response:
                        # Set status to published
                        update_stmt2 = (
                            update(ScheduledContent)
                            .where(ScheduledContent.id == content.id)
                            .values(status=ScheduleContentStatus.published)
                        )
                        await db_session.execute(update_stmt2)
                        await db_session.commit()

                except Exception as e:
                    logger.error(
                        f"Failed to post content {content.id} to Facebook: {str(e)}"
                    )
                    # Set status to draft on error
                    update_stmt2 = (
                        update(ScheduledContent)
                        .where(ScheduledContent.id == content.id)
                        .values(status=ScheduleContentStatus.draft)
                    )
                    await db_session.execute(update_stmt2)
                    await db_session.commit()

            if "twitter" in content.platforms:
                try:
                    response = await post_to_twitter(
                        organisation_id=content.organization_id,
                        schedulecontent_id=content.id,
                        img_urls=content.media_links,
                        tweet=content.content,
                        db_session=db_session,
                    )
                    logger.info(f"Successfully posted content {content.id} to Twitter.")
                    if response:
                        content.status = ScheduleContentStatus.published
                except Exception as e:
                    logger.error(
                        f"Failed to post content {content.id} to Twitter: {str(e)}"
                    )
                    # TODO: add a notification here with the reason
                    content.status = ScheduleContentStatus.draft

            if "instagram" in content.platforms:
                try:
                    if content.is_carousel:
                        # Handle carousel post
                        medias = []
                        for i, media_url in enumerate(content.media_links):
                            # Determine media type from URL or media_items_info
                            item_media_type = "image"  # Default

                            # If media_items_info is available, use it
                            if content.media_items_info and i < len(content.media_items_info):
                                item_media_type = content.media_items_info[i].get("media_type", "image")
                            # Otherwise detect from file extension
                            elif media_url.lower().endswith((".mp4", ".mov")):
                                item_media_type = "video"

                            # Create InstagramCreate object for each item
                            data = create_instagram_create_object(
                                media_url=media_url,
                                media_type=item_media_type,
                                is_carousel_item=True
                            )
                            medias.append(data)

                        # Create carousel data
                        carousel_data = InstagramCarouselCreate(
                            medias=medias,
                            caption=content.content
                        )

                        # Call carousel endpoint
                        response = await create_carousel_container(
                            organisation_id=content.organization_id,
                            data=carousel_data,
                            db_session=db_session,
                            schedule_content_id=content.id
                        )

                    elif len(content.media_links) == 1:
                        if content.media_links[0].endswith(".jpg") or content.media_links[0].endswith(".jpeg"):
                            content.media_type = "image"
                        data = create_instagram_create_object(
                            media_url=content.media_links[0],
                            media_type=content.media_type,
                            is_carousel_item=False
                        )
                        # pass it to the post endpoint
                        response = await create_ig_single_post(
                            organisation_id=content.organization_id,
                            data=data,
                            db_session=db_session,
                            schedule_content_id=content.id
                        )
                    logger.info(f"Successfully posted content {content.id} to instagram.")
                    if response:
                        content.status = ScheduleContentStatus.published
                except Exception as e:
                    logger.error(
                        f"Failed to post content {content.id} to Instagram: {str(e)}"
                    )
                    # TODO: add a notification here
                    content.status = ScheduleContentStatus.draft
        else:
            logger.error("Posting time elapsed, re-schedule the content")
            content.status = ScheduleContentStatus.draft

    await db_session.commit()
    return


async def update_approval_status(db_session: AsyncSession):
    """Update content status if posting time is over

    Args:
        db_session (AsyncSession): The database session
    """
    logger.info("Starting update_approval_status")
    now = datetime.now(pytz.utc)

    result = await db_session.execute(
        select(ScheduledContent).filter(
            ScheduledContent.post_time < now,
            ScheduledContent.status == ScheduleContentStatus.scheduled,
            ScheduledContent.approval_status != ApprovalStatus.approved,
        )
    )
    scheduled_contents = result.scalars().all()

    logger.info(
        f"Found {len(scheduled_contents)} contents with pending approval status."
    )
    for content in scheduled_contents:
        content.approval_status = ApprovalStatus.expired
        content.status = ScheduleContentStatus.draft

    await db_session.commit()
    return


async def scheduled_metrics_update():
    """
    Function to be called by the scheduler to update metrics for all platforms
    This runs twice a day to refresh all metrics
    """
    while True:
        try:
            logger.info("Starting scheduled metrics update for all platforms")

            # # Update Twitter metrics
            # await scheduled_twitter_metrics_update()

            # # Update Facebook metrics
            await scheduled_facebook_metrics_update()

            # Update Instagram metrics
            # await scheduled_instagram_metrics_update()

            logger.info("Completed scheduled metrics update for all platforms")

        except Exception as e:
            logger.error(f"Error in scheduled metrics update: {str(e)}")

        # Run every 12 hours
        # await asyncio.sleep(120)  # Check every minute
        await asyncio.sleep(3 * 60 * 60)
