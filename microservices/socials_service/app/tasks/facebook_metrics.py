import asyncio
import json
import traceback
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional

import httpx
from app.core.config import settings
from app.database.session import SessionLocal
from app.models import model
from app.services.facebook import (
    composite_score,
    extract_images,
    fetch_conversations_and_messages,
    save_comments_to_db,
)
from app.services.facebook_post_perf import fetch_all_facebook_posts
from app.utils.logger import get_logger
from app.utils.redis_cache import redis_client
from dateutil.relativedelta import relativedelta
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

logger = get_logger(__name__)

# Constants
FACEBOOK_GRAPH_API_URL = settings.FACEBOOK_GRAPH_API_URL
redis_expiry_time = settings.REDIS_LONG_CACHE_EXPIRY_SECONDS
redis_api_expiry_time = settings.REDIS_CACHE_EXPIRY_SECONDS
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)

# Configuration for Facebook API metrics
class MetricType(Enum):
    FOLLOWERS = "followers"
    IMPRESSIONS = "impressions"
    REACH = "reach"
    ENGAGEMENTS = "engagements"

@dataclass
class MetricConfig:
    """Configuration for Facebook API metrics"""
    primary_metrics: List[str]
    fallback_metrics: List[str]
    is_cumulative: bool = False

# Updated metric configurations based on 2024 API changes
METRIC_CONFIGS = {
    MetricType.FOLLOWERS: MetricConfig(
        primary_metrics=['page_follows', 'page_follower_count'],
        fallback_metrics=['page_fans_total', 'page_fans'],
        is_cumulative=True
    ),
    MetricType.IMPRESSIONS: MetricConfig(
        primary_metrics=['page_impressions'],
        fallback_metrics=['page_impressions_total'],
        is_cumulative=False
    ),
    MetricType.REACH: MetricConfig(
        primary_metrics=['page_impressions_unique'],
        fallback_metrics=[],
        is_cumulative=False
    ),
    MetricType.ENGAGEMENTS: MetricConfig(
        primary_metrics=['page_engaged_users', 'page_post_engagements'],
        fallback_metrics=[],
        is_cumulative=False
    )
}

@dataclass
class OperationResult:
    """Result of an individual operation"""
    success: bool
    operation: str
    account_username: str
    error_message: Optional[str] = None
    warning_message: Optional[str] = None
    data: Optional[Dict] = None


class FacebookAPIClient:
    """Centralized Facebook API client with error handling and rate limiting"""

    def __init__(self):
        self.timeout = timeout
        self.base_url = FACEBOOK_GRAPH_API_URL

    async def get_insights(self, page_id: str, access_token: str,
                          metric: str, period: str = "day",
                          since: Optional[int] = None, until: Optional[int] = None) -> Dict:
        """Get insights for a specific metric with error handling"""
        params = {
            "metric": metric,
            "period": period,
            "access_token": access_token
        }
        if since:
            params["since"] = str(since)
        if until:
            params["until"] = str(until)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/{page_id}/insights", params=params)
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 429:  # Rate limited
                    logger.warning(f"Rate limited for metric {metric}")
                    raise
                elif e.response.status_code in [400, 403]:  # Bad request or forbidden
                    error_data = e.response.json() if e.response.content else {}
                    error_msg = error_data.get('error', {}).get('message', 'Unknown error')
                    logger.warning(f"API error for metric {metric}: {error_msg}")
                    return {"data": [], "error": error_data}
                else:
                    raise
            except Exception as e:
                logger.error(f"Unexpected error fetching metric {metric}: {str(e)}")
                raise

    async def get_page_posts(self, page_id: str, access_token: str, fields: str) -> Dict:
        """Get page posts with error handling"""
        params = {
            "access_token": access_token,
            "fields": fields
        }

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(f"{self.base_url}/{page_id}/posts", params=params)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Error fetching posts for page {page_id}: {str(e)}")
                raise


class ErrorTracker:
    """Track errors and successes for detailed reporting"""

    def __init__(self):
        self.results: List[OperationResult] = []

    def add_result(self, result: OperationResult):
        """Add an operation result"""
        self.results.append(result)

    def add_success(self, operation: str, account_username: str, data: Optional[Dict] = None):
        """Add a successful operation"""
        self.add_result(OperationResult(
            success=True,
            operation=operation,
            account_username=account_username,
            data=data
        ))

    def add_error(self, operation: str, account_username: str, error_message: str):
        """Add a failed operation"""
        self.add_result(OperationResult(
            success=False,
            operation=operation,
            account_username=account_username,
            error_message=error_message
        ))

    def add_warning(self, operation: str, account_username: str, warning_message: str):
        """Add a warning"""
        self.add_result(OperationResult(
            success=True,
            operation=operation,
            account_username=account_username,
            warning_message=warning_message
        ))

    def get_summary(self) -> Dict:
        """Get summary of all operations"""
        total = len(self.results)
        successful = sum(1 for r in self.results if r.success)
        failed = total - successful
        warnings = sum(1 for r in self.results if r.warning_message)

        return {
            "total_operations": total,
            "successful": successful,
            "failed": failed,
            "warnings": warnings,
            "success_rate": (successful / total * 100) if total > 0 else 0
        }

    def get_account_summary(self) -> Dict:
        """Get summary by account"""
        accounts = {}
        for result in self.results:
            username = result.account_username
            if username not in accounts:
                accounts[username] = {"total": 0, "successful": 0, "failed": 0, "warnings": 0}

            accounts[username]["total"] += 1
            if result.success:
                accounts[username]["successful"] += 1
            else:
                accounts[username]["failed"] += 1
            if result.warning_message:
                accounts[username]["warnings"] += 1

        return accounts


class FacebookMetricsCollector:
    """Main class for collecting Facebook metrics with improved error handling"""

    def __init__(self):
        self.api_client = FacebookAPIClient()
        self.error_tracker = ErrorTracker()

    async def collect_page_metrics(self, account, db: AsyncSession) -> bool:
        """Collect page overview metrics with fallback handling"""
        try:
            username = account.username

            # Define date range (last 30 days)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            since = int(start_date.timestamp())
            until = int(end_date.timestamp())

            # Collect metrics using fallback strategy
            metrics_data = {}
            for metric_type, config in METRIC_CONFIGS.items():
                value = await self._get_metric_with_fallback(
                    account.page_id, account.page_access_token,
                    config, since, until
                )
                metrics_data[metric_type.value] = value

            # Calculate engagement rate
            impressions = metrics_data.get('impressions', 0)
            engagements = metrics_data.get('engagements', 0)
            engagement_rate = (engagements / impressions * 100) if impressions > 0 else 0

            # Create and store metrics record
            metrics = model.FacebookPageMetrics(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                total_followers=metrics_data.get('followers', 0),
                total_impressions=metrics_data.get('impressions', 0),
                total_reach=metrics_data.get('reach', 0),
                total_engagements=metrics_data.get('engagements', 0),
                engagement_rate=round(engagement_rate, 2)
            )
            db.add(metrics)

            # Cache in Redis
            await self._cache_page_metrics(account.organisation_id, metrics_data, engagement_rate)

            self.error_tracker.add_success("page_metrics", username, metrics_data)
            # logger.info(f"Successfully collected page metrics for {username}")
            return True

        except Exception as e:
            error_msg = f"Error collecting page metrics: {str(e)}"
            self.error_tracker.add_error("page_metrics", username, error_msg)
            logger.error(f"Page metrics failed for {username}: {error_msg}")
            return False

    async def _get_metric_with_fallback(self, page_id: str, access_token: str,
                                       config: MetricConfig, since: int, until: int) -> int:
        """Get metric value with fallback to alternative metrics"""
        # Try primary metrics first
        for metric in config.primary_metrics:
            try:
                data = await self.api_client.get_insights(
                    page_id, access_token, metric, "day", since, until
                )
                if 'data' in data and len(data['data']) > 0:
                    values = [entry['value'] for entry in data['data'][0].get('values', [])]
                    if values:
                        return values[-1] if config.is_cumulative else sum(values)
            except Exception as e:
                logger.warning(f"Failed to get metric {metric}: {str(e)}")
                continue

        # Try fallback metrics
        for metric in config.fallback_metrics:
            try:
                data = await self.api_client.get_insights(
                    page_id, access_token, metric, "day", since, until
                )
                if 'data' in data and len(data['data']) > 0:
                    values = [entry['value'] for entry in data['data'][0].get('values', [])]
                    if values:
                        return values[-1] if config.is_cumulative else sum(values)
            except Exception as e:
                logger.warning(f"Failed to get fallback metric {metric}: {str(e)}")
                continue

        logger.warning(f"All metrics failed for {config.primary_metrics}")
        return 0

    async def _cache_page_metrics(self, organisation_id: str, metrics_data: Dict, engagement_rate: float):
        """Cache page metrics in Redis"""
        cache_key = f"fb_overview_{organisation_id}"
        cache_data = {
            "total_followers": metrics_data.get('followers', 0),
            "total_impressions": metrics_data.get('impressions', 0),
            "total_reach": metrics_data.get('reach', 0),
            "total_engagements": metrics_data.get('engagements', 0),
            "engagement_rate": round(engagement_rate, 2),
            "updated_at": datetime.now().isoformat()
        }
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))


class GrowthTrendCollector:
    """Generic growth trend collector to eliminate code duplication"""

    def __init__(self, api_client: FacebookAPIClient, error_tracker: ErrorTracker):
        self.api_client = api_client
        self.error_tracker = error_tracker

    async def collect_growth_trend(self, account, db: AsyncSession, trend_type: str,
                                 metric: str, period: str = "week") -> bool:
        """Generic method to collect growth trends for any metric"""
        try:
            username = account.username
            page_id = account.page_id
            page_access_token = account.page_access_token
            organisation_id = account.organisation_id

            # Define date range (last 6 months)
            end_date = datetime.now()
            start_date = end_date - relativedelta(months=6)

            trend_data = []
            current_date = start_date

            # Collect data month by month
            while current_date < end_date:
                chunk_end_date = current_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                since_date = current_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                try:
                    # Handle special case for click rate (needs multiple metrics)
                    if trend_type == "click_rate":
                        value = await self._collect_click_rate_data(
                            page_id, page_access_token, since_date, until_date
                        )
                    else:
                        value = await self._collect_single_metric_data(
                            page_id, page_access_token, metric, period, since_date, until_date
                        )

                    trend_data.append({
                        "month": current_date.strftime("%Y-%m"),
                        "value": value
                    })

                except Exception as e:
                    logger.warning(f"Failed to collect {trend_type} data for {current_date.strftime('%Y-%m')}: {str(e)}")
                    trend_data.append({
                        "month": current_date.strftime("%Y-%m"),
                        "value": 0
                    })

                current_date = chunk_end_date

            # Calculate growth percentages and store in database
            await self._store_growth_trends(db, organisation_id, page_id, trend_type, trend_data)

            # Cache the results
            await self._cache_growth_trends(organisation_id, trend_type, trend_data)

            self.error_tracker.add_success(f"{trend_type}_growth", username)
            # logger.info(f"Successfully collected {trend_type} growth trend for {username}")
            return True

        except Exception as e:
            error_msg = f"Error collecting {trend_type} growth trend: {str(e)}"
            self.error_tracker.add_error(f"{trend_type}_growth", username, error_msg)
            logger.error(f"{trend_type} growth trend failed for {username}: {error_msg}")
            return False

    async def _collect_single_metric_data(self, page_id: str, access_token: str,
                                        metric: str, period: str, since_date: str, until_date: str) -> int:
        """Collect data for a single metric"""
        data = await self.api_client.get_insights(page_id, access_token, metric, period)

        if data.get("data") and len(data["data"]) > 0:
            values = data["data"][0].get("values", [])
            return sum(value.get("value", 0) for value in values)
        return 0

    async def _collect_click_rate_data(self, page_id: str, access_token: str,
                                     since_date: str, until_date: str) -> float:
        """Collect click rate data (requires clicks and impressions)"""
        try:
            # Get both metrics in one call
            data = await self.api_client.get_insights(
                page_id, access_token, "post_clicks,page_impressions", "week"
            )

            total_clicks = 0
            total_impressions = 0

            if data.get("data") and len(data["data"]) > 0:
                for item in data["data"]:
                    if item["name"] == "post_clicks":
                        total_clicks = sum(v.get("value", 0) for v in item.get("values", []))
                    elif item["name"] == "page_impressions":
                        total_impressions = sum(v.get("value", 0) for v in item.get("values", []))

            return (total_clicks / total_impressions * 100) if total_impressions > 0 else 0

        except Exception as e:
            logger.warning(f"Failed to collect click rate data: {str(e)}")
            return 0

    async def _store_growth_trends(self, db: AsyncSession, organisation_id: str,
                                 page_id: str, trend_type: str, trend_data: List[Dict]) -> None:
        """Store growth trends in database with growth percentage calculation"""
        for i, current in enumerate(trend_data):
            # Calculate growth percentage
            growth_percentage = None
            if i > 0:
                previous = trend_data[i - 1]
                if previous["value"] == 0:
                    growth_percentage = 100.0 if current["value"] > 0 else 0
                else:
                    growth_percentage = ((current["value"] - previous["value"]) / previous["value"]) * 100

            # Check if record exists
            existing_record = await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == organisation_id,
                    model.FacebookGrowthTrend.trend_type == trend_type,
                    model.FacebookGrowthTrend.month == current["month"]
                )
            )
            existing = existing_record.scalars().first()

            # Store value appropriately based on trend type
            if trend_type == "click_rate":
                # Store click rate as integer (percentage * 100)
                value = int(current["value"] * 100)
            else:
                value = current["value"]

            if existing:
                # Update existing record
                existing.page_id = page_id
                existing.value = value
                existing.growth_percentage = growth_percentage
                existing.collected_at = func.now()
            else:
                # Create new record
                growth = model.FacebookGrowthTrend(
                    organisation_id=organisation_id,
                    page_id=page_id,
                    trend_type=trend_type,
                    month=current["month"],
                    value=value,
                    growth_percentage=growth_percentage
                )
                db.add(growth)

    async def _cache_growth_trends(self, organisation_id: str, trend_type: str,
                                 trend_data: List[Dict]) -> None:
        """Cache growth trends in Redis"""
        # Calculate growth percentages for caching
        cache_data = []
        for i, current in enumerate(trend_data):
            growth_percentage = None
            if i > 0:
                previous = trend_data[i - 1]
                if previous["value"] == 0:
                    growth_percentage = 100.0 if current["value"] > 0 else 0
                else:
                    growth_percentage = ((current["value"] - previous["value"]) / previous["value"]) * 100

            cache_data.append({
                "month": current["month"],
                trend_type: current["value"],
                "growth": growth_percentage
            })

        cache_key = f"fb_{trend_type}_growth_trend_{organisation_id}_6"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))


async def fetch_and_store_facebook_metrics():
    """
    Fetch all Facebook metrics for all organizations and store them in the database and Redis.
    This function is meant to be called by a scheduler twice a day.
    Uses the new modular architecture for improved reliability and maintainability.
    """
    # logger.info("Starting scheduled Facebook metrics update")

    # Initialize collectors
    metrics_collector = FacebookMetricsCollector()
    growth_collector = GrowthTrendCollector(
        metrics_collector.api_client,
        metrics_collector.error_tracker
    )

    async with SessionLocal() as db:
        try:
            # Get all Facebook social media accounts
            try:
                result = await db.execute(
                    select(model.SocialMediaAccount)
                    .options(selectinload(model.SocialMediaAccount.posts))
                    .where(
                        model.SocialMediaAccount.platform == "facebook",
                        model.SocialMediaAccount.login_status.is_(True)
                    )
                )
                social_accounts = result.scalars().all()

                # Ensure all accounts are properly loaded
                for account in social_accounts:
                    _ = account.page_id
                    _ = account.username
                    _ = account.access_token
                    _ = account.page_access_token
                    _ = account.organisation_id

                total_accounts = len(social_accounts)
                # logger.info(f"Found {total_accounts} Facebook accounts to update")

            except SQLAlchemyError as e:
                logger.error(f"Database error when fetching Facebook accounts: {str(e)}")
                raise

            # Process accounts in batches
            batch_size = 10
            for i in range(0, total_accounts, batch_size):
                batch = social_accounts[i:i+batch_size]
                # logger.info(f"Processing batch {i//batch_size + 1} of {(total_accounts + batch_size - 1)//batch_size}")

                for account in batch:
                    try:
                        await db.refresh(account)

                        # Process each metric type with individual error handling
                        await process_account_metrics(account, db, metrics_collector, growth_collector)

                    except Exception as e:
                        logger.error(f"Error processing account {account.id}: {str(e)}")
                        metrics_collector.error_tracker.add_error("account_processing", account.username, str(e))
                        continue

                    # Rate limiting delay
                    await asyncio.sleep(1)

                # Commit after each batch
                try:
                    await db.commit()
                    # logger.info(f"Committed batch {i//batch_size + 1}")
                except SQLAlchemyError as e:
                    await db.rollback()
                    logger.error(f"Database error when committing batch {i//batch_size + 1}: {str(e)}")

            # Log final summary
            summary = metrics_collector.error_tracker.get_summary()
            account_summary = metrics_collector.error_tracker.get_account_summary()

            # logger.info("Facebook metrics update completed:")
            # logger.info(f"Total operations: {summary['total_operations']}")
            # logger.info(f"Successful: {summary['successful']} ({summary['success_rate']:.1f}%)")
            # logger.info(f"Failed: {summary['failed']}")
            # logger.info(f"Warnings: {summary['warnings']}")
            # logger.info(f"Accounts processed: {len(account_summary)}")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in scheduled Facebook metrics update: {str(e)}")
            logger.error(traceback.format_exc())


async def process_account_metrics(account, db: AsyncSession,
                                metrics_collector: FacebookMetricsCollector,
                                growth_collector: GrowthTrendCollector):
    """Process all metrics for a single account"""
    username = account.username

    # 1. Update conversations and messages
    try:
        await fetch_conversations_and_messages(account, db)
        metrics_collector.error_tracker.add_success("conversations", username)
        # logger.info(f"Conversations and messages updated for {username}")
    except Exception as e:
        error_msg = f'Error updating conversations and messages: {str(e)}'
        metrics_collector.error_tracker.add_error("conversations", username, error_msg)
        logger.error(f"Conversations failed for {username}: {error_msg}")

    # 2. Update comments for posts
    await process_post_comments(account, db, metrics_collector.error_tracker)

    # 3. Fetch and store page metrics (overview)
    await metrics_collector.collect_page_metrics(account, db)

    # 4. Fetch and store audience demographics
    try:
        await fetch_and_store_audience_demographics(account, db)
        metrics_collector.error_tracker.add_success("audience_demographics", username)
    except Exception as e:
        error_msg = f"Error fetching audience demographics: {str(e)}"
        metrics_collector.error_tracker.add_error("audience_demographics", username, error_msg)
        logger.error(f"Audience demographics failed for {username}: {error_msg}")

    # 5. Fetch and store growth trends using the new collector
    growth_trends = [
        ("audience", "page_fans", "days_28"),
        ("engagement", "page_post_engagements", "week"),
        ("reach", "page_impressions_unique", "week"),
        ("click_rate", None, "week")  # Special case handled in collector
    ]

    for trend_type, metric, period in growth_trends:
        await growth_collector.collect_growth_trend(account, db, trend_type, metric, period)

    # 6. Fetch and store top performing posts
    try:
        organisation_id = account.organisation_id
        await fetch_all_facebook_posts(db, organisation_id)
        metrics_collector.error_tracker.add_success("top_posts", username)
        # logger.info(f"Successfully updated top performing posts for {username}")
    except Exception as e:
        error_msg = f"Error fetching top performing posts: {str(e)}"
        metrics_collector.error_tracker.add_error("top_posts", username, error_msg)
        logger.error(f"Top posts failed for {username}: {error_msg}")


async def process_post_comments(account, db: AsyncSession, error_tracker: ErrorTracker):
    """Process comments for all posts of an account"""
    username = account.username
    comments_processed = 0
    comments_skipped = 0
    comments_failed = 0

    try:
        access_token = account.access_token
        for post in account.posts:
            result = await save_comments_to_db(post.post_id, access_token, db)

            if isinstance(result, dict):
                if result.get("status") == "success":
                    comments_processed += int(result.get("comments_count", 0))
                elif result.get("status") == "skipped":
                    comments_skipped += 1
                    reason = result.get("reason", "unknown")
                    error_tracker.add_warning("comments", username, f"Skipped post {post.post_id}: {reason}")
                elif result.get("status") == "error":
                    comments_failed += 1
                    reason = result.get("reason", "unknown")
                    message = result.get("message", "Unknown error")
                    error_tracker.add_error("comments", username, f"Failed post {post.post_id}: {reason} - {message}")
            else:
                comments_processed += 1

        # logger.info(f"Comments for {username}: {comments_processed} processed, {comments_skipped} skipped, {comments_failed} failed")

        if comments_failed == 0:
            error_tracker.add_success("comments", username, {
                "processed": comments_processed,
                "skipped": comments_skipped
            })

    except Exception as e:
        error_msg = f'Unexpected error updating comments: {str(e)}'
        error_tracker.add_error("comments", username, error_msg)
        logger.error(f"Comments processing failed for {username}: {error_msg}")


async def fetch_and_store_page_metrics(account, db: AsyncSession):
    """
    Legacy function - now uses the new FacebookMetricsCollector
    Kept for backward compatibility
    """
    collector = FacebookMetricsCollector()
    return await collector.collect_page_metrics(account, db)


async def fetch_and_store_audience_demographics(account, db: AsyncSession):
    """Fetch and store audience demographics"""
    try:
        # Fetch audience demographics from Facebook API
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                params={
                    "metric": "page_fans,page_fans_locale,page_fans_city,page_fans_country,page_fan_adds,page_fan_adds_unique,page_fan_removes,page_fan_removes_unique",
                    "access_token": account.page_access_token,
                },
            )
            response.raise_for_status()
            audience_data = response.json()

        # Process audience data
        if not audience_data.get("data"):
            # logger.info(f"No audience demographic data available for {account.username}")
            return

        # Extract and sort country data
        country_data = audience_data["data"][0].get("values", [{}])[0].get("value", {})
        # # logger.info(audience_data)
        top_countries = sorted(
            country_data.items(), key=lambda item: item[1], reverse=True
        )[:10]

        # Extract and sort city data
        city_data = audience_data["data"][1].get("values", [{}])[0].get("value", {})
        top_cities = sorted(
            city_data.items(), key=lambda item: item[1], reverse=True
        )[:10]

        # Extract locale demographics
        locale_data = audience_data["data"][2].get("values", [{}])[0].get("value", {})

        # Create demographics record
        demographics = model.FacebookAudienceDemographics(
            organisation_id=account.organisation_id,
            page_id=account.page_id
        )
        demographics.set_top_countries(dict(top_countries))
        demographics.set_top_cities(dict(top_cities))
        demographics.set_locales(locale_data)

        # Add demographics to database
        db.add(demographics)

        # # Cache demographics in Redis
        # cache_key = f"fb_audience_demographics_{account.organisation_id}"
        # cache_data = {
        #     "top_countries": top_countries,
        #     "top_cities": top_cities,
        #     "locales": locale_data,
        #     "updated_at": datetime.now().isoformat()
        # }
        # redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))

        # logger.info(f"Updated audience demographics for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching audience demographics for {account.username}: {str(e)}")
        raise


async def fetch_and_store_growth_trends(account, db: AsyncSession):
    """
    Legacy function - now uses the new GrowthTrendCollector
    Kept for backward compatibility
    """
    api_client = FacebookAPIClient()
    error_tracker = ErrorTracker()
    growth_collector = GrowthTrendCollector(api_client, error_tracker)

    # Collect all growth trends using the new collector
    growth_trends = [
        ("audience", "page_fans", "days_28"),
        ("engagement", "page_post_engagements", "week"),
        ("reach", "page_impressions_unique", "week"),
        ("click_rate", None, "week")  # Special case handled in collector
    ]

    success_count = 0
    for trend_type, metric, period in growth_trends:
        if await growth_collector.collect_growth_trend(account, db, trend_type, metric, period):
            success_count += 1

    if success_count == len(growth_trends):
        # logger.info(f"Updated all growth trends for Facebook account: {account.username}")
    else:
        logger.warning(f"Updated {success_count}/{len(growth_trends)} growth trends for {account.username}")

    # If any failed, raise an exception for backward compatibility
    if success_count == 0:
        raise Exception("All growth trend collections failed")


async def fetch_and_store_top_performing_posts(account, db: AsyncSession):
    """Fetch and store top performing posts"""
    try:
        # Fetch posts directly from Facebook API
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/posts",
                params={
                    "access_token": account.page_access_token,
                    "fields": "message,id,created_time,full_picture,attachments{subattachments,media,type,title,url},insights.metric(page_fans,post_impressions,post_clicks,post_reactions_like_total,post_impressions_unique),reactions.limit(0).summary(true),comments.limit(0).summary(true)",
                },
            )
            response.raise_for_status()
            posts = response.json().get("data", [])

        if not posts:
            # logger.info(f"No posts available for Facebook account: {account.username}")
            return

        formatted_posts = []
        for post in posts:
            try:
                # Deduplicate insights by (name, period)
                insights_data = post.get("insights", {}).get("data", [])
                insights_dict = {}
                for item in insights_data:
                    name = item["name"]
                    period = item["period"]
                    value = item["values"][0]["value"]
                    insights_dict[(name, period)] = {"value": value, "period": period}
                insights = [
                    {name: data} for (name, period), data in insights_dict.items()
                ]

                # Extract reactions and comments
                likes = post.get("reactions", {}).get("summary", {}).get("total_count", 0)
                comments = post.get("comments", {}).get("summary", {}).get("total_count", 0)
                images = extract_images(post)

                post_data = {
                    "id": post.get("id"),
                    "message": post.get("message", ""),
                    "created_time": post.get("created_time"),
                    "full_picture": post.get("full_picture", ""),
                    "attachments": post.get("attachments", {}).get("data", []),
                    "insights": insights,
                    "likes": likes,
                    "comments": comments,
                    "images": images
                }
                formatted_posts.append(post_data)
            except Exception as e:
                logger.error(f"Error processing post {post.get('id')}: {str(e)}")
                continue

        # Sort by composite_score and get the top 5
        top_posts = sorted(
            formatted_posts,
            key=lambda x: composite_score(x["insights"]),
            reverse=True
        )[:5]

        # Store top posts in database
        for post in top_posts:
            created_time = None
            if post.get("created_time"):
                try:
                    created_time = datetime.strptime(post["created_time"], "%Y-%m-%dT%H:%M:%S%z")
                    created_time = created_time.replace(tzinfo=None)
                except ValueError:
                    try:
                        created_time = datetime.strptime(post["created_time"], "%Y-%m-%dT%H:%M:%S+0000")
                    except ValueError:
                        logger.error(f"Could not parse created_time: {post['created_time']}")

            # Extract metrics from insights
            def get_metric_value(insights, metric_name):
                for metric in insights:
                    if metric_name in metric:
                        value = metric[metric_name].get("value")
                        if value is not None:
                            return value
                return 0

            impressions = get_metric_value(post["insights"], "post_impressions")
            clicks = get_metric_value(post["insights"], "post_clicks")
            reach = get_metric_value(post["insights"], "post_impressions_unique")
            likes = post.get("likes", 0)
            comments = post.get("comments", 0)
            engagement_rate = 0
            if reach > 0:
                engagement_rate = ((likes + comments + clicks) / reach) * 100

            top_post = model.FacebookTopPerformingPost(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                post_id=post["id"],
                message=post.get("message"),
                created_time=created_time,
                permalink_url=f"https://www.facebook.com/{post['id']}",
                full_picture=post.get("full_picture"),
                impressions=impressions,
                reach=reach,
                likes=likes,
                comments=comments,
                clicks=clicks,
                engagement_rate=engagement_rate
            )
            top_post.set_attachments(post.get("attachments"))
            top_post.set_insights(post.get("insights"))
            top_post.set_images(post.get("images"))
            db.add(top_post)

        # logger.info(f"Updated top performing posts for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching top performing posts for {account.username}: {str(e)}")


async def fetch_and_store_facebook_metrics_for_account(account_id: str):
    """
    Fetch and store Facebook metrics for a specific account
    This is used when a new account is connected to immediately collect metrics
    Uses the new modular architecture for consistency
    """
    # logger.info(f"Starting immediate Facebook metrics update for account ID: {account_id}")

    # Initialize collectors
    metrics_collector = FacebookMetricsCollector()
    growth_collector = GrowthTrendCollector(
        metrics_collector.api_client,
        metrics_collector.error_tracker
    )

    async with SessionLocal() as db:
        try:
            # Get the specific Facebook social media account
            result = await db.execute(
                select(model.SocialMediaAccount).filter(
                    model.SocialMediaAccount.id == account_id,
                    model.SocialMediaAccount.platform == "facebook",
                    model.SocialMediaAccount.login_status.is_(True)
                )
            )
            account = result.scalars().first()

            if not account:
                logger.error(f"Facebook account with ID {account_id} not found or not active")
                return

            # Process all metrics for the account
            await process_account_metrics(account, db, metrics_collector, growth_collector)

            # Commit all changes
            await db.commit()

            # Log summary
            summary = metrics_collector.error_tracker.get_summary()
            # logger.info(f"Completed immediate Facebook metrics update for account: {account.username}")
            # logger.info(f"Operations: {summary['successful']}/{summary['total_operations']} successful")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in immediate Facebook metrics update for account ID {account_id}: {str(e)}")


async def scheduled_facebook_metrics_update():
    """
    Function to be called by the scheduler to update Facebook metrics for all accounts
    """
    await fetch_and_store_facebook_metrics()
