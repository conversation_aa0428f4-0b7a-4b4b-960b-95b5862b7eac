from typing import Annotated
from urllib.parse import unquote

import httpx
from app.core.config import settings
from app.models.model import SocialMediaAccount
from app.utils.logger import get_logger
from fastapi import Depends, HTTPException, status, Request, WebSocket
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
import hashlib
import base64
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta
from app.services.twitter_service import TwitterService
from app.services.analysis_service import AnalysisService

logger = get_logger(__name__)
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.AUTH_SERVICE_URL}/login")


async def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
        user = payload
    except JWTError:
        raise credentials_exception
    return user


async def get_current_user_from_websocket(websocket: WebSocket) -> dict:
    token = websocket.query_params.get("token")

    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(status_code=401, detail="Token missing in query params")

    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str | None = payload.get("user_id")
        if user_id is None:
            raise ValueError()
        return payload
    except JWTError:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(status_code=401, detail="Invalid token")


async def create_JWT_Response(response: dict) -> str:
    """
    Creates a JWT token for the response

    Args:
        response (dict): The payload to be encoded in the JWT

    Returns:
        str: The encoded JWT token

    Raises:
        HTTPException: If token creation fails
    """
    try:
        # logger.info("Creating JWT token")
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        encoded_jwt = jwt.encode(
            claims=response,
            key=settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )

        # ensure consistent base64 URL-safe encoding without padding
        if encoded_jwt.endswith("="):
            encoded_jwt = encoded_jwt.rstrip("=")
        # logger.info("JWT token created successfully")
        return encoded_jwt

    except JWTError as e:
        logger.error(f"JWT encoding error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create authentication token"
        )
    except ValueError as e:
        logger.error(f"Invalid input error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error during JWT creation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while creating the token"
        )


async def decode_JWT_Response(token: str):
    """Decodes a JWT token for the response"""
    try:
        # Add padding if necessary
        padding_length = len(token) % 4
        if padding_length != 0:
            token += "=" * (4 - padding_length)

        cleaned_token = unquote(token).rstrip('_')

        decoded_jwt = jwt.decode(
            cleaned_token,
            key=settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )

        # logger.info(f"decoded_jwt: {decoded_jwt}")
        return decoded_jwt
    except JWTError as e:
        logger.error(f"JWT decode error: {str(e)}")
        if "Invalid crypto padding" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token format",
            )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
        )
    except Exception as e:
        logger.error(f"Unexpected error during JWT decode: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing the token",
        )


async def get_social_details(
    organisation_id: str, platform_name: str, db_session: AsyncSession
):
    """Returns the organisation social details"""
    # logger.info(
        f"Fetching social details for organisation_id: {organisation_id}, platform_name: {platform_name}"
    )
    try:
        # Get the organisation social credentials
        # logger.info("querying the database for social media credentials")
        result = await db_session.execute(
            select(SocialMediaAccount).filter_by(
                platform=platform_name, organisation_id=organisation_id
            )
        )
        db_social = result.scalars().first()
        if not db_social:
            # logger.info("social media credentials not found")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="social media credentials not found",
            )

        # logger.info(f"social credentials found for {platform_name}")
        # Check if the user has a social_media_user_id
        if not db_social.social_media_user_id:
            # logger.info("social credentials not connected")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Social media account not yet connected",
            )

        if platform_name == "facebook":
            if not db_social.page_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="You have not connected a page",
                )

        # logger.info(
            f"Successfully fetched social details for organisation_id: {organisation_id}, platform_name: {platform_name}"
        )
        return db_social
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )


async def verify_organization(organisation_id: str) -> str:
    """
    Verify the organization by making an HTTP request to the authentication service.

    Args:
        organisation_id (str): The ID of the organization to verify.

    Returns:
        str: The verified organization ID.

    Raises:
        HTTPException: If the organization verification fails.
    """
    try:
        # logger.info("verifying the organisation id")
        async with httpx.AsyncClient(timeout=timeout) as client:
            # logger.info(f"making calls to the url: {settings.AUTH_SERVICE_URL}")
            response = await client.get(
                f"{settings.AUTH_SERVICE_URL}/verify-organisation/{organisation_id}"
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Organization verification failed.",
                )
            # logger.info("organisation verified successfully")
            return data.get("data")["id"]
    except httpx.HTTPStatusError as e:
        logger.error(f"an error occurred: {str(e)}")
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        logger.error(f"an error occurred: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the authentication service: {str(e)}",
        )


async def fetch_user_permissions(user_id: str, organisation_id: str) -> dict:
    """
    Fetch user permissions by making an HTTP request to the user service.

    Args:
        user_id (str): The ID of the user to fetch permissions for.
        organisation_id (str): The ID of the organization.

    Returns:
        dict: A dictionary containing the user's permissions.

    Raises:
        HTTPException: If fetching the user permissions fails.
    """

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.AUTH_SERVICE_URL}/user/{user_id}/permissions",
                params={"organisation_id": organisation_id},
                timeout=timeout,
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Failed to fetch user permissions.",
                )
            return data.get("data")
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500, detail=f"Error connecting to the user service: {str(e)}"
        )


async def check_permissions(user_id, organisation_id, required_permission):
    try:
        # fetch the user permissions
        user_permissions_dict = await fetch_user_permissions(user_id, organisation_id)
        user_permissions = user_permissions_dict.get("permissions", [])
        user_role = user_permissions_dict.get("role", "")
        if (user_role != "admin" and user_role != "owner") and required_permission not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"User does not have permission to access this endpoint: {required_permission}",
            )
        return user_role
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching user permissions: {e}")
        raise HTTPException(status_code=500, detail="Error fetching user permissions")


def get_challenge(verifier: str = "ellumai"):
    sha256_digest = hashlib.sha256(verifier.encode()).digest()
    code_challenge = base64.urlsafe_b64encode(sha256_digest).rstrip(b'=').decode()
    return code_challenge


async def refresh_ig_token(access_token):
    """
    Refresh the Instagram access token and update it in the database.
    """
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                "https://graph.instagram.com/refresh_access_token",
                params={
                    "grant_type": "ig_refresh_token",
                    "access_token": access_token,
                },
            )
            response.raise_for_status()
            return response.json()
    except httpx.HTTPError as e:
        raise HTTPException(status_code=400, detail=f"Failed to refresh token: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")


# Rate limiting
class RateLimiter:
    def __init__(self):
        self.requests = {}  # ip address -> list of request timestamps

    async def __call__(self, request: Request):
        ip = request.client.host
        now = datetime.now()

        # Initialize entry for this IP if it doesn't exist
        if ip not in self.requests:
            self.requests[ip] = []

        # Remove requests older than the rate limit period
        self.requests[ip] = [t for t in self.requests[ip]
                             if t > now - timedelta(minutes=settings.RATE_LIMIT_PERIOD_MINUTES)]

        # Check if rate limit exceeded
        if len(self.requests[ip]) >= settings.RATE_LIMIT_REQUESTS:
            remaining_time = (self.requests[ip][0] +
                             timedelta(minutes=settings.RATE_LIMIT_PERIOD_MINUTES) - now).total_seconds()

            if remaining_time > 0:
                logger.warning(f"Rate limit exceeded for IP {ip}")
                raise HTTPException(
                    status_code=429,
                    detail=f"Rate limit exceeded. Try again in {int(remaining_time)} seconds."
                )

        # Add current request timestamp
        self.requests[ip].append(now)

        return True

rate_limiter = RateLimiter()

def get_twitter_service():
    return TwitterService()

def get_analysis_service():
    return AnalysisService()

async def get_organization_from_token(token: Annotated[str, Depends(oauth2_scheme)]):
    """
    Extract organization ID from the JWT token
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        organization_id = payload.get("organisation_id")
        if not organization_id:
            raise HTTPException(
                status_code=401,
                detail="Invalid token: organization ID not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify the organization exists
        return await verify_organization(organization_id)
    except JWTError:
        raise HTTPException(
            status_code=401,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )