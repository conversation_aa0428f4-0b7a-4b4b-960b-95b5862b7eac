"""
Twitter engagement calculation utilities.
This module provides functions to calculate Twitter engagement metrics properly.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def calculate_twitter_engagement(
    impressions: int,
    followers_count: int,
    likes: Optional[int] = None,
    retweets: Optional[int] = None,
    replies: Optional[int] = None,
    engagements: Optional[int] = None
) -> Dict[str, Any]:
    """
    Calculate Twitter engagement metrics properly.

    Args:
        impressions: Number of impressions
        followers_count: Number of followers
        likes: Number of likes (optional)
        retweets: Number of retweets (optional)
        replies: Number of replies (optional)

    Returns:
        Dictionary containing engagement metrics:
        - total_engagement: Total engagement count
        - engagement_rate: Engagement rate as a percentage
    """
    # If we have individual metrics, use them to calculate total engagement
    if any([likes is not None, retweets is not None, replies is not None]):
        likes = likes or 0
        retweets = retweets or 0
        replies = replies or 0

        # Calculate total engagement (sum of all interactions)
        total_engagement = likes + retweets + replies
    # Otherwise, use the provided engagements value if available
    elif engagements is not None:
        total_engagement = engagements
    # If no engagement data is available, estimate based on impressions
    else:
        # Typical engagement rate is around 1-3% of impressions
        total_engagement = int(impressions * 0.02)  # 2% of impressions
        # logger.info(f"No engagement data available, estimating as 2% of impressions: {total_engagement}")

    # Calculate engagement rate
    # Standard formula: (Total Engagements / Total Impressions) * 100
    engagement_rate = 0
    if impressions > 0:
        engagement_rate = (total_engagement / impressions) * 100

    # Alternative engagement rate based on followers
    # Sometimes used when impression data is not available
    follower_engagement_rate = 0
    if followers_count > 0:
        follower_engagement_rate = (total_engagement / followers_count) * 100

    # Log the calculation for debugging
    logger.debug(
        f"Calculated Twitter engagement: total={total_engagement}, "
        f"rate={engagement_rate:.2f}%, follower_rate={follower_engagement_rate:.2f}%"
    )

    return {
        "total_engagement": total_engagement,
        "engagement_rate": round(engagement_rate, 2),
        "follower_engagement_rate": round(follower_engagement_rate, 2)
    }
