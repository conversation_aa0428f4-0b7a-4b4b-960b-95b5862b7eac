"""
Comment transformation functions for normalizing Instagram and Facebook comment data.

This module provides functions to transform platform-specific comment responses
into the unified comment format.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from app.models.model import Comment
from app.schemas.unified_comment import (
    MediaType,
    PlatformType,
    ReactionType,
    UnifiedComment,
    UnifiedCommentAttachment,
    UnifiedCommentAuthor,
    UnifiedCommentMedia,
    UnifiedCommentReaction,
    UnifiedCommentReply,
    UnifiedCommentResponse,
)

logger = logging.getLogger(__name__)


def map_media_type(media_type_str: Optional[str]) -> MediaType:
    """Map platform-specific media type strings to unified MediaType enum"""
    if not media_type_str:
        return MediaType.UNKNOWN
    
    media_type_lower = media_type_str.lower()
    
    # Instagram media types
    if media_type_lower in ["image", "photo"]:
        return MediaType.IMAGE
    elif media_type_lower in ["video", "reel"]:
        return MediaType.VIDEO
    elif media_type_lower in ["carousel_album", "carousel"]:
        return MediaType.CAROUSEL_ALBUM
    elif media_type_lower == "story":
        return MediaType.STORY
    
    # Facebook media types
    elif media_type_lower in ["photo", "picture"]:
        return MediaType.IMAGE
    elif media_type_lower in ["video", "mp4"]:
        return MediaType.VIDEO
    
    return MediaType.UNKNOWN


def map_reaction_type(reaction_str: Optional[str]) -> ReactionType:
    """Map platform-specific reaction strings to unified ReactionType enum"""
    if not reaction_str:
        return ReactionType.LIKE
    
    reaction_lower = reaction_str.lower()
    
    if reaction_lower in ["like", "thumbs_up"]:
        return ReactionType.LIKE
    elif reaction_lower in ["love", "heart"]:
        return ReactionType.LOVE
    elif reaction_lower in ["haha", "laugh"]:
        return ReactionType.HAHA
    elif reaction_lower in ["wow", "surprised"]:
        return ReactionType.WOW
    elif reaction_lower in ["sad", "cry"]:
        return ReactionType.SAD
    elif reaction_lower in ["angry", "mad"]:
        return ReactionType.ANGRY
    elif reaction_lower == "care":
        return ReactionType.CARE
    elif reaction_lower == "pride":
        return ReactionType.PRIDE
    elif reaction_lower == "thankful":
        return ReactionType.THANKFUL
    
    return ReactionType.LIKE


def transform_instagram_author(author_data: Dict[str, Any]) -> UnifiedCommentAuthor:
    """Transform Instagram author data to unified format"""
    return UnifiedCommentAuthor(
        id=author_data.get("id", ""),
        username=author_data.get("username"),
        name=author_data.get("name"),
        profile_picture_url=author_data.get("profile_picture_url")
    )


def transform_facebook_author(author_data: Dict[str, Any]) -> UnifiedCommentAuthor:
    """Transform Facebook author data to unified format"""
    return UnifiedCommentAuthor(
        id=author_data.get("id", ""),
        username=author_data.get("username"),
        name=author_data.get("name"),
        profile_picture_url=author_data.get("picture", {}).get("data", {}).get("url")
    )


def transform_instagram_media(media_data: Optional[Dict[str, Any]]) -> Optional[UnifiedCommentMedia]:
    """Transform Instagram media data to unified format"""
    if not media_data:
        return None
    
    return UnifiedCommentMedia(
        id=media_data.get("id"),
        media_type=map_media_type(media_data.get("media_type")),
        media_url=media_data.get("media_url"),
        thumbnail_url=media_data.get("thumbnail_url"),
        alt_text=media_data.get("alt_text"),
        caption=media_data.get("caption")
    )


def transform_facebook_attachment(attachment_data: Optional[Dict[str, Any]]) -> Optional[UnifiedCommentAttachment]:
    """Transform Facebook attachment data to unified format"""
    if not attachment_data:
        return None
    
    return UnifiedCommentAttachment(
        attachment_type=attachment_data.get("type"),
        url=attachment_data.get("url"),
        title=attachment_data.get("title"),
        description=attachment_data.get("description")
    )


def transform_reactions(reactions_data: List[Dict[str, Any]]) -> List[UnifiedCommentReaction]:
    """Transform reaction data to unified format"""
    reactions = []
    
    for reaction in reactions_data:
        reactions.append(UnifiedCommentReaction(
            reaction_type=map_reaction_type(reaction.get("type")),
            user_id=reaction.get("id"),
            username=reaction.get("username"),
            name=reaction.get("name")
        ))
    
    return reactions


def transform_instagram_replies(replies_data: List[Dict[str, Any]]) -> List[UnifiedCommentReply]:
    """Transform Instagram reply data to unified format"""
    replies = []
    
    for reply in replies_data:
        author = UnifiedCommentAuthor(
            id=reply.get("id", ""),
            username=reply.get("username"),
            name=reply.get("name")
        )
        
        media = transform_instagram_media(reply.get("media"))
        
        replies.append(UnifiedCommentReply(
            id=reply.get("id", ""),
            content=reply.get("text", ""),
            created_time=datetime.fromisoformat(reply.get("timestamp", "").replace("Z", "+00:00")),
            author=author,
            like_count=reply.get("like_count", 0),
            media=media,
            platform=PlatformType.INSTAGRAM
        ))
    
    return replies


def transform_facebook_replies(replies_data: List[Dict[str, Any]]) -> List[UnifiedCommentReply]:
    """Transform Facebook reply data to unified format"""
    replies = []
    
    for reply in replies_data:
        author = transform_facebook_author(reply.get("from", {}))
        
        replies.append(UnifiedCommentReply(
            id=reply.get("id", ""),
            content=reply.get("message", ""),
            created_time=datetime.fromisoformat(reply.get("created_time", "").replace("Z", "+00:00")),
            author=author,
            like_count=reply.get("like_count", 0),
            media=None,  # Facebook replies typically don't have media
            platform=PlatformType.FACEBOOK
        ))
    
    return replies


def transform_instagram_comment_to_unified(comment_data: Union[Dict[str, Any], Comment]) -> UnifiedComment:
    """Transform Instagram comment data to unified format"""
    
    # Handle both raw API data and database Comment model
    if isinstance(comment_data, Comment):
        # Database model
        raw_data = {
            "id": comment_data.comment_id,
            "text": comment_data.content,
            "timestamp": comment_data.created_time.isoformat(),
            "from": comment_data.sender,
            "like_count": comment_data.extra_data.get("like_count", 0) if comment_data.extra_data else 0,
            "replies": comment_data.extra_data.get("replies", []) if comment_data.extra_data else [],
            "media": comment_data.media,
            "parent_id": comment_data.parent_id,
            "hidden": comment_data.extra_data.get("hidden", False) if comment_data.extra_data else False
        }
    else:
        # Raw API data
        raw_data = comment_data
    
    # Transform author
    author = transform_instagram_author(raw_data.get("from", {}))
    
    # Transform media
    media = transform_instagram_media(raw_data.get("media"))
    
    # Transform replies
    replies = transform_instagram_replies(raw_data.get("replies", {}).get("data", []))
    
    # Create unified comment
    return UnifiedComment(
        id=raw_data.get("id", ""),
        content=raw_data.get("text", ""),
        created_time=datetime.fromisoformat(raw_data.get("timestamp", "").replace("Z", "+00:00")),
        author=author,
        platform=PlatformType.INSTAGRAM,
        like_count=raw_data.get("like_count", 0),
        reply_count=len(replies),
        parent_id=raw_data.get("parent_id"),
        replies=replies,
        media=media,
        attachment=None,
        reactions=[],  # Instagram doesn't have detailed reactions like Facebook
        likes=[],
        is_hidden=raw_data.get("hidden", False),
        extra_data={
            "username": raw_data.get("username"),
            "hidden": raw_data.get("hidden", False)
        },
        raw_data=raw_data if not isinstance(comment_data, Comment) else None
    )


def transform_facebook_comment_to_unified(comment_data: Union[Dict[str, Any], Comment]) -> UnifiedComment:
    """Transform Facebook comment data to unified format"""
    
    # Handle both raw API data and database Comment model
    if isinstance(comment_data, Comment):
        # Database model
        raw_data = {
            "id": comment_data.comment_id,
            "message": comment_data.content,
            "created_time": comment_data.created_time.isoformat(),
            "from": comment_data.sender,
            "like_count": comment_data.extra_data.get("like_count", 0) if comment_data.extra_data else 0,
            "comments": comment_data.extra_data.get("replies", []) if comment_data.extra_data else [],
            "attachment": comment_data.attachments,
            "parent": {"id": comment_data.parent_id} if comment_data.parent_id else None,
            "is_hidden": comment_data.extra_data.get("hidden", False) if comment_data.extra_data else False,
            "reactions": comment_data.reactions or [],
            "likes": comment_data.extra_data.get("likes", []) if comment_data.extra_data else []
        }
    else:
        # Raw API data
        raw_data = comment_data
    
    # Transform author
    author = transform_facebook_author(raw_data.get("from", {}))
    
    # Transform attachment
    attachment = transform_facebook_attachment(raw_data.get("attachment"))
    
    # Transform reactions
    reactions = transform_reactions(raw_data.get("reactions", {}).get("data", []))
    
    # Transform replies
    replies = transform_facebook_replies(raw_data.get("comments", {}).get("data", []))
    
    # Create unified comment
    return UnifiedComment(
        id=raw_data.get("id", ""),
        content=raw_data.get("message", ""),
        created_time=datetime.fromisoformat(raw_data.get("created_time", "").replace("Z", "+00:00")),
        author=author,
        platform=PlatformType.FACEBOOK,
        like_count=raw_data.get("like_count", 0),
        reply_count=len(replies),
        parent_id=raw_data.get("parent", {}).get("id") if raw_data.get("parent") else None,
        replies=replies,
        media=None,  # Facebook comments don't typically have media like Instagram
        attachment=attachment,
        reactions=reactions,
        likes=raw_data.get("likes", {}).get("data", []),
        is_hidden=raw_data.get("is_hidden", False),
        extra_data={
            "object": raw_data.get("object"),
            "hidden": raw_data.get("is_hidden", False)
        },
        raw_data=raw_data if not isinstance(comment_data, Comment) else None
    )


def transform_comments_to_unified(
    comments: List[Union[Dict[str, Any], Comment]],
    platform: PlatformType
) -> List[UnifiedComment]:
    """Transform a list of comments to unified format based on platform"""
    unified_comments = []

    for comment in comments:
        try:
            if platform == PlatformType.INSTAGRAM:
                unified_comment = transform_instagram_comment_to_unified(
                    comment
                )
            elif platform == PlatformType.FACEBOOK:
                unified_comment = transform_facebook_comment_to_unified(
                    comment
                )
            else:
                logger.warning(f"Unknown platform: {platform}")
                continue

            unified_comments.append(unified_comment)
        except Exception as e:
            logger.error(f"Error transforming comment: {e}")
            continue

    return unified_comments


def create_unified_comment_response(
    comments: List[UnifiedComment],
    total: int,
    limit: int,
    offset: int,
    platform: Optional[PlatformType] = None
) -> UnifiedCommentResponse:
    """Create a unified comment response with pagination metadata"""
    return UnifiedCommentResponse(
        comments=comments,
        total=total,
        limit=limit,
        offset=offset,
        platform=platform
    )


def detect_platform_from_comment_data(
    comment_data: Dict[str, Any]
) -> PlatformType:
    """Detect the platform based on comment data structure"""

    # Instagram-specific fields
    if "text" in comment_data and "timestamp" in comment_data:
        return PlatformType.INSTAGRAM

    # Facebook-specific fields
    if "message" in comment_data and "created_time" in comment_data:
        return PlatformType.FACEBOOK

    # Default to Instagram if unclear
    return PlatformType.INSTAGRAM


def transform_database_comment_to_unified(
    db_comment: Comment
) -> UnifiedComment:
    """Transform a database Comment model to unified format, auto-detecting platform"""

    # Try to detect platform from the data structure
    # This is a heuristic based on typical field patterns
    extra_data = db_comment.extra_data or {}

    # Instagram typically has 'timestamp' in raw data, Facebook has 'created_time'
    # Instagram uses 'text', Facebook uses 'message'
    # Check for Instagram-specific patterns
    if (extra_data.get("username") or
        (hasattr(db_comment, 'content') and db_comment.content and
         not extra_data.get("object"))):  # Facebook often has 'object' field
        return transform_instagram_comment_to_unified(db_comment)
    else:
        return transform_facebook_comment_to_unified(db_comment)


def batch_transform_comments(
    comments_data: List[Dict[str, Any]],
    platform: PlatformType,
    include_raw_data: bool = False
) -> List[UnifiedComment]:
    """
    Batch transform multiple comments with error handling and logging.

    Args:
        comments_data: List of raw comment data from API
        platform: Platform type for the comments
        include_raw_data: Whether to include raw API data in the response

    Returns:
        List of successfully transformed unified comments
    """
    unified_comments = []
    failed_count = 0

    for i, comment_data in enumerate(comments_data):
        try:
            if platform == PlatformType.INSTAGRAM:
                unified_comment = transform_instagram_comment_to_unified(
                    comment_data
                )
            elif platform == PlatformType.FACEBOOK:
                unified_comment = transform_facebook_comment_to_unified(
                    comment_data
                )
            else:
                logger.error(f"Unsupported platform: {platform}")
                failed_count += 1
                continue

            # Optionally remove raw data to reduce response size
            if not include_raw_data:
                unified_comment.raw_data = None

            unified_comments.append(unified_comment)

        except Exception as e:
            logger.error(
                f"Failed to transform comment {i} from {platform}: {e}"
            )
            failed_count += 1
            continue

    if failed_count > 0:
        logger.warning(
            f"Failed to transform {failed_count} out of "
            f"{len(comments_data)} comments"
        )

    logger.info(
        f"Successfully transformed {len(unified_comments)} comments "
        f"from {platform}"
    )
    return unified_comments
