import asyncio
import json
import logging
from datetime import datetime
from typing import Annotated, As<PERSON><PERSON><PERSON>ator, List, Optional

from app.database import get_session
from app.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, Chat<PERSON>hread, SavedContent
from app.schemas import (ChatRequest, ChatResponse, ChatThreadResponse,
                         ChatThreadSummary, DeleteThreadResponse,
                         NewChatMessage, NewChatResponse,
                         SaveSocialMediaContentRequest,
                         SocialMediaContentRequest, UpdateThreadTitleRequest,
                         UpdateThreadTitleResponse)
from app.services import chromadb_service, openai_service
from app.utils.dependency import (get_chat_history, get_current_user,
                                  save_chat_history)
from app.utils.external_calls import (fetch_user_permissions, fetch_user_roles,
                                      verify_organization)
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload

logger = logging.getLogger(__name__)
router = APIRouter()


async def get_threads(
    session: AsyncSession,
    organisation_id,
    user_id,
    last_timestamp: Optional[datetime] = None,
    batch_size: int = 100,
):
    query = (
        (
            select(ChatThread).where(
                ChatThread.user_id == user_id,
                ChatThread.organisation_id == organisation_id,
            )
        )
        .order_by(ChatThread.created_at)
        .limit(batch_size)
    )
    if last_timestamp:
        query = query.filter(ChatThread.created_at > last_timestamp)
    result = await session.execute(query)
    messages = result.scalars().all()
    return messages


async def fetch_threads_in_batches(
    session: AsyncSession, organisation_id, user_id
) -> AsyncGenerator:
    last_timestamp = None
    batch_size = 100
    while True:
        threads = await get_threads(
            session, organisation_id, user_id, last_timestamp, batch_size
        )
        if not threads:
            break
        last_timestamp = threads[-1].created_at
        # logger.info(f"Returning {len(threads)} threads for user {user_id}")
        yield json.dumps(
            [{"thread_id": thread.id, "title": thread.title} for thread in threads]
        )
        await asyncio.sleep(0.1)


# implementing streamming responses for histories and threads
async def get_messages(
    session: AsyncSession,
    organisation_id,
    user_id,
    last_timestamp: Optional[datetime] = None,
    batch_size: int = 100,
):
    query = (
        (
            select(ChatThread)
            .options(joinedload(ChatThread.chat_histories))
            .where(
                ChatThread.user_id == user_id,
                ChatThread.organisation_id == organisation_id,
            )
        )
        .order_by(ChatThread.created_at)
        .limit(batch_size)
    )
    if last_timestamp:
        query = query.filter(ChatThread.created_at > last_timestamp)
    result = await session.execute(query)
    messages = result.unique().scalars().all()
    return messages


async def fetch_messages_in_batches(
    session: AsyncSession, organisation_id, user_id
) -> AsyncGenerator:
    last_timestamp = None
    batch_size = 100
    while True:
        messages = await get_messages(
            session, organisation_id, user_id, last_timestamp, batch_size
        )
        if not messages:
            break
        last_timestamp = messages[-1].created_at
        chat_history = []
        for thread in messages:
            thread_history = {
                "thread_id": thread.id,
                "thread_title": thread.title,
                "messages": [],
            }

            for history in thread.chat_histories:
                chat_message = {
                    "user_message": history.user_message,
                    "bot_response": history.bot_response,
                    "timestamp": history.created_at.isoformat(),
                }
                thread_history["messages"].append(chat_message)

            chat_history.append(thread_history)

        # logger.info(f"Returning {len(chat_history)} chat threads for user {user_id}")
        yield json.dumps(chat_history)
        await asyncio.sleep(0.1)


async def get_messages_for_user(
    session: AsyncSession,
    organisation_id,
    user_id,
    thread_id,
    last_timestamp: Optional[datetime] = None,
    batch_size: int = 100,
):
    query = (
        (
            select(ChatHistory)
            .options(joinedload(ChatHistory.chat_thread))
            .where(
                ChatHistory.chat_thread.has(ChatThread.user_id == user_id),
                ChatHistory.organisation_id == organisation_id,
                ChatHistory.thread_id == thread_id,
            )
        )
        .order_by(ChatHistory.created_at)
        .limit(batch_size)
    )
    if last_timestamp:
        query = query.filter(ChatHistory.created_at > last_timestamp)
    result = await session.execute(query)
    messages = result.unique().scalars().all()
    return messages


async def fetch_messages_in_batches_for_user(
    thread_id, session: AsyncSession, organisation_id, user_id
) -> AsyncGenerator:
    last_timestamp = None
    batch_size = 100
    while True:
        messages = await get_messages_for_user(
            session, organisation_id, user_id, thread_id, last_timestamp, batch_size
        )
        if not messages:
            break
        last_timestamp = messages[-1].created_at
        thread = messages[0]
        thread_history = {
            "thread_id": thread.thread_id,
            "thread_title": thread.chat_thread.title,
            "messages": [],
        }

        for history in messages:
            chat_message = {
                "user_message": history.user_message,
                "bot_response": history.bot_response,
                "timestamp": history.created_at.isoformat(),
            }
            thread_history["messages"].append(chat_message)

        # logger.info(
            f"Returning chat thread {thread_id} with {len(thread_history['messages'])} messages for user {user_id}"
        )
        yield json.dumps(thread_history)
        await asyncio.sleep(0.1)


@router.post("/continue-chat", tags=["Chats"], response_model=ChatResponse)
async def chat_with_bot(
    request: ChatRequest,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")
    user_roles = await fetch_user_roles(organisation_id, user_id)
    # logger.info(f"User {user_id} is continuing chat in thread {request.thread_id}")

    try:
        # Retrieve chat history
        chat_history_records = await get_chat_history(
            request.thread_id, organisation_id, session
        )
    except Exception as e:
        logger.error(f"Failed to retrieve chat history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve chat history.")

    # Check if there's a history
    if chat_history_records:
        chat_history_prompt = "\n".join(
            [
                f"User: {chat.user_message}\nBot: {chat.bot_response}"
                for chat in chat_history_records
            ]
        )
    else:
        chat_history_prompt = "\n".join(["User: ''\nBot: ''"])

    prompt = f"Chat history:\n{chat_history_prompt}\nUser: {request.user_message}\nAssistant:"

    try:
        # Query ChromaDB
        # logger.info("Querying ChromaDB for related information")
        chromadb_result = await chromadb_service.query_chromadb(prompt, organisation_id)
        # logger.info("CheomaDB queried")
        logger.debug(f"ChromaDB result: {chromadb_result}")
    except Exception as e:
        logger.error(f"Failed to query ChromaDB: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to query ChromaDB.")

    if chromadb_result:
        # logger.info("ChromaDB result found")
        prompt += f"\n\nAdditionally, here's some related information that might help: {chromadb_result}. Ignore and do not mention it at all if the information does not relate to the question else make use of it"
    prompt += f"\n\nEnsure your response aligns with the following User roles: {', '.join(user_roles)}."
    # Analyze the prompt for image generation suitability using GPT-4
    generate_image_flag = False
    response_type = "text"
    try:
        gpt_response = await openai_service.analyse_prompt(prompt)
        if "yes $ellum" in gpt_response.lower():
            generate_image_flag = True
        logger.debug(f"GPT-4 Analysis: {gpt_response}")
        # logger.info("GPT Analysis done")
    except Exception as e:
        logger.error(
            f"Failed to analyze prompt for image generation using GPT-4: {str(e)}"
        )

    # Generate an image if the prompt is suitable
    if generate_image_flag:
        response_type = "image"
        # logger.info("Generate image true")
        try:
            bot_response = await openai_service.generate_image(prompt)
            if bot_response:
                # logger.info("Image generated successfully")
            else:
                logger.warning("Failed to generate an image")
        except Exception as e:
            logger.error(f"Failed to generate image: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to generate image.")
    else:
        try:
            # Generate bot response
            bot_response = await openai_service.query_openai(prompt)
            # logger.info(f"Bot response: {bot_response}")
        except Exception as e:
            logger.error(f"Failed to generate bot response: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Failed to generate bot response."
            )

    try:
        # Save chat history
        await save_chat_history(
            request.thread_id,
            request.user_message,
            bot_response,
            organisation_id,
            session,
        )
        # logger.info(f"Chat history saved for thread {request.thread_id}")
    except Exception as e:
        logger.error(f"Failed to save chat history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save chat history.")

    return ChatResponse(response=bot_response, response_type=response_type)


@router.post("/start-chat", tags=["Chats"], response_model=NewChatResponse)
async def start_new_chat(
    message: NewChatMessage,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")
    user_roles = await fetch_user_roles(organisation_id, user_id)
    # logger.info(f"User Roles: {user_roles}")
    # logger.info(f"User {user_id} is starting a new chat")

    try:
        # Query ChromaDB
        prompt = message.user_message

        chromadb_result = await chromadb_service.query_chromadb(prompt, organisation_id)
        logger.debug(f"ChromaDB result: {chromadb_result}")
        # logger.info("chromDB results fetched")
    except Exception as e:
        logger.error(f"Failed to query ChromaDB: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to query ChromaDB.")

    if chromadb_result:
        prompt += f"\n\nIf relevant, incorporate the following related information into your response: {chromadb_result}. However, if this information is unrelated to the question, disregard it entirely and do not reference it."
    prompt += f"\n\nEnsure your response aligns with the following User roles: {', '.join(user_roles)}."

    # Analyze the prompt for image generation suitability using GPT-4
    generate_image_flag = False
    response_type = "text"
    try:
        gpt_response = await openai_service.analyse_prompt(prompt)
        if "yes $ellum" in gpt_response.lower():
            generate_image_flag = True
        # logger.info(f"GPT-4 Analysis: {gpt_response}")
    except Exception as e:
        logger.error(
            f"Failed to analyze prompt for image generation using GPT-4: {str(e)}"
        )

    # Generate an image if the prompt is suitable
    if generate_image_flag:
        response_type = "image"
        # logger.info("Generate image true")
        try:
            bot_response = await openai_service.generate_image(prompt)
            if bot_response:
                # logger.info("Image generated successfully")
            else:
                logger.warning("Failed to generate an image")
        except Exception as e:
            logger.error(f"Failed to generate image: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to generate image.")
    else:
        try:
            # Generate bot response
            bot_response = await openai_service.query_openai(prompt)
            # logger.info(f"Bot response: {bot_response}")
        except Exception as e:
            logger.error(f"Failed to generate bot response: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Failed to generate bot response."
            )

    try:
        # Generate thread title
        conversation = f"user:{message.user_message}\nsystem:{bot_response}"
        thread_title = await openai_service.generate_thread_title(conversation)
    except Exception as e:
        logger.error(f"Failed to generate thread title: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate thread title.")

    try:
        # Create new chat thread
        new_thread = ChatThread(
            user_id=user_id,
            organisation_id=organisation_id,
            title=thread_title,
        )
        session.add(new_thread)
        await session.commit()
        # logger.info(
            f"New chat thread created with ID {new_thread.id} and title {new_thread.title}"
        )
    except Exception as e:
        logger.error(f"Failed to create new chat thread: {str(e)}")
        await session.rollback()
        raise HTTPException(status_code=500, detail="Failed to create new chat thread.")

    try:
        # Save chat history
        background_tasks.add_task(
            save_chat_history,
            new_thread.id,
            message.user_message,
            bot_response,
            organisation_id,
            session,
        )
        # logger.info(f"Chat history saved for new thread {new_thread.id}")
    except Exception as e:
        logger.error(f"Failed to save chat history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save chat history.")

    return NewChatResponse(
        bot_response=bot_response,
        thread_id=new_thread.id,
        thread_title=new_thread.title,
        response_type=response_type,
    )


@router.get("/chats_streaming", tags=["Chats"], response_model=List[ChatThreadResponse])
async def list_user_chat_history_with_streaming(
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")
    return StreamingResponse(
        fetch_messages_in_batches(session, organisation_id, user_id),
        media_type="application/json",
    )


@router.get("/chats", tags=["Chats"], response_model=List[ChatThreadResponse])
async def list_user_chat_history(
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    # logger.info(f"Listing chat history for user {token.get('user_id')}")
    user_id = token["decoded"].get("user_id")
    query = (
        select(ChatThread)
        .options(joinedload(ChatThread.chat_histories))
        .where(
            ChatThread.user_id == user_id, ChatThread.organisation_id == organisation_id
        )
    )

    # Execute the query
    result = await session.execute(query)

    # Fetch unique scalar results
    chat_threads = result.unique().scalars().all()

    chat_history = []
    for thread in chat_threads:
        thread_history = {
            "thread_id": thread.id,
            "thread_title": thread.title,
            "messages": [],
        }

        for history in thread.chat_histories:
            chat_message = {
                "user_message": history.user_message,
                "bot_response": history.bot_response,
                "timestamp": history.created_at.isoformat(),
            }
            thread_history["messages"].append(chat_message)

        chat_history.append(thread_history)

    # logger.info(f"Returning {len(chat_history)} chat threads for user {user_id}")
    return chat_history


@router.get("/user/{thread_id}", tags=["Chats"], response_model=ChatThreadResponse)
async def get_user_chat(
    thread_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    # logger.info(
        f"Listing a single chat for user {token.get('user_id')} with thread_id={thread_id}"
    )
    user_id = token["decoded"].get("user_id")
    query = (
        select(ChatThread)
        .options(joinedload(ChatThread.chat_histories))
        .where(
            ChatThread.user_id == user_id,
            ChatThread.organisation_id == organisation_id,
            ChatThread.id == thread_id,
        )
    )

    # Execute the query
    result = await session.execute(query)

    # Fetch unique scalar results
    chat_threads = result.unique().scalars().all()

    if not chat_threads:
        logger.warning(f"No chat history found for user {user_id}")
        raise HTTPException(
            status_code=404, detail="No chat history found for this user."
        )

    thread = chat_threads[0]
    thread_history = {
        "thread_id": thread.id,
        "thread_title": thread.title,
        "messages": [],
    }

    messages = thread.chat_histories
    if not messages:
        logger.warning(f"No messages found in thread {thread_id} for user {user_id}")
        raise HTTPException(status_code=404, detail="No messages found in this thread.")

    for history in messages:
        chat_message = {
            "user_message": history.user_message,
            "bot_response": history.bot_response,
            "timestamp": history.created_at.isoformat(),
        }
        thread_history["messages"].append(chat_message)

    # logger.info(
        f"Returning chat thread {thread_id} with {len(thread_history['messages'])} messages for user {user_id}"
    )
    return thread_history


@router.get("/threads", tags=["Chats"], response_model=List[ChatThreadSummary])
async def get_user_threads(
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")
    # logger.info(f"Fetching threads for user {user_id}")
    result = await session.execute(
        select(ChatThread).where(
            ChatThread.user_id == user_id, ChatThread.organisation_id == organisation_id
        )
    )
    threads = result.scalars().all()

    # logger.info(f"Returning {len(threads)} threads for user {user_id}")
    return [{"thread_id": thread.id, "title": thread.title} for thread in threads]


@router.put(
    "/threads/{thread_id}", tags=["Chats"], response_model=UpdateThreadTitleResponse
)
async def update_thread_title(
    thread_id: str,
    request: UpdateThreadTitleRequest,
    token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")
    # logger.info(
        f"User {user_id} is  updating thread {thread_id} title to {request.new_title}"
    )
    try:
        # Fetch the thread
        result = await session.execute(
            select(ChatThread).where(
                ChatThread.id == thread_id,
                ChatThread.user_id == user_id,
                ChatThread.organisation_id == organisation_id,
            )
        )
        thread = result.scalars().first()

        if not thread:
            logger.warning(f"Thread {thread_id} not found for user {user_id}")
            raise HTTPException(status_code=404, detail="Thread not found")

        # Update the title
        old_title = thread.title
        thread.title = request.new_title
        await session.commit()

        # logger.info(
            f"User {user_id} updated thread {thread_id} title from '{old_title}' to '{request.new_title}'"
        )

        return {
            "message": "Thread title updated successfully",
            "thread": {
                "thread_id": thread.id,
                "old_title": old_title,
                "new_title": request.new_title,
            },
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        await session.rollback()
        logger.error(f"Error updating thread title: {str(e)}")
        raise HTTPException(
            status_code=500, detail="An error occurred while updating the thread title."
        )


@router.delete(
    "/threads/{thread_id}", tags=["Chats"], response_model=DeleteThreadResponse
)
async def delete_thread(
    token: Annotated[str, Depends(get_current_user)],
    thread_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    session: AsyncSession = Depends(get_session),
):
    try:
        user_id = token["decoded"].get("user_id")
        result = await session.execute(
            select(ChatThread).where(
                ChatThread.id == thread_id,
                ChatThread.user_id == user_id,
                ChatThread.organisation_id == organisation_id,
            )
        )
        thread = result.scalars().first()

        if not thread:
            logger.warning(f"Thread {thread_id} not found for user {user_id}")
            raise HTTPException(status_code=404, detail="Thread not found")

        # logger.info(f"Deleting thread {thread_id} for user {user_id}")
        await session.delete(thread)
        await session.commit()

        # logger.info(f"Thread {thread_id} deleted successfully for user {user_id}")
        return {"message": "Thread deleted successfully"}

    except HTTPException as e:
        raise e
    except Exception as e:
        await session.rollback()
        logger.error(f"Error deleting thread {thread_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail="An error occurred while deleting the thread."
        )


@router.post("/generate-content", tags=["SocialMedia"])
async def generate_content(
    request: SocialMediaContentRequest,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")
    user_permissions = await fetch_user_permissions(user_id, organisation_id)

    # FIXME: Check if the user has the required permission to create and post content
    required_permission = "can create and post content"
    if required_permission not in user_permissions:
        raise HTTPException(
            status_code=403,
            detail="User does not have permission to create and post content.",
        )

    # Query ChromaDB to get all business data stored in the collection
    chromadb_result = await chromadb_service.query_entire_chromadb_collection(
        organisation_id
    )

    if not chromadb_result or not chromadb_result.get("documents"):
        return {
            "error": "No business information found in ChromaDB. Please check if the data is stored."
        }

    # Platform-specific tone/length adjustments
    platform_specs = {
        "facebook": {"tone": "friendly", "length": "longer", "format": "paragraphs"},
        "linkedin": {"tone": "professional", "length": "long", "format": "formal"},
        "whatsapp": {"tone": "casual", "length": "short", "format": "conversational"},
        "twitter": {"tone": "concise", "length": "very short", "format": "tweets"},
        "instagram": {
            "tone": "creative",
            "length": "medium",
            "format": "visual + text",
        },
    }

    platform = request.platform.lower()

    if platform not in platform_specs:
        return {
            "error": "Invalid platform. Please choose from Facebook, LinkedIn, WhatsApp, Twitter (X), or Instagram."
        }

    # Get the platform-specific settings
    platform_tone = platform_specs[platform]["tone"]
    platform_length = platform_specs[platform]["length"]
    platform_format = platform_specs[platform]["format"]

    # Build the text prompt
    text_prompt = f"""
            Write an engaging and impactful social media post tailored for {platform}.

            **Key Details:**
            1. Tone: {platform_tone} (e.g., friendly, professional, creative, or casual).
            2. Length: {platform_length} (e.g., very short, medium, or long).
            3. Audience: {chromadb_result.get('target_audience', 'general audience')}.

            ### The Context:
            - This post is for a business specializing in {chromadb_result.get('business_type', 'innovative products or services')}.
            - It aims to promote {chromadb_result.get('key_products', 'exciting offerings')} and inspire interest among {platform} users.

            ### Requirements:
            - Create a hook that grabs attention instantly (e.g., a question, bold statement, or striking fact).
            - Highlight the unique selling points (USPs) of the business, such as {chromadb_result.get('unique_features', 'quality, affordability, and innovation')}.
            - Include a clear call to action (CTA), encouraging readers to learn more, visit the website, or shop now.
            - Add relevant hashtags and tags to improve visibility.

            **Additional Info:** Incorporate this user-provided detail: general branding or marketing goals.

            Deliver the post in a {platform_format} format, optimized for {platform}, while maintaining an authentic and relatable tone."
        """

    if request.additional_info:
        text_prompt += f"Additionally, incorporate this: {request.additional_info}."

    # Generate text content asynchronously
    text_content_task = openai_service.query_openai(text_prompt)

    # Build the image prompt
    image_prompt = (
        f"Create an aesthetically pleasing and engaging visual for a {platform_tone} social media post. "
        f"The image should align with the following details:\n\n"
        f"1. Business Type: {chromadb_result.get('business_type', 'General')}\n"
        f"2. Theme: {platform_tone.capitalize()} with a focus on {request.additional_info or 'general appeal'}.\n"
        f"3. Colors: Use brand-aligned or platform-preferred colors.\n"
        f"4. Composition: Ensure the image is professional, attention-grabbing, and optimized for {platform}.\n"
        f"5. Include: Visual elements or icons that represent {chromadb_result.get('key_products', 'the brand message')}.\n\n"
        f"Design the image to resonate with {platform} users while maintaining a creative edge."
    )

    # Generate image content asynchronously
    image_content_task = openai_service.generate_image(image_prompt)

    # Await the tasks
    generated_text = await text_content_task
    generated_image = await image_content_task

    # Return the generated content
    return {
        "organisation_id": organisation_id,
        "platform": request.platform,
        "generated_text": generated_text,
        "generated_image": generated_image,
    }


@router.post("/save-content", tags=["SocialMedia"])
async def save_content(
    request: SaveSocialMediaContentRequest,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")

    user_permissions = await fetch_user_permissions(user_id, organisation_id)

    required_permission = "can schedule content"
    if required_permission not in user_permissions:
        raise HTTPException(
            status_code=403, detail="User does not have permission to save content."
        )

    # Save content for later use
    saved_content = SavedContent(
        organisation_id=organisation_id,
        user_id=user_id,
        platform=request.platform,
        content_text=request.content_text,
        additional_info=request.additional_info,
        created_at=datetime.now(),
    )

    session.add(saved_content)
    await session.commit()

    return {"message": "Content saved successfully", "content_id": saved_content.id}


@router.put("/edit-content/{content_id}", tags=["SocialMedia"])
async def edit_content(
    content_id: int,
    request: SaveSocialMediaContentRequest,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    user_id = token["decoded"].get("user_id")

    # Retrieve existing content
    existing_content = await session.get(SavedContent, content_id)
    if not existing_content:
        raise HTTPException(status_code=404, detail="Content not found")
    user_permissions = await fetch_user_permissions(user_id, organisation_id)

    required_permission = "can schedule content"
    if required_permission not in user_permissions:
        raise HTTPException(
            status_code=403, detail="User does not have permission to edit content."
        )

    if existing_content.organisation_id != organisation_id:
        raise HTTPException(status_code=403, detail="Unauthorized to edit this content")

    # Update content fields
    existing_content.platform = request.platform
    existing_content.content_text = request.content_text
    existing_content.additional_info = request.additional_info
    existing_content.updated_at = datetime.now()

    await session.commit()

    return {"message": "Content updated successfully", "content_id": content_id}
