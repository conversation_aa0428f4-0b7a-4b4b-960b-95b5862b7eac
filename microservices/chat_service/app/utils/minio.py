from io import By<PERSON><PERSON>
from minio import Minio
from fastapi import HTTPException, status
from datetime import datetime

from app.core.config import settings
from app.core.logger import get_logger

logger = get_logger(__name__)

MODEL_RUN_OUTPUT_BUCKET = settings.MINIO_BUCKET


# Initialize Minio client
minio_client = Minio(
    endpoint=settings.MINIO_ENDPOINT,
    access_key=settings.MINIO_ROOT_USER,
    secret_key=settings.MINIO_ROOT_PASSWORD,
    secure=False,
)


def upload_to_minio(output_file: BytesIO, file_metadata):
    """Upload file to MinIO."""
    try:
        # NOTE: Each organisation has it's own folder
        minio_path = (
            f"{file_metadata.get('organisation_id')}/{file_metadata.get('id')}_{file_metadata.get('filename')}"
        )
        minio_client.put_object(
            MODEL_RUN_OUTPUT_BUCKET,
            minio_path,
            output_file,
            length=output_file.getbuffer().nbytes,
        )
        # logger.info(f"File uploaded to MinIO: {minio_path}")
    except Exception as e:
        logger.error(f"Error uploading to MinIO: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload to MinIO",
        )

def get_from_minio(file_metadata):
    # logger.info("Retrieving file from storage")
    file_response = minio_client.get_object(
        MODEL_RUN_OUTPUT_BUCKET,
        f"{file_metadata.organisation_id}/{file_metadata.id}_{file_metadata.filename}",
    )
    file_content = file_response.read()
    zip_buffer = BytesIO(file_content)
    # logger.info("File retrieved successfully")
    filename = f"{datetime.fromtimestamp(file_metadata.timestamp).strftime('%A-%B-%Y-%H-%M-%S')}_{file_metadata.filename}"
    return zip_buffer.read(), filename
