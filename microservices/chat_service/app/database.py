from app.models import Base
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.core.logger import get_logger

logger = get_logger(__name__)

DATABASE_URL = (
    settings.CHAT_DATABASE_URL
    if settings.ENV == "PRODUCTION"
    else settings.LOCAL_CHAT_DATABASE_URL
)

# logger.info(f"Running in {settings.ENV} mode with db uri {DATABASE_URL}")


engine = create_async_engine(
    DATABASE_URL,
    future=True,
    echo=False,
    pool_pre_ping=True,
    pool_recycle=1800,
    pool_timeout=30,
    pool_size=10,
    max_overflow=20,
)
async_session = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)


async def create_db_and_tables():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


# Dependency for getting a session
async def get_session() -> AsyncSession:
    async with async_session() as session:
        yield session
