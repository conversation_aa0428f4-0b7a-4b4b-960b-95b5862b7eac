import ast
import os

import httpx
from app.core.logger import get_logger
from dotenv import load_dotenv
from fastapi import HTTPException
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from openai import OpenAI

logger = get_logger(__name__)

timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)

load_dotenv()


async def query_openai(prompt: str) -> str:
    """
    Query the OpenAI API with a given prompt and return the response.
    """
    # logger.info("Starting query_openai function")
    logger.debug(f"Prompt: {prompt}")
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            # logger.info("Sending request to OpenAI API")
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={"Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"},
                json={
                    "model": "gpt-4o-mini",
                    "messages": [
                        {
                            "role": "system",
                            "content": """
                            You are an intelligent assistant that uses a combination of a predefined knowledge base and real-time
                            search to provide accurate, concise, and context-aware responses without hallucinations,
                            while remembering past conversations and ensuring coherence in tone and style.
                            You are a highly concise assistant. Respond to the query with only the necessary information,
                            avoiding filler phrases like "Sure" or "If you need more help.
                            """,
                        },
                        {"role": "user", "content": prompt},
                    ],
                },
                timeout=60,
            )
            # logger.info(f"Response status: {response.status_code}")
            response.raise_for_status()
            logger.debug(f"Response data: {response.json()}")
            response_data = response.json()
        except httpx.HTTPStatusError as e:
            # Handle specific HTTP errors
            logger.error(
                f"HTTP error occurred: {e.response.status_code}\n{e.response.text}"
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"HTTP error occurred: {e.response.status_code}",
            )
        # TODO: Catch errors well, and chating takes time, so would have to revert to what it was back
        except httpx.RequestError as exc:
            # Handle request errors
            logger.error(f"Request error: {str(exc)}")
            raise RuntimeError("An error occurred while requesting")
        final_response = response_data["choices"][0]["message"]["content"].strip()
        # logger.info("Returning final response from query_openai")
        logger.debug(f"Final response: {final_response}")
        return final_response


async def generate_combined_response(user_query: str) -> str:
    """Generate a combined response using ChromaDB results and OpenAI API.

    Args:
        user_query (str): The user's query to process.

    Returns:
        str: The combined response from ChromaDB and OpenAI.
    """
    from app.services.chromadb_service import query_chromadb

    chromadb_result = await query_chromadb(user_query)

    if chromadb_result:
        openai_prompt = (
            f"{user_query}\n\nAdditionally, here's some related information that might help: {chromadb_result}. "
            "Ignore and do not mention it at all if the information does not relate to the question else make use of it"
        )
    else:
        openai_prompt = user_query

    return await query_openai(openai_prompt)


async def generate_summary(text):
    """
    Generate a summary of the given text.
    This is a placeholder function. You can replace it with a more sophisticated summarization algorithm.
    """
    try:
        # logger.info("generating summary")
        prompt = f"You are an assistant specializing in summarizing text into concise summaries.Summarize the following text in about 13 words: {text[:100000]}"
        # logger.info("making the call to query_openai")
        # # logger.info(f'The text to summarize: {text}')
        summary = await query_openai(prompt)
        # logger.info(f"generated summary: {summary}")
        return summary
    except HTTPException as e:
        logger.error(f"Error generating summary: {str(e)}")
        summary = "can not generate summary"
        return summary
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        summary = "can not generate summary"
        return summary


async def generate_thread_title(prompt: str) -> str:
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={"Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"},
                json={
                    "model": "gpt-4o",
                    "messages": [
                        {"role": "system", "content": "You are a good title suggester"},
                        {
                            "role": "user",
                            "content": f"Describe the conversation below in seven words or less:{prompt}",
                        },
                    ],
                },
            )
            response.raise_for_status()
            response_data = response.json()
        except httpx.HTTPStatusError as e:
            raise RuntimeError(f"HTTP error occurred: {e.response.status_code}")
        except httpx.RequestError as e:
            raise RuntimeError(f"An error occurred while requesting: {e}")
        return response_data["choices"][0]["message"]["content"].strip()


def find_similar_competitors(business_description: str, competitor_names: list[str]):
    # Validate inputs
    if not business_description or not isinstance(competitor_names, list):
        raise ValueError(
            "Invalid input: business_description must be a string and competitor_names must be a list."
        )

    system_prompt = f"""Please generate a list response (strictly list) listing competitor website links for
      a business that fits the following description: {business_description}.
      The known competitors are {', '.join(competitor_names)}.No additional text or explanations should be included.
      For example ['https://apple.com','https://ethereum.org', 'https://ripple.com']
      """

    model = ChatOpenAI(model="gpt-4o")
    parser = StrOutputParser()
    prompt_template = ChatPromptTemplate.from_messages([("system", system_prompt)])

    chain = prompt_template | model | parser

    try:
        response = chain.invoke({"description": business_description})
        # logger.info(f"Model response: {response}")
        competitor_list = ast.literal_eval(response)

        # Ensure the response is actually a list
        if isinstance(competitor_list, list):
            return competitor_list
        else:
            raise ValueError("Response is not a valid list")
    except ValueError as ve:
        logger.error(f"ValueError in find_similar_competitors: {str(ve)}")
        raise HTTPException(
            status_code=400, detail="Invalid response format from the model."
        )
    except SyntaxError as se:
        logger.error(f"SyntaxError in find_similar_competitors: {str(se)}")
        raise HTTPException(
            status_code=400, detail="Error parsing the model's response."
        )
    except HTTPException as he:
        logger.error(f"HTTPException in find_similar_competitors: {str(he)}")
        raise he
    except Exception as e:
        logger.error(f"Unexpected error in find_similar_competitors: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while finding similar competitors.",
        )


async def generate_image(prompt: str):
    """
    Generates an image using the OpenAI DALL-E API

    """
    try:
        # Initialize the OpenAI client
        client = OpenAI()

        # Generate the image
        response = client.images.generate(
            model="dall-e-3", prompt=prompt, size="1024x1024", quality="standard", n=1
        )

        image_url = response.data[0].url
        # logger.info(f"Generated Image URL: {image_url}")

        return image_url
    except Exception as e:
        print(f"Error generating or downloading the image: {e}")
        return None


async def analyse_prompt(prompt: str) -> str:
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={"Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"},
                json={
                    "model": "gpt-4o",
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an assistant designed to evaluate user prompts to determine if they describe or request something that should be visualized as an image.",
                        },
                        {
                            "role": "user",
                            "content": f"Prompt: {prompt}. Should this be visualized as an image? Answer 'yes $ellum' or 'no $ellum'.",
                        },
                    ],
                },
            )
            response.raise_for_status()
            response_data = response.json()
        except httpx.HTTPStatusError as e:
            raise RuntimeError(f"HTTP error occurred: {e.response.status_code}")
        except httpx.RequestError as e:
            raise RuntimeError(f"An error occurred while requesting: {e}")
        return response_data["choices"][0]["message"]["content"]
