"""
Event client for other services to publish events to the event bus.
This provides a simple HTTP API for services that don't want to integrate Redis directly.
"""

import logging
from typing import Any, Dict, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from .bus import get_event_bus
from .models import EventType
from .monitoring import get_event_monitor
from .persistence import get_event_store

logger = logging.getLogger(__name__)

router = APIRouter()


class EventPublishRequest(BaseModel):
    """Request model for publishing events."""
    event_type: str
    data: Dict[str, Any]
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    organization_id: Optional[str] = None


class EventPublishResponse(BaseModel):
    """Response model for event publishing."""
    event_id: str
    status: str = "published"


@router.post("/publish", response_model=EventPublishResponse)
async def publish_event(request: EventPublishRequest):
    """
    Publish an event to the event bus.
    This endpoint allows other services to publish events via HTTP.
    """
    try:
        # Validate event type
        try:
            event_type = EventType(request.event_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid event type: {request.event_type}"
            )
        
        # Get event bus
        event_bus = get_event_bus()
        
        # Publish event
        event_id = await event_bus.publish(
            event_type=event_type,
            data=request.data,
            correlation_id=request.correlation_id,
            user_id=request.user_id,
            organization_id=request.organization_id,
        )
        
        # logger.info(f"Published event {event_id} of type {event_type} via HTTP API")
        
        return EventPublishResponse(event_id=event_id)
        
    except Exception as e:
        logger.error(f"Failed to publish event via HTTP API: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to publish event: {str(e)}"
        )


@router.get("/event-types")
async def get_event_types():
    """Get all available event types."""
    return {
        "event_types": [event_type.value for event_type in EventType]
    }


@router.get("/health")
async def event_system_health():
    """Check the health of the event system."""
    try:
        event_monitor = get_event_monitor()
        health_status = await event_monitor.health_check()

        if health_status["overall_status"] == "unhealthy":
            raise HTTPException(
                status_code=503,
                detail=health_status
            )

        return health_status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Event system health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Event system unhealthy: {str(e)}"
        )


@router.get("/metrics")
async def get_event_metrics():
    """Get event processing metrics."""
    try:
        event_monitor = get_event_monitor()
        return event_monitor.get_metrics()
    except Exception as e:
        logger.error(f"Error getting event metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get metrics: {str(e)}"
        )


@router.get("/recent-events")
async def get_recent_events(limit: int = 50):
    """Get recent events."""
    try:
        event_monitor = get_event_monitor()
        return {
            "events": event_monitor.get_recent_events(limit),
            "total_count": len(event_monitor.recent_events)
        }
    except Exception as e:
        logger.error(f"Error getting recent events: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recent events: {str(e)}"
        )


@router.get("/statistics")
async def get_event_statistics(
    hours: int = 24
):
    """Get event statistics for a time period."""
    try:
        from datetime import datetime, timedelta

        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)

        event_store = get_event_store()
        stats = await event_store.get_event_statistics(start_time, end_time)

        return {
            "time_range": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "hours": hours
            },
            "statistics": stats
        }
    except Exception as e:
        logger.error(f"Error getting event statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get statistics: {str(e)}"
        )


@router.get("/report")
async def generate_event_report(
    hours: int = 24
):
    """Generate a comprehensive event report."""
    try:
        from datetime import datetime, timedelta

        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)

        event_monitor = get_event_monitor()
        report = await event_monitor.generate_report(start_time, end_time)

        return report
    except Exception as e:
        logger.error(f"Error generating event report: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate report: {str(e)}"
        )


@router.post("/reset-metrics")
async def reset_event_metrics():
    """Reset event metrics counters."""
    try:
        event_monitor = get_event_monitor()
        event_monitor.reset_metrics()
        return {"status": "success", "message": "Event metrics reset"}
    except Exception as e:
        logger.error(f"Error resetting event metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset metrics: {str(e)}"
        )


# Utility functions for common event publishing

async def publish_user_registered_event(
    user_id: str,
    email: str,
    organization_id: str,
    username: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Utility function to publish user registration events."""
    event_bus = get_event_bus()
    
    data = {
        "user_id": user_id,
        "email": email,
        "organization_id": organization_id,
        "username": username,
        "first_name": first_name,
        "last_name": last_name,
    }
    
    return await event_bus.publish(
        event_type=EventType.AUTH_USER_REGISTERED,
        data=data,
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    )


async def publish_user_login_event(
    user_id: str,
    organization_id: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    login_method: str = "password",
    correlation_id: Optional[str] = None,
) -> str:
    """Utility function to publish user login events."""
    event_bus = get_event_bus()
    
    data = {
        "user_id": user_id,
        "organization_id": organization_id,
        "ip_address": ip_address,
        "user_agent": user_agent,
        "login_method": login_method,
    }
    
    return await event_bus.publish(
        event_type=EventType.AUTH_USER_LOGIN,
        data=data,
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    )


async def publish_file_uploaded_event(
    file_id: str,
    user_id: str,
    organization_id: str,
    filename: str,
    file_size: int,
    file_type: str,
    upload_path: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Utility function to publish file upload events."""
    event_bus = get_event_bus()
    
    data = {
        "file_id": file_id,
        "user_id": user_id,
        "organization_id": organization_id,
        "filename": filename,
        "file_size": file_size,
        "file_type": file_type,
        "upload_path": upload_path,
    }
    
    return await event_bus.publish(
        event_type=EventType.FASTBOT_FILE_UPLOADED,
        data=data,
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    )


async def publish_chat_message_event(
    message_id: str,
    user_id: str,
    organization_id: str,
    conversation_id: str,
    message_content: str,
    message_type: str = "text",
    ai_response: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Utility function to publish chat message events."""
    event_bus = get_event_bus()
    
    data = {
        "message_id": message_id,
        "user_id": user_id,
        "organization_id": organization_id,
        "conversation_id": conversation_id,
        "message_content": message_content,
        "message_type": message_type,
        "ai_response": ai_response,
    }
    
    return await event_bus.publish(
        event_type=EventType.FASTBOT_CHAT_MESSAGE_SENT,
        data=data,
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    )


async def publish_task_completed_event(
    task_id: str,
    task_name: str,
    user_id: str,
    organization_id: str,
    result: Optional[Dict[str, Any]] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Utility function to publish task completion events."""
    event_bus = get_event_bus()
    
    data = {
        "task_id": task_id,
        "task_name": task_name,
        "user_id": user_id,
        "organization_id": organization_id,
        "result": result,
    }
    
    return await event_bus.publish(
        event_type=EventType.FASTBOT_TASK_COMPLETED,
        data=data,
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    )


async def publish_task_failed_event(
    task_id: str,
    task_name: str,
    user_id: str,
    organization_id: str,
    error: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Utility function to publish task failure events."""
    event_bus = get_event_bus()
    
    data = {
        "task_id": task_id,
        "task_name": task_name,
        "user_id": user_id,
        "organization_id": organization_id,
        "error": error,
    }
    
    return await event_bus.publish(
        event_type=EventType.FASTBOT_TASK_FAILED,
        data=data,
        correlation_id=correlation_id,
        user_id=user_id,
        organization_id=organization_id,
    )
