"""
Event handlers for Settings and Payment service.
"""

import asyncio
import logging
from functools import wraps
from typing import Callable, Dict, List

from .models import Event, EventType
from .exceptions import EventError

from app.database.session import get_db
from app.models.model import NotificationSettings
from app.services.redis_service import redis_service

logger = logging.getLogger(__name__)


class EventHandler:
    """Event handler registry and management."""

    def __init__(self):
        self._handlers: Dict[EventType, List[Callable]] = {}
        self._middleware: List[Callable] = []

    def register(self, event_type: EventType, handler: Callable):
        """Register an event handler for a specific event type."""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)
        # logger.info(f"Registered handler {handler.__name__} for event type {event_type}")

    def add_middleware(self, middleware: Callable):
        """Add middleware that runs before all event handlers."""
        self._middleware.append(middleware)
        # logger.info(f"Added middleware {middleware.__name__}")

    async def handle_event(self, event: Event) -> None:
        """Handle an event by calling all registered handlers."""
        try:
            # Run middleware first
            for middleware in self._middleware:
                if asyncio.iscoroutinefunction(middleware):
                    await middleware(event)
                else:
                    middleware(event)

            # Get handlers for this event type
            handlers = self._handlers.get(event.type, [])

            if not handlers:
                logger.warning(f"No handlers registered for event type {event.type}")
                return

            # Execute all handlers concurrently
            tasks = []
            for handler in handlers:
                if asyncio.iscoroutinefunction(handler):
                    tasks.append(handler(event))
                else:
                    # Wrap sync functions in async
                    tasks.append(asyncio.create_task(asyncio.to_thread(handler, event)))

            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Log any exceptions
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        handler_name = handlers[i].__name__
                        logger.error(f"Handler {handler_name} failed for event {event.id}: {result}")

        except Exception as e:
            logger.error(f"Error handling event {event.id}: {e}")
            raise EventError(f"Failed to handle event {event.id}: {e}")

    def get_handlers(self, event_type: EventType) -> List[Callable]:
        """Get all handlers for a specific event type."""
        return self._handlers.get(event_type, [])


# Global event handler instance
_event_handler = EventHandler()


def event_handler(event_type: EventType):
    """Decorator to register a function as an event handler."""
    def decorator(func: Callable):
        _event_handler.register(event_type, func)

        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)

        return wrapper
    return decorator


def middleware(func: Callable):
    """Decorator to register a function as event middleware."""
    _event_handler.add_middleware(func)

    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)

    return wrapper


async def handle_event(event: Event) -> None:
    """Handle an event using the global event handler."""
    await _event_handler.handle_event(event)


def get_event_handler() -> EventHandler:
    """Get the global event handler instance."""
    return _event_handler


# Common middleware functions

@middleware
async def logging_middleware(event: Event):
    """Log all events for debugging and monitoring."""
    # logger.info(f"Processing event {event.id} of type {event.type} from {event.metadata.source_service}")


@middleware
async def metrics_middleware(event: Event):
    """Collect metrics for event processing."""
    # This would integrate with your metrics system
    # For now, just log the metric
    logger.debug(f"Event metric: {event.type} processed")


@event_handler(EventType.SOCIALS_CONTENT_SCHEDULED)
async def handle_socials_content_scheduled(event: Event) -> None:
    """Handle content scheduled event by notifying all reviewers."""
    try:
        data = event.data
        content_id = data.get("content_id")
        organization_id = data.get("organization_id")
        scheduled_by = data.get("scheduled_by")
        content = data.get("content")
        platform = data.get("platform")
        scheduled_time = data.get("scheduled_time")
        reviewers = data.get("reviewers", [])

        if not content_id or not organization_id or not scheduled_by or not content or not platform or not scheduled_time or not reviewers:
            logger.warning(f"Missing required data in socials content scheduled event {event.id}")
            return

        title = f"Content Scheduled for Review on {platform}"
        message = (
            f"New content has been scheduled for {platform} at {scheduled_time}.\n"
            f"Content: {content[:10]}...\n"
            f"Please review and approve."
        )

        db = next(get_db())
        try:
            from app.models.model import Notification
            from .publishers import publish_notification_event

            for reviewer in reviewers:
                # reviewer can be user_id or email
                user_id = reviewer if isinstance(reviewer, str) and "@" not in reviewer else None
                email = reviewer if isinstance(reviewer, str) and "@" in reviewer else None

                # Create notification record in database if user_id is available
                notification_id = None
                if user_id:
                    new_notification = Notification(
                        user_id=user_id,
                        organization_id=organization_id,
                        message=message,
                        is_read=False,
                    )
                    db.add(new_notification)
                    db.commit()
                    db.refresh(new_notification)
                    notification_id = str(new_notification.id)
                    # logger.info(f"Created scheduled content notification for reviewer {user_id}")

                    # Send real-time notification if enabled
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking (email or in_app)
                await publish_notification_event(
                    notification_id=notification_id or f"scheduled_{content_id}_{reviewer}",
                    user_id=user_id,
                    organization_id=organization_id,
                    title=title,
                    message=message,
                    notification_type="content_review",
                    channel="email" if email else "in_app",
                    correlation_id=event.metadata.correlation_id,
                )
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling socials content scheduled event {event.id}: {e}")


@event_handler(EventType.SOCIALS_CONTENT_UPDATED)
async def handle_socials_content_updated(event: Event) -> None:
    """Handle content updated event by notifying all reviewers."""
    try:
        data = event.data
        content_id = data.get("content_id")
        organization_id = data.get("organization_id")
        updated_by = data.get("updated_by")
        content = data.get("content")
        platform = data.get("platform")
        scheduled_time = data.get("scheduled_time")
        reviewers = data.get("reviewers", [])

        if not content_id or not organization_id or not updated_by or not content or not platform or not scheduled_time or not reviewers:
            logger.warning(f"Missing required data in socials content updated event {event.id}")
            return

        title = f"Content Updated for Review on {platform}"
        message = (
            f"Content scheduled for {platform} at {scheduled_time} has been updated.\n"
            f"Content: {content[:10]}...\n"
            f"Please review the latest version."
        )

        db = next(get_db())
        try:
            from app.models.model import Notification
            from .publishers import publish_notification_event

            for reviewer in reviewers:
                user_id = reviewer if isinstance(reviewer, str) and "@" not in reviewer else None
                email = reviewer if isinstance(reviewer, str) and "@" in reviewer else None

                notification_id = None
                if user_id:
                    new_notification = Notification(
                        user_id=user_id,
                        organization_id=organization_id,
                        message=message,
                        is_read=False,
                    )
                    db.add(new_notification)
                    db.commit()
                    db.refresh(new_notification)
                    notification_id = str(new_notification.id)
                    # logger.info(f"Created updated content notification for reviewer {user_id}")

                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                await publish_notification_event(
                    notification_id=notification_id or f"updated_{content_id}_{reviewer}",
                    user_id=user_id,
                    organization_id=organization_id,
                    title=title,
                    message=message,
                    notification_type="content_review_update",
                    channel="email" if email else "in_app",
                    correlation_id=event.metadata.correlation_id,
                )
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling socials content updated event {event.id}: {e}")


@event_handler(EventType.AUTH_USER_INVITED)
async def handle_user_invited(event: Event) -> None:
    """Handle user invitation events by creating a notification for the invitee."""
    try:
        data = event.data
        invitee = data.get("invitee", {})
        organisation_id = data.get("organisation_id")
        organisation_name = data.get("organisation_name")
        inviter_name = data.get("inviter_name")
        role_name = data.get("role_name")
        email = invitee.get("email")
        first_name = invitee.get("first_name")
        last_name = invitee.get("last_name")
        user_id = invitee.get("user_id")

        if not email or not organisation_id or not organisation_name or not inviter_name or not role_name:
            logger.warning(f"Missing required data in user invited event {event.id}")
            return

        # Compose notification message
        message = (
            f"You have been invited to join '{organisation_name}' as '{role_name}' by {inviter_name}."
        )
        title = f"Invitation to join {organisation_name}"

        # Create notification record in database (if user_id is available)
        db = next(get_db())
        try:
            from app.models.model import Notification
            new_notification = Notification(
                user_id=user_id,
                organization_id=organisation_id,
                message=message,
                is_read=False,
            )
            db.add(new_notification)
            db.commit()
            db.refresh(new_notification)
            # logger.info(f"Created invitation notification for invitee {email} to org {organisation_name}")

            # Send real-time notification if enabled (if user_id is available)
            from app.services.redis_service import redis_service
            if user_id:
                await redis_service.publish(
                    f"user:{user_id}:notifications:{organisation_id}",
                    message
                )

            # Publish notification event for tracking (email channel)
            from .publishers import publish_notification_event
            await publish_notification_event(
                notification_id=str(new_notification.id),
                user_id=user_id,
                organization_id=organisation_id,
                title=title,
                message=message,
                notification_type="invitation",
                channel="email",
                correlation_id=event.metadata.correlation_id,
            )
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling user invited event {event.id}: {e}")


@event_handler(EventType.AUTH_USER_REGISTERED)
async def handle_user_registered(event: Event) -> None:
    """Handle user joining an organisation events by creating default notification settings."""
    try:
        user_data = event.data
        user_id = user_data.get("user_id")
        organization_id = user_data.get("organization_id")
        email = user_data.get("email")
        
        if not user_id or not organization_id:
            logger.warning(f"Missing required data in user registration event {event.id}")
            return
        
        # Create default notification settings for the new user
        db = next(get_db())
        try:
            # Check if settings already exist
            existing_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )
            
            if not existing_settings:
                # Create default notification settings
                default_settings = NotificationSettings(
                    user_id=user_id,
                    organization_id=organization_id,
                    comments=True,
                    new_message_alerts=True,
                    account_activity=True,
                    system_updates=True,
                    marketing_updates=False,
                    in_app_notifications=True,
                    email_notifications=True,
                    push_notifications=True,
                    notification_frequency="Real-time",
                )
                
                db.add(default_settings)
                db.commit()
                
                # logger.info(f"Created default notification settings for user {user_id} in org {organization_id}")
                
                # Send welcome notification
                if email:
                    welcome_message = f"Welcome to EllumAI! Your account has been successfully created."
                    
                    # Publish welcome notification event
                    from .publishers import publish_notification_event
                    await publish_notification_event(
                        notification_id=f"welcome_{user_id}",
                        user_id=user_id,
                        organization_id=organization_id,
                        title="Welcome to EllumAI",
                        message=welcome_message,
                        notification_type="welcome",
                        channel="email",
                        correlation_id=event.metadata.correlation_id,
                    )
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error handling user registration event {event.id}: {e}")


@event_handler(EventType.AUTH_USER_LOGIN)
async def handle_user_login(event: Event) -> None:
    """Handle user login events for security notifications."""
    try:
        login_data = event.data
        user_id = login_data.get("user_id")
        organization_id = login_data.get("organization_id")
        ip_address = login_data.get("ip_address")
        user_agent = login_data.get("user_agent")
        
        if not user_id or not organization_id:
            logger.warning(f"Missing required data in user login event {event.id}")
            return
        
        # Check if this is a suspicious login (different IP, etc.)
        # For now, just log all logins
        # logger.info(f"User {user_id} logged in from IP {ip_address}")
        
        # Could implement suspicious login detection here
        # and send security alerts if needed
        
    except Exception as e:
        logger.error(f"Error handling user login event {event.id}: {e}")


@event_handler(EventType.FASTBOT_FILE_UPLOADED)
async def handle_file_uploaded(event: Event) -> None:
    """Handle file upload events to send notifications."""
    try:
        file_data = event.data
        user_id = file_data.get("user_id")
        organization_id = file_data.get("organization_id")
        filename = file_data.get("filename")
        file_size = file_data.get("file_size")
        
        if not user_id or not organization_id:
            logger.warning(f"Missing required data in file upload event {event.id}")
            return
        
        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )
            
            if user_settings and user_settings.account_activity:
                # Send file upload notification
                message = f"Your file '{filename}' ({file_size} bytes) has been successfully uploaded and is being processed."

                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created file upload notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="File Upload Complete",
                    message=message,
                    notification_type="file_upload",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )
        
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error handling file upload event {event.id}: {e}")


@event_handler(EventType.FASTBOT_CHAT_MESSAGE_SENT)
async def handle_chat_message(event: Event) -> None:
    """Handle chat message events for notification purposes."""
    try:
        message_data = event.data
        user_id = message_data.get("user_id")
        organization_id = message_data.get("organization_id")
        conversation_id = message_data.get("conversation_id")
        ai_response = message_data.get("ai_response")
        
        if not user_id or not organization_id:
            logger.warning(f"Missing required data in chat message event {event.id}")
            return
        
        # Check user's notification preferences for new message alerts
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )
            
            if user_settings and user_settings.new_message_alerts and ai_response:
                # Send notification about AI response
                message = f"You have a new AI response in conversation {conversation_id}"
                
                # Publish to real-time notification channel
                await redis_service.publish(
                    f"user:{user_id}:notifications:{organization_id}",
                    message
                )
                
                # logger.info(f"Sent real-time notification for chat message to user {user_id}")
        
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error handling chat message event {event.id}: {e}")


@event_handler(EventType.FASTBOT_TASK_COMPLETED)
async def handle_task_completed(event: Event) -> None:
    """Handle task completion events."""
    try:
        task_data = event.data
        user_id = task_data.get("user_id")
        organization_id = task_data.get("organization_id")
        task_name = task_data.get("task_name")
        
        if not user_id or not organization_id:
            logger.warning(f"Missing required data in task completion event {event.id}")
            return
        
        # Send task completion notification
        message = f"Your task '{task_name}' has been completed successfully."

        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )

            if user_settings and user_settings.account_activity:
                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created task completion notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="Task Completed",
                    message=message,
                    notification_type="task_completion",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )

        finally:
            db.close()
        
    except Exception as e:
        logger.error(f"Error handling task completion event {event.id}: {e}")


@event_handler(EventType.FASTBOT_TASK_FAILED)
async def handle_task_failed(event: Event) -> None:
    """Handle task failure events."""
    try:
        task_data = event.data
        user_id = task_data.get("user_id")
        organization_id = task_data.get("organization_id")
        task_name = task_data.get("task_name")
        error = task_data.get("error")

        if not user_id or not organization_id:
            logger.warning(f"Missing required data in task failure event {event.id}")
            return

        # Send task failure notification
        message = f"Your task '{task_name}' has failed. Error: {error}"

        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )

            if user_settings and user_settings.account_activity:
                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created task failure notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="Task Failed",
                    message=message,
                    notification_type="task_failure",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling task failure event {event.id}: {e}")


@event_handler(EventType.SOCIALS_POST_PUBLISHED)
async def handle_social_post_published(event: Event) -> None:
    """Handle social media post published events."""
    try:
        post_data = event.data
        user_id = post_data.get("user_id")
        organization_id = post_data.get("organization_id")
        platform = post_data.get("platform")
        content = post_data.get("content", "")

        if not user_id or not organization_id:
            logger.warning(f"Missing required data in social post published event {event.id}")
            return

        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )

            if user_settings and user_settings.account_activity:
                # Send post published notification
                content_preview = content[:50] + "..." if len(content) > 50 else content
                message = f"Your post on {platform} has been published: '{content_preview}'"

                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created social post notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="Post Published",
                    message=message,
                    notification_type="social_media",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling social post published event {event.id}: {e}")


@event_handler(EventType.SOCIALS_PLATFORM_CONNECTED)
async def handle_platform_connected(event: Event) -> None:
    """Handle social media platform connection events."""
    try:
        platform_data = event.data
        user_id = platform_data.get("user_id")
        organization_id = platform_data.get("organization_id")
        platform = platform_data.get("platform")

        if not user_id or not organization_id:
            logger.warning(f"Missing required data in platform connected event {event.id}")
            return

        # Send platform connection notification
        message = f"Successfully connected your {platform} account!"

        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )

            if user_settings and user_settings.account_activity:
                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created platform connection notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="Platform Connected",
                    message=message,
                    notification_type="social_media",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling platform connected event {event.id}: {e}")


@event_handler(EventType.SOCIALS_PLATFORM_DISCONNECTED)
async def handle_platform_disconnected(event: Event) -> None:
    """Handle social media platform disconnection events."""
    try:
        platform_data = event.data
        user_id = platform_data.get("user_id")
        organization_id = platform_data.get("organization_id")
        platform = platform_data.get("platform")
        reason = platform_data.get("reason", "")

        if not user_id or not organization_id:
            logger.warning(f"Missing required data in platform disconnected event {event.id}")
            return

        # Send platform disconnection notification
        message = f"Your {platform} account has been disconnected"
        if reason:
            message += f": {reason}"

        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )

            if user_settings and user_settings.account_activity:
                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created platform disconnection notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="Platform Disconnected",
                    message=message,
                    notification_type="social_media",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling platform disconnected event {event.id}: {e}")


@event_handler(EventType.FASTBOT_KNOWLEDGE_BASE_UPDATED)
async def handle_knowledge_base_updated(event: Event) -> None:
    """Handle knowledge base update events."""
    try:
        kb_data = event.data
        user_id = kb_data.get("user_id")
        organization_id = kb_data.get("organization_id")
        update_type = kb_data.get("update_type")
        affected_documents = kb_data.get("affected_documents", [])

        if not user_id or not organization_id:
            logger.warning(f"Missing required data in knowledge base updated event {event.id}")
            return

        # Check user's notification preferences
        db = next(get_db())
        try:
            user_settings = (
                db.query(NotificationSettings)
                .filter(
                    NotificationSettings.user_id == user_id,
                    NotificationSettings.organization_id == organization_id,
                )
                .first()
            )

            if user_settings and user_settings.system_updates:
                # Send knowledge base update notification
                doc_count = len(affected_documents)
                message = f"Knowledge base updated: {update_type}"
                if doc_count > 0:
                    message += f" ({doc_count} document{'s' if doc_count != 1 else ''} affected)"

                # Create notification record in database
                from app.models.model import Notification
                new_notification = Notification(
                    user_id=user_id,
                    organization_id=organization_id,
                    message=message,
                    is_read=False,
                )

                db.add(new_notification)
                db.commit()
                db.refresh(new_notification)

                # logger.info(f"Created knowledge base update notification for user {user_id}: {message}")

                # Send real-time notification if enabled
                if user_settings.in_app_notifications:
                    from app.services.redis_service import redis_service
                    await redis_service.publish(
                        f"user:{user_id}:notifications:{organization_id}",
                        message
                    )

                # Publish notification event for tracking
                from .publishers import publish_notification_event
                await publish_notification_event(
                    notification_id=str(new_notification.id),
                    user_id=user_id,
                    organization_id=organization_id,
                    title="Knowledge Base Updated",
                    message=message,
                    notification_type="system_update",
                    channel="in_app",
                    correlation_id=event.metadata.correlation_id,
                )

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error handling knowledge base updated event {event.id}: {e}")
