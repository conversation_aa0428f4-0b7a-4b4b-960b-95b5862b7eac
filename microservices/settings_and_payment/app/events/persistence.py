"""
Event persistence and replay functionality.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from .bus import get_event_bus
from .models import Event, EventType

logger = logging.getLogger(__name__)


class EventStore:
    """Event store for persistence and replay functionality."""
    
    def __init__(self):
        self.event_bus = None
    
    def _get_event_bus(self):
        """Get event bus instance."""
        if self.event_bus is None:
            self.event_bus = get_event_bus()
        return self.event_bus
    
    async def get_events_by_type(
        self,
        event_type: EventType,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[Event]:
        """
        Get events by type within a time range.
        
        Args:
            event_type: Type of events to retrieve
            start_time: Start time for filtering (optional)
            end_time: End time for filtering (optional)
            limit: Maximum number of events to return
            
        Returns:
            List of events
        """
        try:
            event_bus = self._get_event_bus()
            stream_name = f"{event_bus.stream_prefix}:{event_type.value}"
            
            # Use Redis XRANGE to get events from stream
            if start_time:
                start_id = f"{int(start_time.timestamp() * 1000)}-0"
            else:
                start_id = "-"
            
            if end_time:
                end_id = f"{int(end_time.timestamp() * 1000)}-0"
            else:
                end_id = "+"
            
            messages = await event_bus._redis.xrange(
                stream_name,
                min=start_id,
                max=end_id,
                count=limit
            )
            
            events = []
            for msg_id, fields in messages:
                try:
                    event_data = json.loads(fields["event"])
                    event = Event.from_dict(event_data)
                    events.append(event)
                except Exception as e:
                    logger.error(f"Error parsing event {msg_id}: {e}")
            
            # logger.info(f"Retrieved {len(events)} events of type {event_type}")
            return events
            
        except Exception as e:
            logger.error(f"Error retrieving events by type {event_type}: {e}")
            return []
    
    async def get_events_by_organization(
        self,
        organization_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[Event]:
        """
        Get events for a specific organization.
        
        Args:
            organization_id: Organization ID to filter by
            start_time: Start time for filtering (optional)
            end_time: End time for filtering (optional)
            limit: Maximum number of events to return
            
        Returns:
            List of events
        """
        try:
            all_events = []
            
            # Get events from all event types
            for event_type in EventType:
                events = await self.get_events_by_type(
                    event_type, start_time, end_time, limit
                )
                
                # Filter by organization
                org_events = [
                    event for event in events
                    if event.metadata.organization_id == organization_id
                ]
                all_events.extend(org_events)
            
            # Sort by timestamp
            all_events.sort(key=lambda e: e.timestamp)
            
            # Apply limit
            if len(all_events) > limit:
                all_events = all_events[-limit:]
            
            # logger.info(f"Retrieved {len(all_events)} events for organization {organization_id}")
            return all_events
            
        except Exception as e:
            logger.error(f"Error retrieving events for organization {organization_id}: {e}")
            return []
    
    async def get_events_by_user(
        self,
        user_id: str,
        organization_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[Event]:
        """
        Get events for a specific user.
        
        Args:
            user_id: User ID to filter by
            organization_id: Organization ID to filter by
            start_time: Start time for filtering (optional)
            end_time: End time for filtering (optional)
            limit: Maximum number of events to return
            
        Returns:
            List of events
        """
        try:
            org_events = await self.get_events_by_organization(
                organization_id, start_time, end_time, limit * 2  # Get more to filter
            )
            
            # Filter by user
            user_events = [
                event for event in org_events
                if event.metadata.user_id == user_id
            ]
            
            # Apply limit
            if len(user_events) > limit:
                user_events = user_events[-limit:]
            
            # logger.info(f"Retrieved {len(user_events)} events for user {user_id}")
            return user_events
            
        except Exception as e:
            logger.error(f"Error retrieving events for user {user_id}: {e}")
            return []
    
    async def replay_events(
        self,
        event_type: EventType,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        target_handler: Optional[str] = None,
    ) -> int:
        """
        Replay events of a specific type.
        
        Args:
            event_type: Type of events to replay
            start_time: Start time for filtering (optional)
            end_time: End time for filtering (optional)
            target_handler: Specific handler to replay to (optional)
            
        Returns:
            Number of events replayed
        """
        try:
            events = await self.get_events_by_type(event_type, start_time, end_time)
            
            replayed_count = 0
            for event in events:
                try:
                    # Re-publish the event for replay
                    event_bus = self._get_event_bus()
                    
                    # Mark as replay in metadata
                    event.metadata.retry_count += 1
                    
                    # Publish to pub/sub only (not streams to avoid duplication)
                    pubsub_channel = f"{event_bus.pubsub_prefix}:{event.type.value}"
                    await event_bus._redis.publish(pubsub_channel, event.json())
                    
                    replayed_count += 1
                    # logger.info(f"Replayed event {event.id} of type {event_type}")
                    
                except Exception as e:
                    logger.error(f"Error replaying event {event.id}: {e}")
            
            # logger.info(f"Replayed {replayed_count} events of type {event_type}")
            return replayed_count
            
        except Exception as e:
            logger.error(f"Error replaying events of type {event_type}: {e}")
            return 0
    
    async def get_event_statistics(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """
        Get event statistics for monitoring and analytics.
        
        Args:
            start_time: Start time for filtering (optional)
            end_time: End time for filtering (optional)
            
        Returns:
            Dictionary with event statistics
        """
        try:
            stats = {
                "total_events": 0,
                "events_by_type": {},
                "events_by_service": {},
                "time_range": {
                    "start": start_time.isoformat() if start_time else None,
                    "end": end_time.isoformat() if end_time else None,
                }
            }
            
            # Get events for each type
            for event_type in EventType:
                events = await self.get_events_by_type(event_type, start_time, end_time)
                
                if events:
                    stats["events_by_type"][event_type.value] = len(events)
                    stats["total_events"] += len(events)
                    
                    # Count by source service
                    for event in events:
                        service = event.metadata.source_service
                        if service not in stats["events_by_service"]:
                            stats["events_by_service"][service] = 0
                        stats["events_by_service"][service] += 1
            
            # logger.info(f"Generated event statistics: {stats['total_events']} total events")
            return stats
            
        except Exception as e:
            logger.error(f"Error generating event statistics: {e}")
            return {
                "total_events": 0,
                "events_by_type": {},
                "events_by_service": {},
                "error": str(e)
            }
    
    async def cleanup_old_events(self, retention_days: int = 30) -> int:
        """
        Clean up old events from streams.
        
        Args:
            retention_days: Number of days to retain events
            
        Returns:
            Number of events cleaned up
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=retention_days)
            cutoff_timestamp = int(cutoff_time.timestamp() * 1000)
            
            event_bus = self._get_event_bus()
            cleaned_count = 0
            
            # Clean up each event type stream
            for event_type in EventType:
                stream_name = f"{event_bus.stream_prefix}:{event_type.value}"
                
                try:
                    # Use XTRIM to remove old events
                    result = await event_bus._redis.xtrim(
                        stream_name,
                        minid=f"{cutoff_timestamp}-0",
                        approximate=True
                    )
                    cleaned_count += result
                    # logger.info(f"Cleaned {result} old events from stream {stream_name}")
                    
                except Exception as e:
                    logger.error(f"Error cleaning stream {stream_name}: {e}")
            
            # logger.info(f"Cleaned up {cleaned_count} old events (older than {retention_days} days)")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old events: {e}")
            return 0


# Global event store instance
_event_store: Optional[EventStore] = None


def get_event_store() -> EventStore:
    """Get the global event store instance."""
    global _event_store
    if _event_store is None:
        _event_store = EventStore()
    return _event_store
