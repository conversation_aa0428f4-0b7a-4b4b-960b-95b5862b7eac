"""
Event bus implementation using Redis Streams and Pub/Sub.
"""

import asyncio
import json
from typing import Any, Callable, Dict, List, Optional, Union

import redis.asyncio as redis
from pydantic import ValidationError

from .exceptions import (
    EventBusConnectionError,
    EventPublishError,
    EventSerializationError,
    EventSubscribeError,
    EventValidationError,
)
from .models import Event, EventMetadata, EventType
from app.utils.logger import get_logger

logger = get_logger(__name__)


class EventBus:
    """
    Event bus implementation using Redis Streams for persistence and Pub/Sub for real-time delivery.
    """

    def __init__(
        self,
        redis_url: str,
        service_name: str,
        stream_prefix: str = "events",
        pubsub_prefix: str = "events_realtime",
        max_stream_length: int = 10000,
        consumer_group: Optional[str] = None,
    ):
        self.redis_url = redis_url
        self.service_name = service_name
        self.stream_prefix = stream_prefix
        self.pubsub_prefix = pubsub_prefix
        self.max_stream_length = max_stream_length
        self.consumer_group = consumer_group or f"{service_name}_group"

        self._redis: Optional[redis.Redis] = None
        self._pubsub: Optional[redis.client.PubSub] = None
        self._subscribers: Dict[str, List[Callable]] = {}
        self._running = False

    async def connect(self) -> None:
        """Connect to Redis."""
        try:
            self._redis = redis.from_url(self.redis_url, decode_responses=True)
            await self._redis.ping()
            # logger.info(f"Connected to Redis at {self.redis_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise EventBusConnectionError(f"Failed to connect to Redis: {e}")

    async def disconnect(self) -> None:
        """Disconnect from Redis."""
        self._running = False

        if self._pubsub:
            await self._pubsub.close()

        if self._redis:
            await self._redis.close()

        # logger.info("Disconnected from Redis")

    async def publish(
        self,
        event_type: Union[EventType, str],
        data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        organization_id: Optional[str] = None,
    ) -> str:
        """
        Publish an event to both Redis Streams (for persistence) and Pub/Sub (for real-time delivery).

        Returns:
            str: The event ID
        """
        if not self._redis:
            raise EventBusConnectionError("Not connected to Redis")

        try:
            # Create event metadata
            event_metadata = EventMetadata(
                source_service=self.service_name,
                correlation_id=correlation_id,
                user_id=user_id,
                organization_id=organization_id,
                **(metadata or {})
            )

            # Create event
            event = Event(
                type=event_type if isinstance(event_type, EventType) else EventType(event_type),
                data=data,
                metadata=event_metadata,
                version="1.0",
            )

            # Serialize event
            event_json = json.dumps(event.to_dict())

            # Publish to Redis Stream for persistence
            stream_name = f"{self.stream_prefix}:{event.type.value}"
            stream_id = await self._redis.xadd(
                stream_name,
                {"event": event_json},
                maxlen=self.max_stream_length,
                approximate=True,
            )

            # Publish to Redis Pub/Sub for real-time delivery
            pubsub_channel = f"{self.pubsub_prefix}:{event.type.value}"
            await self._redis.publish(pubsub_channel, event_json)

            # logger.info(f"Published event {event.id} of type {event.type} to stream {stream_name}")
            return event.id

        except ValidationError as e:
            logger.error(f"Event validation failed: {e}")
            raise EventValidationError(f"Event validation failed: {e}")
        except (TypeError, ValueError) as e:
            logger.error(f"Event serialization failed: {e}")
            raise EventSerializationError(f"Event serialization failed: {e}")
        except Exception as e:
            logger.error(f"Failed to publish event: {e}")
            raise EventPublishError(f"Failed to publish event: {e}")

    async def subscribe(
        self,
        event_types: Union[EventType, str, List[Union[EventType, str]]],
        handler: Optional[Callable] = None,
        use_streams: bool = True,
        use_pubsub: bool = True,
    ) -> None:
        """
        Subscribe to events of specific types.

        Args:
            event_types: Event type(s) to subscribe to
            handler: Optional custom handler function
            use_streams: Whether to consume from Redis Streams
            use_pubsub: Whether to consume from Redis Pub/Sub
        """
        if not self._redis:
            raise EventBusConnectionError("Not connected to Redis")

        # Normalize event types to list
        if isinstance(event_types, (EventType, str)):
            event_types = [event_types]

        event_type_strs = [
            et.value if isinstance(et, EventType) else et
            for et in event_types
        ]

        try:
            if use_streams:
                await self._setup_stream_consumers(event_type_strs, handler)

            if use_pubsub:
                await self._setup_pubsub_consumers(event_type_strs, handler)

        except Exception as e:
            logger.error(f"Failed to subscribe to events: {e}")
            raise EventSubscribeError(f"Failed to subscribe to events: {e}")

    async def _setup_stream_consumers(
        self,
        event_types: List[str],
        handler: Optional[Callable] = None,
    ) -> None:
        """Set up Redis Streams consumers for event types."""
        for event_type in event_types:
            stream_name = f"{self.stream_prefix}:{event_type}"

            # Create consumer group if it doesn't exist
            try:
                await self._redis.xgroup_create(
                    stream_name,
                    self.consumer_group,
                    id="0",
                    mkstream=True,
                )
                # logger.info(f"Created consumer group {self.consumer_group} for stream {stream_name}")
            except redis.ResponseError as e:
                if "BUSYGROUP" not in str(e):
                    raise
                # Consumer group already exists
                logger.debug(f"Consumer group {self.consumer_group} already exists for stream {stream_name}")

            # Start consuming from stream
            asyncio.create_task(
                self._consume_from_stream(stream_name, handler)
            )

    async def _setup_pubsub_consumers(
        self,
        event_types: List[str],
        handler: Optional[Callable] = None,
    ) -> None:
        """Set up Redis Pub/Sub consumers for event types."""
        if not self._pubsub:
            self._pubsub = self._redis.pubsub()

        # Subscribe to channels
        channels = [f"{self.pubsub_prefix}:{event_type}" for event_type in event_types]
        await self._pubsub.subscribe(*channels)

        # Start consuming from pub/sub
        asyncio.create_task(
            self._consume_from_pubsub(handler)
        )

    async def _consume_from_stream(
        self,
        stream_name: str,
        handler: Optional[Callable] = None,
    ) -> None:
        """Consume events from a Redis Stream."""
        consumer_name = f"{self.service_name}_{id(asyncio.current_task())}"

        while self._running:
            try:
                # Read from stream
                messages = await self._redis.xreadgroup(
                    self.consumer_group,
                    consumer_name,
                    {stream_name: ">"},
                    count=10,
                    block=1000,  # 1 second timeout
                )

                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        try:
                            event_data = json.loads(fields["event"])
                            event = Event.from_dict(event_data)

                            # Handle event
                            if handler:
                                await handler(event)
                            else:
                                # Import here to avoid circular imports
                                from .handlers import handle_event
                                await handle_event(event)

                            # Acknowledge message
                            await self._redis.xack(stream_name, self.consumer_group, msg_id)

                        except Exception as e:
                            logger.error(f"Error processing stream message {msg_id}: {e}")
                            # TODO: Implement dead letter queue

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error consuming from stream {stream_name}: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _consume_from_pubsub(self, handler: Optional[Callable] = None) -> None:
        """Consume events from Redis Pub/Sub."""
        async for message in self._pubsub.listen():
            if message["type"] == "message":
                try:
                    event_data = json.loads(message["data"])
                    event = Event.from_dict(event_data)

                    # Handle event
                    if handler:
                        await handler(event)
                    else:
                        # Import here to avoid circular imports
                        from .handlers import handle_event
                        await handle_event(event)

                except Exception as e:
                    logger.error(f"Error processing pub/sub message: {e}")

    async def start(self) -> None:
        """Start the event bus."""
        if not self._redis:
            await self.connect()

        self._running = True
        # logger.info(f"Event bus started for service {self.service_name}")

    async def stop(self) -> None:
        """Stop the event bus."""
        self._running = False
        await self.disconnect()
        # logger.info(f"Event bus stopped for service {self.service_name}")


# Global event bus instance
_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """Get the global event bus instance."""
    if _event_bus is None:
        raise EventBusConnectionError("Event bus not initialized. Call init_event_bus() first.")
    return _event_bus


def init_event_bus(
    redis_url: str,
    service_name: str,
    **kwargs
) -> EventBus:
    """Initialize the global event bus instance."""
    global _event_bus
    _event_bus = EventBus(redis_url, service_name, **kwargs)
    return _event_bus
