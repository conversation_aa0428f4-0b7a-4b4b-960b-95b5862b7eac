import redis.asyncio as redis
from app.core.settings import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class RedisService:
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.redis = None

    async def connect(self):
        """Establish a connection to the Redis server."""
        if self.redis:
            return  # Avoid reconnecting if already connected
        try:
            self.redis = redis.Redis.from_url(self.redis_url, decode_responses=True)
            await self.redis.ping()
            # logger.info("Connected to Redis successfully.")
            return self.redis
        except Exception as e:
            self.redis = None
            logger.error(f"Failed to connect to Redis: {e}")
            raise e

    async def publish(self, channel: str, message: str):
        """Publish a message to a Redis channel."""
        if not self.redis:
            logger.warning("Redis connection is not established. Reconnecting...")
            await self.connect()
        await self.redis.publish(channel, message)

    async def subscribe(self, channel: str):
        """Subscribe to a Redis channel and return the PubSub instance."""
        if not self.redis:
            raise ConnectionError("Redis connection is not established.")
        pubsub = self.redis.pubsub()
        await pubsub.subscribe(channel)
        return pubsub

    async def close(self):
        """Close the Redis connection."""
        if self.redis:
            await self.redis.close()
            # logger.info("Redis connection closed.")


redis_service = RedisService(settings.REDIS_URL)
