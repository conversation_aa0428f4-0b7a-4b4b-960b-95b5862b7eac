import enum
import json
from sqlite3 import OperationalError

import httpx
from app.core.settings import settings
from app.models.model import Plan, Subscription
from app.schemas.subscription import PlanBase, PlanUpdate, User
from app.utils.logger import get_logger
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

logger = get_logger(__name__)


headers = {
    "Authorization": f"Bearer {settings.PAYSTACK_SECRET}",
    "Content-Type": "application/json",
}


timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)


# this should be accessible only by superadmin
async def create_plan(plan_details: PlanBase, db_session: Session):
    """Creates a new plan"""
    try:
        # check if plan do not already exists
        existing_plan = db_session.query(Plan).filter_by(name=plan_details.name).first()
        if existing_plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Plan already exists"
            )
        # call the paystack endpoint to create the plan
        # Exclude 'features' from the data sent to Paystack
        plan_data = json.loads(plan_details.json())
        plan_data.pop("features", None)

        # # logger.info(plan_data)

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{settings.PAYSTACK_BASE_URL}/plan", json=plan_data, headers=headers
            )
            response.raise_for_status()
            # # logger.info(response.json())
            if response.is_success:
                plan_data = response.json()
            # save to database
            db_plan = Plan(
                name=plan_data["data"]["name"],
                interval=plan_data["data"]["interval"],
                description=plan_data["data"]["description"],
                features=plan_details.features,
                currency=plan_data["data"]["currency"],
                amount=plan_data["data"]["amount"],
                plan_code=plan_data["data"]["plan_code"],
                created_at=plan_data["data"]["createdAt"],
                updated_at=plan_data["data"]["updatedAt"],
            )
            db_session.add(db_plan)
            db_session.commit()
            db_session.refresh(db_plan)
            return db_plan
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 400:
            # Log the JSON response or text for debugging
            logger.error(
                "400 Bad Request Error: %s", e.response.json()
            )  # JSON response details
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.json()
            )
        else:
            logger.error(f"Request failed: {e}")
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.json()
            )
    except HTTPException as e:
        raise e
    except OperationalError as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"A database error occurred: {str(e)}",
        )
    except httpx.RequestError as e:
        logger.error(f"Request to Paystack failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail="Failed to create plan with Paystack",
        )
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


# this endpoint is not locked
async def get_plans(db_session: Session):
    """Returns all the plans available"""
    try:
        # plans = db_session.query(Plan).all()
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.PAYSTACK_BASE_URL}/plan", headers=headers
            )
            response.raise_for_status()
            # # logger.info(response.json())
            if response.is_success:
                plan_data = response.json()
                # message = plan_data['message']

            response = [
                {
                    "plan_code": plans.get("plan_code"),
                    "id": plans.get("id"),
                    "name": plans.get("name"),
                    "amount": plans.get("amount"),
                    "currency": plans.get("currency"),
                    "interval": plans.get("interval"),
                    "description": plans.get("description"),
                }
                if plans["is_deleted"] is False
                else ""
                for plans in plan_data["data"]
            ]
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


async def get_plan(plan_code: str, db_session: Session):
    """Retrieves a single plan detail"""
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.PAYSTACK_BASE_URL}/plan/{plan_code}", headers=headers
            )
            response.raise_for_status()
            # # logger.info(response.json())
            if response.is_success:
                db_plan = response.json()
        return db_plan["data"]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"{str(e)}"
        )


async def get_dbplans(db_session: Session):
    """Returns all the plans available"""
    try:
        plans = db_session.query(Plan).all()
        return plans
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


async def get_dbplan(plan_code: str, db_session: Session):
    """Retrieves a single plan detail"""
    try:
        db_plan = db_session.query(Plan).filter_by(plan_code=plan_code).first()
        if not db_plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Requested plan not found"
            )
        return db_plan
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"{str(e)}"
        )


async def update_plan(plan_code: str, plan_details: PlanUpdate, db_session: Session):
    """Updates a plan"""
    try:
        # check if plan do not already exists
        existing_plan = db_session.query(Plan).filter_by(plan_code=plan_code).first()
        if not existing_plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Requested Plan doesn't exist",
            )
        # call the paystack endpoint to create the plan
        # Exclude 'features' from the data sent to Paystack
        plan_data = plan_details.dict_without_none()

        # Convert enums to their value
        for key, value in plan_data.items():
            if isinstance(value, enum.Enum):
                plan_data[key] = value.value

        plan_data.pop("features", None)
        # # logger.info(type(plan_data))
        plan_data = json.dumps(plan_data)
        # logger.info(plan_data)

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.put(
                f"{settings.PAYSTACK_BASE_URL}/plan/{plan_code}",
                data=plan_data,
                headers=headers,
            )
            response.raise_for_status()
            # # logger.info(response.json())
            if response.is_success:
                response = response.json()
                message = response["message"]

            # Edit the resource saved to database
            plan_data = json.loads(plan_data)
            existing_plan.name = (
                plan_data.get("name") if plan_data.get("name") else existing_plan.name
            )

            existing_plan.interval = (
                plan_data.get("interval")
                if plan_data.get("interval")
                else existing_plan.interval
            )

            existing_plan.description = (
                plan_data.get("description")
                if plan_data.get("description")
                else existing_plan.description
            )

            existing_plan.features = (
                plan_details.features
                if plan_details.features
                else existing_plan.features
            )

            existing_plan.currency = (
                plan_data.get("currency")
                if plan_data.get("currency")
                else existing_plan.currency
            )

            existing_plan.amount = (
                plan_data.get("amount")
                if plan_data.get("amount")
                else existing_plan.amount
            )

            db_session.commit()
            db_session.refresh(existing_plan)
            return existing_plan, message
    except httpx.HTTPStatusError as e:
        if e.response.status_code:
            # Log the JSON response or text for debugging
            logger.error("Error: %s", e.response.json())  # JSON response details
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.json()
            )
        else:
            logger.error(f"Request failed: {e}")
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.json()
            )
    except HTTPException as e:
        raise e
    except OperationalError as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"A database error occurred: {str(e)}",
        )
    except httpx.RequestError as e:
        logger.error(f"Request to Paystack failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail="Failed to update plan with Paystack",
        )
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


async def initializeTransaction(
    plan_code: str, user_details: User, db_session: Session
):
    """subscribes a user to a plan
    This is for first timers
    """
    try:
        # check if the plan exists
        plan_data = await get_dbplan(plan_code, db_session)
        if not plan_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The requested plan can not be found",
            )
        # create the data to be added
        data = {
            "amount": 100,
            "email": user_details.email,
            "callback_url": f"{settings.PAYSTACK_CALLBACK_API_URL}",  # this would be a frontend url..
            "plan": plan_code,
        }

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{settings.PAYSTACK_BASE_URL}/transaction/initialize",
                json=data,
                headers=headers,
            )
            response.raise_for_status()
            response_data = response.json()

            # logger.info(response_data)

            # save to database for the user
            db_subscription = Subscription(
                reference_number=response_data["data"]["reference"],
                access_code=response_data["data"]["access_code"],
                user_id=user_details.email,
                status=False,
                plan_id=plan_data.id,
            )
            db_session.add(db_subscription)
            db_session.commit()
            db_session.refresh(db_subscription)
            return {
                "message": "Transaction initialized successfully",
                "data": {
                    "authorization_url": response_data["data"]["authorization_url"],
                    "access_code": response_data["data"]["access_code"],
                    "reference": response_data["data"]["reference"],
                },
            }

    except httpx.HTTPStatusError as e:
        if e.response.status_code:
            logger.error("HTTP Error occurred: %s", e.response.json())
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.json()
            )
    except HTTPException as e:
        raise e
    except OperationalError as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"A database error occurred: {str(e)}",
        )
    except httpx.RequestError as e:
        logger.error(f"Request to Paystack failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail="Failed to connect with Paystack",
        )
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


async def verifyTransaction(reference_token: str, db_session: Session):
    """Verify a transaction with Paystack."""
    try:
        # Create a Paystack client
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.PAYSTACK_BASE_URL}/transaction/verify/{reference_token}",
                headers=headers,
            )
            response.raise_for_status()
            response_data = response.json()
            # logger.info(response_data)
            if response_data["data"]["status"] == "success":
                # Update the subscription status in the database
                db_subscription = (
                    db_session.query(Subscription)
                    .filter(Subscription.reference_number == reference_token)
                    .first()
                )
                db_subscription.status = True
                db_session.commit()
                return {
                    "message": "Subscription successful",
                    "data": response_data["data"],
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Transaction verification failed",
                )
    except httpx.HTTPStatusError as e:
        # Log the JSON response or text for debugging
        logger.error("Error: %s", e.response.json())  # JSON response details
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.json()
        )
    except HTTPException as e:
        raise e
    except OperationalError as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"A database error occurred: {str(e)}",
        )
    except httpx.RequestError as e:
        logger.error(f"Request to Paystack failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail="Failed to communicate with Paystack",
        )
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )
