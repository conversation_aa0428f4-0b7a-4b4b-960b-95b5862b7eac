from app.core.settings import settings
from app.utils.logger import get_logger
from fastapi import HTT<PERSON>Exception
from fastapi_mail import <PERSON><PERSON>onfig, FastMail, MessageSchema
from jinja2 import Template  # Keep jinja2 for templating

logger = get_logger(__name__)

conf = ConnectionConfig(
    MAIL_USERNAME=settings.MAIL_USERNAME,
    MAIL_PASSWORD=settings.MAIL_PASSWORD,
    MAIL_FROM=settings.MAIL_FROM,
    MAIL_PORT=settings.MAIL_PORT,
    MAIL_SERVER=settings.MAIL_SERVER,
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True
    # SUPPRESS_SEND=True
)


class EmailHandler:
    def __init__(self, config):
        self.config = config
        self.fm = FastMail(self.config)

    async def send_email(
        self, recipient: str, subject: str, template: str, context: dict
    ):
        try:
            with open(template) as f:
                template_content = Template(f.read())
            body = template_content.render(context)

            message = MessageSchema(
                subject=subject, recipients=[recipient], body=body, subtype="html"
            )
            # check the env
            if settings.ENV == "DEVELOPMENT":
                print("Development Email Handler")
                # logger.info(f"Email sent to {message.recipients}")
                # logger.info(f"Subject: {message.subject}")
                # logger.info(f"Body: {message.body}")
            else:
                # logger.info(f"{settings.ENV} email sent to {message.recipients}")
                await self.fm.send_message(message)
        except FileNotFoundError as e:
            logger.error(f"Email template not found: {e}")
            raise HTTPException(status_code=500, detail="Email template not found")
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            raise HTTPException(status_code=500, detail="Error sending email")


email_service = EmailHandler(conf)
