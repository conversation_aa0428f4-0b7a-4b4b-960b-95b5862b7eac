from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnablePassthrough
from langgraph.graph import StateGraph, END
from typing import List, Dict, Any, Optional, TypedDict
from app.utils.logger import get_logger
from app.models.schemas import MessageRole
import json

logger = get_logger(__name__)


class AgentState(TypedDict):
    """State management for LangGraph agents."""
    messages: List[Any]
    agent_instructions: str
    capabilities: List[str]
    context: Dict[str, Any]


class LangChainService:
    """Service for LangChain and LangGraph integration."""

    def __init__(self):
        """Initialize LangChain service."""
        self.llm = None
        # logger.info("LangChain service initialized")

    def _get_llm(self):
        """Lazy initialization of LLM."""
        if self.llm is None:
            from app.services.gemini_service import gemini_service
            self.llm = gemini_service.get_chat_model()
        return self.llm
    
    def create_agent_prompt(self, instructions: str, personality: Optional[str] = None) -> ChatPromptTemplate:
        """Create a chat prompt template for an agent."""
        system_message = f"""
        You are a specialized AI assistant with the following instructions:
        
        {instructions}
        
        {f"Personality and Communication Style: {personality}" if personality else ""}
        
        Always be helpful, accurate, and follow your specific instructions. 
        If a request is outside your capabilities, politely explain your limitations.
        """
        
        return ChatPromptTemplate.from_messages([
            ("system", system_message),
            MessagesPlaceholder(variable_name="messages"),
        ])
    
    def create_agent_chain(self, instructions: str, personality: Optional[str] = None):
        """Create a LangChain chain for an agent."""
        prompt = self.create_agent_prompt(instructions, personality)
        chain = prompt | self._get_llm()
        return chain
    
    async def process_message(
        self, 
        message: str, 
        instructions: str, 
        conversation_history: List[Dict[str, str]] = None,
        personality: Optional[str] = None,
        capabilities: List[str] = None
    ) -> str:
        """Process a message through the agent chain."""
        try:
            # Create the agent chain
            chain = self.create_agent_chain(instructions, personality)
            
            # Prepare messages
            messages = []
            
            # Add conversation history
            if conversation_history:
                for msg in conversation_history[-10:]:  # Keep last 10 messages for context
                    if msg["role"] == MessageRole.USER:
                        messages.append(HumanMessage(content=msg["content"]))
                    elif msg["role"] == MessageRole.ASSISTANT:
                        messages.append(AIMessage(content=msg["content"]))
            
            # Add current message
            messages.append(HumanMessage(content=message))
            
            # Invoke the chain
            response = await chain.ainvoke({"messages": messages})
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            raise
    
    def create_langgraph_agent(self, instructions: str, capabilities: List[str] = None):
        """Create a LangGraph agent with tools and capabilities."""
        
        def agent_node(state: AgentState):
            """Main agent processing node."""
            try:
                # Get the last message
                last_message = state.messages[-1] if state.messages else None
                if not last_message:
                    return {"messages": [AIMessage(content="Hello! How can I help you today?")]}
                
                # Create prompt with instructions
                prompt = self.create_agent_prompt(state.agent_instructions)
                chain = prompt | self._get_llm()
                
                # Process the message
                response = chain.invoke({"messages": state.messages})
                
                return {"messages": [response]}
                
            except Exception as e:
                logger.error(f"Error in agent node: {e}")
                return {"messages": [AIMessage(content="I apologize, but I encountered an error processing your request.")]}
        
        # Create the graph
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("agent", agent_node)
        
        # Set entry point
        workflow.set_entry_point("agent")
        
        # Add edge to end
        workflow.add_edge("agent", END)
        
        # Compile the graph
        app = workflow.compile()
        
        return app
    
    async def run_agent_workflow(
        self, 
        message: str, 
        instructions: str, 
        conversation_history: List[Dict[str, str]] = None,
        capabilities: List[str] = None
    ) -> str:
        """Run an agent workflow using LangGraph."""
        try:
            # Create agent
            agent = self.create_langgraph_agent(instructions, capabilities)
            
            # Prepare initial state
            messages = []

            # Add conversation history
            if conversation_history:
                for msg in conversation_history[-10:]:
                    if msg["role"] == MessageRole.USER:
                        messages.append(HumanMessage(content=msg["content"]))
                    elif msg["role"] == MessageRole.ASSISTANT:
                        messages.append(AIMessage(content=msg["content"]))

            # Add current message
            messages.append(HumanMessage(content=message))

            initial_state = AgentState(
                messages=messages,
                agent_instructions=instructions,
                capabilities=capabilities or [],
                context={}
            )
            
            # Run the workflow
            result = await agent.ainvoke(initial_state)
            
            # Extract response
            if result.get("messages"):
                last_message = result["messages"][-1]
                return last_message.content
            
            return "I apologize, but I couldn't generate a response."
            
        except Exception as e:
            logger.error(f"Error running agent workflow: {e}")
            raise


# Global instance
langchain_service = LangChainService()
