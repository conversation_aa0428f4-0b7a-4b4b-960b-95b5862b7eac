import google.generativeai as genai
from langchain_google_genai import ChatGoogleGenerativeAI
from app.core.config import settings
from app.utils.logger import get_logger
from typing import Optional, Dict, Any

logger = get_logger(__name__)


class GeminiService:
    """Service for Google Gemini AI integration."""
    
    def __init__(self):
        """Initialize Gemini service."""
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=settings.GEMINI_API_KEY,
            temperature=0.7,
            max_tokens=4096,
        )
        # logger.info("Gemini service initialized")
    
    def get_chat_model(self, temperature: float = 0.7, max_tokens: int = 4096) -> ChatGoogleGenerativeAI:
        """Get a configured Gemini chat model."""
        return ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=settings.GEMINI_API_KEY,
            temperature=temperature,
            max_tokens=max_tokens,
        )
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate a response using Gemini."""
        try:
            response = await self.model.ainvoke(prompt)
            return response.content
        except Exception as e:
            logger.error(f"Error generating Gemini response: {e}")
            raise
    
    async def generate_agent_instructions(self, description: str, personality: Optional[str] = None) -> str:
        """Generate detailed instructions for a custom agent based on description."""
        prompt = f"""
        Create detailed instructions for an AI agent based on the following description:
        
        Description: {description}
        {f"Personality: {personality}" if personality else ""}
        
        Generate comprehensive instructions that include:
        1. The agent's role and purpose
        2. How it should communicate and behave
        3. Specific guidelines for handling user requests
        4. Any limitations or boundaries
        
        Make the instructions clear, actionable, and specific to the described agent.
        """
        
        try:
            instructions = await self.generate_response(prompt)
            return instructions
        except Exception as e:
            logger.error(f"Error generating agent instructions: {e}")
            raise


# Global instance
gemini_service = GeminiService()
