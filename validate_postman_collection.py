#!/usr/bin/env python3
"""
Validation script for the EllumAI Postman collection.

This script validates the Postman collection JSON file to ensure:
- Valid JSON structure
- All required fields are present
- Variable references are properly defined
- Request/response examples are complete
- No broken references or missing data
"""

import json
import re
from typing import Dict, List, Set


def load_collection(file_path: str) -> Dict:
    """Load and parse the Postman collection JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON: {e}")
        return {}
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return {}


def extract_variables(collection: Dict) -> Set[str]:
    """Extract all variable references from the collection."""
    variables = set()
    
    # Collection-level variables
    if 'variable' in collection:
        for var in collection['variable']:
            variables.add(var['key'])
    
    return variables


def find_variable_references(text: str) -> Set[str]:
    """Find all {{variable}} references in text."""
    if not isinstance(text, str):
        return set()
    
    pattern = r'\{\{([^}]+)\}\}'
    matches = re.findall(pattern, text)
    return set(matches)


def validate_requests(items: List[Dict], defined_vars: Set[str]) -> Dict:
    """Validate all requests in the collection."""
    stats = {
        'total_requests': 0,
        'requests_with_examples': 0,
        'undefined_variables': set(),
        'missing_descriptions': 0,
        'services': set(),
        'http_methods': set()
    }
    
    def process_item(item: Dict):
        if 'request' in item:
            stats['total_requests'] += 1
            request = item['request']
            
            # Check for description
            if not item.get('description'):
                stats['missing_descriptions'] += 1
            
            # Extract HTTP method
            if 'method' in request:
                stats['http_methods'].add(request['method'])
            
            # Check URL for service identification and variables
            if 'url' in request:
                url_data = request['url']
                if isinstance(url_data, dict):
                    # Extract service from port variable
                    if 'port' in url_data:
                        port_var = url_data['port']
                        if 'ServicePort' in port_var:
                            service = port_var.replace('{{', '').replace('}}', '').replace('ServicePort', '')
                            stats['services'].add(service)
                    
                    # Check for undefined variables in URL
                    for component in ['raw', 'host', 'port']:
                        if component in url_data:
                            if isinstance(url_data[component], list):
                                for item in url_data[component]:
                                    undefined = find_variable_references(item) - defined_vars
                                    stats['undefined_variables'].update(undefined)
                            else:
                                undefined = find_variable_references(url_data[component]) - defined_vars
                                stats['undefined_variables'].update(undefined)
            
            # Check for response examples
            if 'response' in item and item['response']:
                stats['requests_with_examples'] += 1
        
        # Process nested items
        if 'item' in item:
            for subitem in item['item']:
                process_item(subitem)
    
    for item in items:
        process_item(item)
    
    return stats


def validate_collection_structure(collection: Dict) -> Dict:
    """Validate the overall collection structure."""
    required_fields = ['info', 'item', 'variable']
    missing_fields = []
    
    for field in required_fields:
        if field not in collection:
            missing_fields.append(field)
    
    # Validate info section
    info_issues = []
    if 'info' in collection:
        info = collection['info']
        required_info_fields = ['name', 'description', 'version', 'schema']
        for field in required_info_fields:
            if field not in info:
                info_issues.append(field)
    
    return {
        'missing_fields': missing_fields,
        'info_issues': info_issues
    }


def main():
    """Main validation function."""
    print("🔍 Validating EllumAI Postman Collection...")
    print("=" * 50)
    
    # Load collection
    collection = load_collection('ellumAI_backend_postman_collection.json')
    if not collection:
        return
    
    print("✅ JSON structure is valid")
    
    # Validate collection structure
    structure_validation = validate_collection_structure(collection)
    if structure_validation['missing_fields']:
        print(f"❌ Missing required fields: {structure_validation['missing_fields']}")
    else:
        print("✅ Collection structure is valid")
    
    if structure_validation['info_issues']:
        print(f"⚠️  Info section issues: {structure_validation['info_issues']}")
    else:
        print("✅ Collection info is complete")
    
    # Extract defined variables
    defined_vars = extract_variables(collection)
    print(f"📋 Defined variables: {len(defined_vars)}")
    for var in sorted(defined_vars):
        print(f"   • {var}")
    
    # Validate requests
    if 'item' in collection:
        request_stats = validate_requests(collection['item'], defined_vars)
        
        print(f"\n📊 Request Statistics:")
        print(f"   • Total requests: {request_stats['total_requests']}")
        print(f"   • Requests with examples: {request_stats['requests_with_examples']}")
        print(f"   • Missing descriptions: {request_stats['missing_descriptions']}")
        print(f"   • HTTP methods: {sorted(request_stats['http_methods'])}")
        print(f"   • Services covered: {sorted(request_stats['services'])}")
        
        if request_stats['undefined_variables']:
            print(f"\n❌ Undefined variables found:")
            for var in sorted(request_stats['undefined_variables']):
                print(f"   • {{{{ {var} }}}}")
        else:
            print("\n✅ All variable references are defined")
        
        # Calculate coverage
        example_coverage = (request_stats['requests_with_examples'] / request_stats['total_requests']) * 100
        print(f"\n📈 Example Coverage: {example_coverage:.1f}%")
        
        if example_coverage >= 80:
            print("✅ Good example coverage")
        elif example_coverage >= 60:
            print("⚠️  Moderate example coverage")
        else:
            print("❌ Low example coverage")
    
    # Service coverage check
    expected_services = {'auth', 'chat', 'fastbot', 'settings', 'socials', 'deepEllum'}
    covered_services = request_stats.get('services', set())
    missing_services = expected_services - covered_services
    
    if missing_services:
        print(f"\n⚠️  Missing services: {missing_services}")
    else:
        print("\n✅ All expected services are covered")
    
    # Final summary
    print("\n" + "=" * 50)
    total_issues = len(structure_validation['missing_fields']) + len(structure_validation['info_issues']) + len(request_stats.get('undefined_variables', []))
    
    if total_issues == 0:
        print("🎉 Collection validation passed! Ready for import.")
    else:
        print(f"⚠️  Found {total_issues} issues that should be addressed.")
    
    print(f"\n📋 Collection Summary:")
    print(f"   • Services: {len(covered_services)}")
    print(f"   • Total endpoints: {request_stats['total_requests']}")
    print(f"   • Variables: {len(defined_vars)}")
    print(f"   • HTTP methods: {len(request_stats['http_methods'])}")
    print(f"   • Example coverage: {example_coverage:.1f}%")


if __name__ == "__main__":
    main()
